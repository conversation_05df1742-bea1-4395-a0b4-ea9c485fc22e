{"compilerOptions": {"noImplicitAny": false, "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "pinia-plugin-persist-uni"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue"]}