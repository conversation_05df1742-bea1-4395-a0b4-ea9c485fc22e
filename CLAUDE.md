# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a uni-app based WeChat mini-program for a fitness/sports venue membership system. It's built with Vue 3, TypeScript, and uses Vite as the build tool. The app provides functionality for members to book classes, purchase cards, manage tickets, and access various services at sports venues.

## Development Commands

### Development
- `npm run dev` - Start WeChat mini-program development (default)
- `npm run dev:h5` - Start H5 development server
- `npm run dev:mp-weixin` - Start WeChat mini-program development
- `npm run dev:mp-alipay` - Start Alipay mini-program development

### Build
- `npm run build` - Build for WeChat mini-program (default)
- `npm run build:h5` - Build for H5
- `npm run build:mp-weixin` - Build for WeChat mini-program

### Code Quality
- `npm run lint` - Run ESLint with auto-fix
- `npm run style` - Run Stylelint with auto-fix
- `npm run tsc` - Run TypeScript type checking

### Utilities
- `npm run add` - Auto-generate page from pages.json configuration
- `npm run cz` - Stage all changes and commit using commitizen

## Architecture

### State Management
- Uses Pinia for state management with persistence via `pinia-plugin-persist-uni`
- Store modules are located in `src/store/` with separate modules for app, user, merchant, theme, etc.

### Routing & Pages
- Page routing configured in `src/pages.json` following uni-app conventions
- Pages organized by feature areas:
  - `pages/` - Main application pages
  - `packageMy/` - User profile and personal features
  - `packagePt/` - Personal training related features

### API & Networking
- Centralized request handling in `src/utils/request.ts`
- Environment configuration in `src/config/env.ts` with multiple deployment environments (dev, beta, sim, wx)
- API proxy configuration in vite.config.ts for local development

### Component Structure
- Global components in `src/components/`
- Page-specific components co-located with their respective pages
- Custom component libraries for tabs, date pickers, etc.

### Key Features
- Multi-venue support with venue selection
- Class booking system (personal training and group classes)
- Membership card management
- Points/rewards system
- Payment integration
- Stadium/court booking functionality

### Build Configuration
- Vite-based build system with uni-app plugin
- Auto-import configuration for Vue/uni-app APIs
- TypeScript path aliases configured (`@/` -> `src/`)
- Development server runs on port 3000 with auto-open

### Code Standards
- ESLint with Vue 3, TypeScript, and Prettier integration
- Stylelint for CSS/SCSS linting
- Husky for git hooks with commitlint
- No semicolons style (configured in ESLint)
- Conventional commits enforced

### Testing
- No specific test framework configured - check with team for testing approach before adding tests

## Important Notes
- This is a WeChat mini-program with specific uni-app constraints
- The app supports multiple deployment environments controlled by `src/config/env.ts`
- Uses custom page generation via `auto/addPage.ts` script
- Payment functionality integrated with WeChat Pay
- Face recognition and authentication features present