@import url('~@/static/fonts/iconfont.css');
@import './base.scss';
@import './theme.scss';
$blue: #415c91;
$green: #4ed0c4;
$gray: #e7e7e7;
$red: #ca2e53;
//common
.mt20 {
  margin-top: 20rpx !important;
}
.icon {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
}
.icon-warn {
  width: 24rpx;
  height: 20rpx;
}
.icon-time {
  display: inline-block;
  width: 26rpx;
  height: 26rpx;
  vertical-align: middle;
  margin-right: 8rpx;
}
.icon-mr {
  margin-right: 10rpx;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.rich-text {
  padding-top: 32rpx;
  padding-bottom: 50rpx;
  line-height: 1.5;
  font-size: 24rpx;
  word-break: break-all;
  &.nopt {
    padding-top: 0;
  }
}
.rich-text span {
  text-decoration: line-through;
}
.rich-text-img {
  max-width: 100%;
}
.cut-up {
  margin: 0 10rpx;
}
.link-text {
  display: inline;
  color: dodgerblue;
}
// BUTTON
button {
  &.normal-btn {
    background: var(--THEME-COLOR);
    color: #000;
  }
}
.normal-btn {
  border: 0 none;
  border-radius: 0;
  width: 100%;
  height: 80rpx;
  background: var(--THEME-COLOR);
  line-height: 80rpx;
  font-weight: bold;
  font-size: 30rpx;
  color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  &.friend {
    background: $theme-text-color-other;
    color: #fff;
  }
  &.reserved {
    background-color: $green;
  }
  &.finished {
    border-color: $blue;
    background-color: #fff;
    color: $blue;
  }
  &.outer-green {
    border: 1px solid var(--THEME-COLOR);
    background-color: #fff;
    color: #1b1b1b;
  }
  &.outer-org {
    border: 1px solid $theme-text-color-other;
    background-color: #fff;
    color: #1b1b1b;
  }
  &.outer-gray {
    border: 1px solid #C6C6C6;
    background-color: #fff;
    color: #1b1b1b;
  }
  &.disabled,
  &[disabled] {
    background-color: $gray !important;
    color: #898989;
  }
  &.disabled-canclick{
    background-color: $gray !important;
    color: $theme-text-color-other;
  }
  &.transparent {
    background-color: transparent !important;
    font-weight: normal;
    font-size: 24rpx;
    color: #1b1b1b !important;
  }
}
.normal-btn-min {
  margin: 0 auto;
  border-radius: 40rpx;
  width: 300rpx;
}
.normal-btn-small {
  width: 110rpx;
  height: 36rpx;
  text-align: center;
  border-radius: 18rpx;
  font-size: 20rpx;
  font-weight: 400;
}
.gray-btn {
  width: 195rpx;
  background: #f2f2f2;
}
.buttons {
  display: flex;
  z-index: 10;
  width: 100%;
  align-items: center;
  button {
    border-radius: 0;
  }
  .mgr {
    margin-right: 14rpx;
  }
  .lef-flex {
    flex: 3;
  }
  .rig-flex {
    flex: 4;
  }
  .price-text {
    color: $theme-text-color-other;
    font-size: 30rpx;
    text {
      font-size: 48rpx;
    }
  }
}
[data-theme='dark'] {
  .normal-btn {
    &.transparent {
      color: #fff !important;
    }
    &.outer-org {
      background: #000;
      color: #fff;
    }
  }
}
//顶部标题栏
.title-bar {
  display: flex;
  position: fixed;
  z-index: 1;
  justify-content: left;
  align-items: center;
  padding-left: 32rpx;
  width: 100%;
  .nav-back {
    padding: 8rpx;
    padding-left: 0;
  }
  .title-bar-text {
    flex: 1;
    margin-right: 220rpx;
    text-align: center;
    font-size: 30rpx;
  }
}
//无数据
.nodata {
  position: relative;
  box-sizing: border-box;
  margin: 0 auto;
  padding-top: 250rpx;
  width: 100%;
  min-width: 286rpx;
  height: 100%;
  min-height: 289rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 24rpx;
}
.nodata::before {
  position: absolute;
  left: 50%;
  top: 144rpx;
  width: 103rpx;
  height: 144rpx;
  background: url('https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png') left top no-repeat;
  background-size: cover;
  content: ' ';
  transform: translate(-50%, -50%);
}
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
//表单
.form-items {
  margin: 0 20rpx 20rpx;
  padding: 0 30rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  .required-icon {
    margin-left: 10rpx;
    font-size: 26rpx;
    color: #f44;
  }
  .item {
    @include flex-center;
    justify-content: space-between;
    border-bottom: 1rpx solid #f6f6f8;
    min-height: 92rpx;
    &:last-child,&.noborder {
      border-bottom: 0;
    }
    .item-input {
      flex: 1;
      height: 92rpx;
      text-align: right;
      font-weight: bold;
      font-size: 26rpx;
      color: #313131;
    }
    .item-border-input {
      width: 93rpx;
      height: 52rpx;
      line-height: 52rpx;
      border: 2rpx solid #DEDEE0;
      margin-right: 15rpx;
      border-radius: 4rpx;
      text-align: center;
    }
    .value {
      height: 100%;
      font-weight: bold;
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    picker {
      flex: 1;
      width: 100%;
      height: 100%;
      padding-right: 20rpx;
      .rig-sel {
        height: 92rpx;
        line-height: 92rpx;
      }
    }
    .icon {
      margin-left: 15rpx;
      width: 12rpx;
      height: 6rpx;
    }
    .label-hastips {
      padding: 25rpx 0;
    }
    .label-tips {
      max-width: 390rpx;
      margin-top: 20rpx;
      font-size: 18rpx;
      color: $theme-text-color-other;
    }
    .rig-sel {
      position: relative;
      padding-right: 14rpx;
      &::after {
        position: absolute;
        right: -10rpx;
        top: 50%;
        margin-top: -5rpx;
        border: 8rpx solid transparent;
        border-top: 8rpx solid $theme-text-color-other;
        width: 0;
        height: 0;
        content: ' ';
      }
    }
    .rig-text {
      display: inline-block;
      padding: 18rpx;
      padding-right: 0;
      font-size: 24rpx;
      color: $theme-text-color-other;
    }
  }
  .des {
    margin-bottom: 30rpx;
    line-height: 36rpx;
    font-size: 24rpx;
  }
  .des-in {
    margin-bottom: 30rpx;
    padding-bottom: 30rpx;
    border-bottom: 1rpx solid #f6f6f8;
    input {
      display: inline-block;
      margin: 0 16rpx;
      border: 1rpx solid var(--THEME-COLOR);
      border-radius: 10rpx;
      width: 100rpx;
      height: 60rpx;
      min-height: auto;
      line-height: 60px;
      vertical-align: middle;
      text-align: center;
    }
  }
  .value-overtext {
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
[data-theme='dark'] {
  .form-items {
    .item-input {
      color: #fff !important;
    }
  }
}
.box-tit {
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
  padding-left: 20rpx;
  line-height: 100rpx;
  font-weight: bold;
  font-size: 36rpx;
}
.box-tit .rig {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
}
.box-tit::before {
  position: absolute;
  left: 0;
  top: 50%;
  border-radius: 3rpx;
  width: 6rpx;
  height: 32rpx;
  background: var(--THEME-COLOR);
  content: ' ';
  transform: translateY(-50%);
}
.arrow-right {
  border-right: 2px solid #000;
  border-top: 2px solid #000;
  width: 10rpx;
  height: 10rpx;
  transform: rotate(45deg);
}
.fixed-bottom-wrap,
.zpaging-bottom-wrap {
  display: flex;
  z-index: 98;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  width: 100%;
  min-height: 120rpx;
  box-shadow: 0 0 6rpx 0 rgba(27, 27, 27, 0.1);
  // box-shadow: 0 -6rpx 6rpx 0 rgba(27, 27, 27, 0.05);
}
.fixed-bottom-wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
}
.fixed-protocol-col {
  display: flex;
  position: fixed;
  left: 0;
  bottom: 120rpx;
  bottom: calc(env(safe-area-inset-bottom) + 120rpx);
  padding-left: 30rpx;
  width: 100%;
  min-height: 60rpx;
  box-sizing: border-box;
  background: #fff;
  line-height: 1.75;
  font-size: 24rpx;
  color: $theme-text-color-grey;
  align-items: center;
  navigator {
    display: inline-block;
  }
}
.footer-hasfixed {
  padding-bottom: 178rpx !important;
}
.hastabbar {
  padding-bottom: 48px;
}

//状态角标
.status-text {
  position: absolute;
  right: -50rpx;
  top: -30rpx;
  width: 120rpx;
  height: 80rpx;
  background: #d6d6d6;
  line-height: 120rpx;
  text-align: center;
  font-weight: bold;
  font-size: 18rpx;
  color: #000;
  transform: rotate(45deg);
  &.theme-status {
    background: var(--THEME-COLOR);
  }
  &.org-bg {
    background: $theme-text-color-other;
  }
  &.org-text {
    color: $theme-text-color-other;
  }
}

//卡信息
.card-info {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  padding: 26rpx 32rpx 0;
  border-radius: 20rpx;
  height: 270rpx;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/ms-card-bg.png');
  background-color: #fff;
  background-size: 100% auto;
  color: #03080e;
  &.excard {
    background-image: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/ex-card-bg.png');
  }
  &.notuse {
    // background: #e6e6e6;
    background-image: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/ms-card-disabled-bg.png');
  }
  .card-top-row {
    display: flex;
    align-items: center;
  }
  .card-type {
    margin-right: 12rpx;
    border: 1px solid $theme-text-color-other;
    border-radius: 6px;
    width: 85rpx;
    height: 31rpx;
    line-height: 31rpx;
    text-align: center;
    font-size: 22rpx;
    color: $theme-text-color-other;
  }
  .card-top {
    display: flex;
    align-items: baseline;
  }
  .card-name {
    display: block;
    overflow: hidden;
    margin-bottom: 20rpx;
    max-width: 350rpx;
    text-overflow: ellipsis;
    font-weight: bold;
    font-size: 36rpx;
    white-space: nowrap;
  }
  .card-num {
    font-weight: bold;
    font-size: 24rpx;
    color: $theme-text-color-other;
  }
  .card-des {
    display: flex;
    position: absolute;
    left: 0;
    top: 165rpx;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 31rpx;
    width: 100%;
  }
  .overplus {
    display: block;
    margin-top: 4rpx;
    height: 32rpx;
    font-weight: bold;
    font-size: 24rpx;
    color: $theme-text-color-other;
    text {
      font-size: 36rpx;
    }
  }
  .status {
    position: absolute;
    right: 10rpx;
    top: 16rpx;
    border-radius: 50%;
    width: 112rpx;
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    font-weight: bold;
    font-size: 24rpx;
    transform: rotate(45deg);
    &::before,
    &::after {
      position: absolute;
      border-radius: 50%;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      content: ' ';
    }
    &::before {
      border: 2rpx solid $theme-text-color-other;
      width: 108rpx;
      height: 108rpx;
    }
    &::after {
      border: 4rpx solid $theme-text-color-other;
      width: 94rpx;
      height: 94rpx;
    }
  }
  .card-bottom-row {
    display: flex;
    position: absolute;
    left: 32rpx;
    right: 32rpx;
    bottom: 20rpx;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    .left-btn {
      padding: 26rpx 30rpx 6rpx;
      padding-left: 0;
      min-width: 130rpx;
    }
    
  }
}
.drop-down {
    display: inline-block;
    position: relative;
    &::after {
      position: absolute;
      right: -24rpx;
      bottom: 50%;
      border-width: 10rpx 8rpx 0;
      border-style: solid;
      border-color: $theme-text-color-other transparent;
      width: 0;
      height: 0;
      content: '';
      transform: translateY(50%);
    }
  }
.date-switch {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 22rpx 20rpx 0;
  overflow: hidden;
}
.date-switch .date {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
  width: 86rpx;
  margin-bottom: 35rpx;
}
.date-switch .day {
  width: 66rpx;
  height: 66rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #313131;
  text-align: center;
  line-height: 66rpx;
}

.date-switch .date .week {
  margin-bottom: 10rpx;
  font-size: 24rpx;
}
.date-switch .active .day {
  background: var(--THEME-COLOR);
  border-radius: 50%;
}

.merchant-bus-wrap {
  padding: 0 25rpx;
  display: flex;
  justify-content: flex-end;
}
