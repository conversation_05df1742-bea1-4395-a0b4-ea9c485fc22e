.page {
  box-sizing: border-box;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  height: 100%;
}
.page-main {
  overflow-y: scroll;

  width: 100vw;
  height: 100%;
}
[data-theme='default'] {
  background: #f6f6f8;
  color: #000;
  .theme-bg {
    background-color: #fff !important;
  }
}
[data-theme='dark'] {
  background: #000;
  color: #fff;
  .theme-bg {
    background-color: #0f0f0f !important;
  }
  input,
  button {
    color: #fff;
  }
}
.themeicon {
  display: inline-block;
}
.theme-color-other {
  color: $theme-text-color-other;
}
