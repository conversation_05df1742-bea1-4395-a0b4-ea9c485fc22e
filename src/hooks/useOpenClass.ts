import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
import http from '@/utils/request'
import { goUrlPage } from '@/utils'
export function useOpenClass() {
  const userStore = useUserStore()
  const { checkLogin } = useLogin()

  const goReservePage = (info, bus?: string) => {
    const busId = bus || userStore.userInfoBusId
    goUrlPage(
      `/pages/class/openClassReserve?id=${info.course_schedule_id}&coach_id=${info.coach_id}&class_id=${info.class_id}&bus_id=${busId}`
    )
  }

  const cancelReserve = async (class_mark_id, callBack, busId?: string) => {
    await checkLogin()
    uni.showModal({
      title: '确定要取消预约吗？',
      content: '取消预约将返还预约时的花费',
      success: (res) => {
        if (res.confirm) {
          http
            .post('/Classmark/cancelCourseMark', {
              bus_id: busId || userStore.userInfoBusId,
              class_mark_id,
              loading: true,
            })
            .then((res) => {
              uni.showToast({
                icon: 'success',
                title: '取消成功',
              })
              if (callBack) {
                callBack()
              } else {
                setTimeout(() => {
                  wx.navigateBack()
                }, 1000)
              }
            })
        }
      },
    })
  }

  const confirmWait = async (
    class_waitting_id,
    callBack?: () => void,
    userInfo?: { bus_id: string; user_id: string }
  ) => {
    if (!userInfo?.bus_id || !userInfo?.user_id) {
      await checkLogin(userInfo?.bus_id || '')
      userInfo = {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
      }
    }
    http
      .post('/Classwaiting/confirm', {
        ...userInfo,
        class_waitting_id,
        loading: true,
      })
      .then((res) => {
        uni.showToast({
          icon: 'success',
          title: '确认成功',
        })
        if (callBack) {
          callBack()
        } else {
          setTimeout(() => {
            wx.navigateBack()
          }, 1000)
        }
      })
  }

  const cancelWait = async (
    class_waitting_id,
    callBack?: () => void,
    userInfo?: { bus_id: string; user_id: string }
  ) => {
    if (!userInfo?.bus_id || !userInfo?.user_id) {
      await checkLogin(userInfo?.bus_id || '')
      userInfo = {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
      }
    }
    uni.showModal({
      title: '确定取消候补吗？',
      content: '取消后将返回候补时锁定的花费',
      success: (res) => {
        if (res.confirm) {
          http
            .post('/Classwaiting/cancel', {
              ...userInfo,
              class_waitting_id,
              loading: true,
            })
            .then((res) => {
              uni.showToast({
                icon: 'success',
                title: '取消成功',
              })
              if (callBack) {
                callBack()
              } else {
                setTimeout(() => {
                  wx.navigateBack()
                }, 1000)
              }
            })
        }
      },
    })
  }
  // 空位订阅
  const goSubscribeNotice = (info, callBack?: () => void) => {
    const tmplId = info.template_id
    wx.requestSubscribeMessage({
      tmplIds: [tmplId],
      success: async (res) => {
        if (res.errMsg === 'requestSubscribeMessage:ok' && res[tmplId] === 'accept') {
          await checkLogin()
          const userInfo = {
            bus_id: userStore.userInfoBusId,
            user_id: userStore.userInfoUserId,
          }
          http
            .post('/Schedule/SubscribeNotice', {
              ...userInfo,
              course_schedule_id: info.course_schedule_id,
              loading: true,
            })
            .then((res) => {
              uni.showToast({
                title: '已订阅空位，请等待通知',
                icon: 'none',
              })
              if (callBack) {
                callBack()
              }
            })
        } else {
          uni.showToast({
            title: '订阅失败',
            icon: 'none',
          })
        }
      },
      fail(res) {
        uni.showToast({
          title: '订阅失败',
          icon: 'none',
        })
      },
      complete(res) {},
    })
  }

  return {
    goSubscribeNotice,
    goReservePage,
    cancelReserve,
    confirmWait,
    cancelWait,
  }
}
