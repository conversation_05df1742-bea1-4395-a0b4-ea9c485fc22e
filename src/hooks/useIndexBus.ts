import { useThemeStore } from '@/store/theme'
import { useUserStore } from '@/store/user'

export function useIndexBus(propsConfigBusId, effectFn?: Function, otherOptions?: any) {
  const optionsBusId = ref('')
  const userStore = useUserStore()
  const themeStore = useThemeStore()
  onLoad((options) => {
    optionsBusId.value = options.bus_id || ''
  })
  const isShowMerchantMode = computed(() => themeStore.isShowMerchantMode)
  const busId = computed(() => propsConfigBusId || optionsBusId.value || userStore.userInfoBusId)
  const stop = watch(
    busId,
    (val) => {
      if (val) {
        busId.value && effectFn(busId.value, isShowMerchantMode.value, otherOptions)
      }
    },
    { immediate: true }
  )
  // // 注销watch
  onHide(() => {
    stop()
  })
  return {
    busId,
    themeStore,
    userStore,
    isShowMerchantMode,
  }
}
