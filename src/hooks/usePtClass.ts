import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
import http from '@/utils/request'
export function usePtClass() {
  const userStore = useUserStore()
  const { checkLogin } = useLogin()
  const cancelPtReserve = async (pt_schedule_id, callBack, appt_type, refund_rate, offline_pay) => {
    await checkLogin()

    if (offline_pay == 1) {
      uni.showModal({
        title: '提示',
        content: '可能涉及线下费用退款，请联系管理员取消。',
        showCancel: false,
        confirmText: '我知道了'
      })
      return false;
    }
    
    let content = '确定要取消预约吗？'
    // 是否是单次购私教课那种方式
    if (appt_type === 1) {
      const rate = Number(refund_rate || 0) * 100
      content = `预计退回 ${rate.toFixed(2)}% 的约课花费`
    }
    uni.showModal({
      title: '提示',
      content,
      success: (res) => {
        if (res.confirm) {
          http
            .post('/Schedule/cancelPtSchedule', {
              pt_schedule_id,
              loading: true,
            })
            .then((res) => {
              uni.showToast({
                icon: 'success',
                title: '取消成功',
              })
              if (callBack) {
                callBack()
              } else {
                setTimeout(() => {
                  wx.navigateBack()
                }, 1000)
              }
            })
        }
      },
    })
  }

  return {
    cancelPtReserve,
  }
}
