import { useUserStore } from '@/store/user'
import http from '@/utils/request'
export function usePoint() {
  const userStore = useUserStore()
  const currentPoint = ref(0)
  const expPoint = ref(0)
  const getUserPoint = () => {
    http
      .get('/Point/getUserPoint', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
      })
      .then((res) => {
        currentPoint.value = res.data.point || 0
      })
  }
  const getUserPointAndExpire = () => {
    http
      .get('/Point/getUserPointAndExpire', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
      })
      .then((res) => {
        currentPoint.value = res.data.info?.point || 0
        expPoint.value = res.data.info?.point_due || 0
      })
  }
  return {
    expPoint,
    currentPoint,
    getUserPointAndExpire,
    getUserPoint,
  }
}
