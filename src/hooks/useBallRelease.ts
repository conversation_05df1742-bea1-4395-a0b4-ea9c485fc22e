import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import http from '@/utils/request'
import _ from 'lodash'
export function useBallRelease() {
  const userStore = useUserStore()
  const { checkLogin } = useLogin()

  const cancelReserve = async (sports_mark_order_id, callBack) => {
    const { user_id, bus_id } = await checkLogin()
    uni.showModal({
      title: '提示',
      content: '确定要取消吗？',
      success: (res) => {
        if (res.confirm) {
          http
            .post('/Sportsmark/sportsMarkCancel', {
              bus_id,
              user_id,
              sports_mark_order_id,
              loading: true,
            })
            .then((res) => {
              uni.showToast({
                icon: 'success',
                title: '取消成功',
              })
              if (callBack) {
                callBack()
              } else {
                setTimeout(() => {
                  wx.navigateBack()
                }, 1000)
              }
            })
        }
      },
    })
  }

  const confirmSignup = _.throttle(
    async (sports_mark_id, callBack) => {
      const { user_id, bus_id } = await checkLogin()
      http
        .post('/Sportsmark/sportsMarkSignup', {
          bus_id,
          user_id,
          sports_mark_id,
          loading: true,
        })
        .then((res) => {
          uni.showToast({
            icon: 'none',
            title: '报名成功，请留意结果通知',
          })
          uni.requestSubscribeMessage({
            tmplIds: [res.data.template_id],
            complete() {
              if (callBack) {
                callBack()
              } else {
                setTimeout(() => {
                  wx.navigateBack()
                }, 1000)
              }
            },
          })
        })
    },
    2000,
    true
  )

  return {
    cancelReserve,
    confirmSignup,
  }
}
