import { useUserStore } from '@/store/user'
import http from '@/utils/request'
import { Loading } from '@icon-park/vue-next'
import { throttle } from 'lodash'
export function usePalm() {
  const userStore = useUserStore()
  const throttledReports = new Map<string, () => void>()
  const lastUsageTime = new Map<string, number>() // 记录每个 action 的最后使用时间

  /**
   * 清理不再使用的 throttled 函数
   */
  const cleanUpThrottledReports = () => {
    const now = Date.now()
    throttledReports.forEach((_, action) => {
      if (now - lastUsageTime.get(action) > 10000) {
        // 10秒内未使用则清除
        throttledReports.delete(action)
        lastUsageTime.delete(action)
      }
    })
  }
  /**
   * @description: 统计上报 同一action 3秒内只上报一次
   * @param action 我的=my 我的里面的刷掌=my_palmservice 小程序首页弹窗=home_popup 首页弹窗点击=home_popup_click 刷掌服务本人开掌 =  palmservice_service 购票/卡=card_done 购票/卡点击=card_done_click 进店指引=store_guide 进店指引点击=store_guide_click 协助=assist
   */
  const palmStaReport = (action = 'palmservice_service') => {
    // 验证 action 是否为空或无效
    if (!action || typeof action !== 'string') {
      return
    }

    // 定期清理不再使用的 throttled 函数
    cleanUpThrottledReports()
    if (!throttledReports.has(action)) {
      const reportAction = () => {
        const { m_id, user_id, bus_id } = userStore.userInfo
        if (!user_id || !bus_id) {
          console.warn('palmStaReport Missing required userInfo fields:', userStore.userInfo)
          return
        }
        try {
          http
            .post('/Statistics/report', {
              m_id,
              bus_id,
              user_id,
              action,
              loading: false
            })
            .catch((error) => {
              console.error('palmStaReport Failed to report statistics:', error)
            })
        } finally {
          lastUsageTime.set(action, Date.now())
        }
      }
      //leading: 是否在节流开始时立即执行函数。 trailing: 是否在节流结束时执行函数。
      throttledReports.set(action, throttle(reportAction, 3000, { leading: true, trailing: false }))
    }
    return throttledReports.get(action)?.()
  }

  return {
    palmStaReport,
  }
}
