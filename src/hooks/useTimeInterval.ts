import { ref } from 'vue'
export function useTimeInterval(codeTextDefault) {
  const timeInit = 60
  const time = ref(timeInit)
  const codeText = ref(codeTextDefault||'获取验证码')
  const codeDisabled = ref(false)
  const timeInterval = ref(null)

  function undisButton() {
    const curTime = parseInt(time.value) - 1
    time.value = curTime
    codeText.value = curTime
  }

  function disButton() {
    time.value = timeInit
    codeText.value = codeTextDefault || '获取验证码'
    codeDisabled.value = false
    clearInterval(timeInterval.value)
  }

  function setTimeInterval() {
    disButton()
    codeDisabled.value = true
    timeInterval.value = setInterval(() => {
      if (time.value > 0) {
        undisButton()
      } else {
        disButton()
      }
    }, 1000)
  }

  return {
    undisButton,
    disButton,
    setTimeInterval,
    codeText,
    codeDisabled,
  }
}
