import { ref } from 'vue'
export function useNextDays() {
  const weekList = ref(['日', '一', '二', '三', '四', '五', '六'])
  const dayList = ref(['今天', '明天', '后天'])
  const today = ref('')
  const dates = ref<any>([])
  const dateList = ref<{ day: number; week: string; date: string }[]>([])
  const setDateList = (dayNum = 7) => {
    const dateNow = new Date()
    const yearNow = dateNow.getFullYear()
    const monthNow = dateNow.getMonth()
    const dayNow = dateNow.getDate()
    // today.value = `${yearNow}-${String(monthNow + 1).padStart(2, '0')}-${dayNow}`
    today.value = `${yearNow}-${String(monthNow + 1).padStart(2, '0')}-${String(dayNow).padStart(2, '0')}`
    let num = 0
    const dateListArr:any[] = []
    const datesArr:any[] = []
    while (num < dayNum) {
      const date = new Date(yearNow, monthNow, dayNow + num)
      datesArr.push(date)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const week = date.getDay()
      const day = String(date.getDate()).padStart(2, '0')
      dateListArr.push({
        day: day,
        week: dayList.value[num] || `周${weekList.value[week]}`,
        date: `${year}-${month}-${day}`,
      })
      num++
    }
    dates.value = datesArr
    dateList.value = dateListArr
  }
  const setDay = (info, callBack) => {
    today.value = info.date
    callBack && callBack()
  }
  return {
    today,
    dateList,
    setDay,
    setDateList,
  }
}
