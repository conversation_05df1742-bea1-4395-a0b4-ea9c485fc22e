import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import { useMerchant } from '@/store/merchant'
import { queryParser } from '@/utils/shared'
import { getCurrentPagePath } from '@/utils/urlMap'
import http from '@/utils/request'
import Auth from '@/utils/auth'
interface LoginResult {
  bus_id: string
  m_id?: string
  bus_name?: string
  user_id?: string
  msg?: string
}
export function useLogin() {
  const userStore = useUserStore()
  const themeStore = useThemeStore()
  /*
   * 获取入口参数 (无法获取到其它页面跳转过来的传参，适用于外部扫带scene二维码场景、公众号通过链接带参进入等)
   * return  Object //
   * {
   *   ...带scene二维码用scene换取的参数
   * }
   */
  const queryScene = ref()
  onLoad((option) => {
    if (option.scene) {
      queryScene.value = option.scene || ''
    }
  })
  onHide(() => {
    queryScene.value = ''
  })

  /*  扫码进入小程序场景时带的scene去获取具体参数
   ** 注意这里的scene换取参数的情况后端会根据path确定请求的场景 以便排除某些历史场景生成的scene没有在对应数据库中 此时会返回空数据给前端 
   ** 页面中调用时默认不需要传scene和path 会自动获取
   */
  const getParam = async (scene: string | undefined, path: string | undefined): Promise<User.sceneInfo['params']> => {
    scene = scene || queryScene.value || ''
    if (!scene) {
      userStore.setSceneInfo({
        scene,
        params: null,
      })
      return Promise.resolve({})
    }
    return new Promise((resolve) => {
      http
        .post('Sign/getParamByScene', {
          scene: decodeURIComponent(scene),
          // 获取当前小程序请求参数的页面路径传递给后端区分场景
          from_path: path || getCurrentPagePath(),
        })
        .then((res) => {
          const path = res.data.param
          const pathObj = path ? queryParser(path) : {}
          userStore.setSceneInfo({
            scene,
            params: pathObj,
          })
          resolve(pathObj)
        })
        .catch((err) => {
          userStore.setSceneInfo({
            scene,
            params: {},
          })
          resolve({})
        })
    })
  }

  /**
   * 是否解绑了当前已登录场馆
   * @return {Promise}
   */
  const checkUnbind = (userInfo, isGoLogin) => {
    return new Promise<LoginResult>((resolve, reject) => {
      http
        .post('Sign/checkUnbind', {
          ...userInfo,
          openid: userStore.userInfo.openid,
        })
        .then((res) => {
          const resData = res.data
          if (resData?.is_bind === 0) {
            if (!isGoLogin) {
              const info = {
                bus_id: resData.bus_id || userInfo.bus_id || '',
                bus_name: resData.bus_name || userInfo.bus_name || '',
                user_id: resData.user_id || '',
                openid: resData.openid || '',
                unionid: resData.unionid || '',
              }
              userStore.setUserInfo({
                ...info,
                is_display_self_qr: resData.is_display_self_qr,
              })
              resolve(info)
            } else {
              userStore.setUserInfo({
                bus_id: userInfo.bus_id,
                bus_name: userInfo.bus_name,
                user_id: '',
                openid: '',
                unionid: '',
              })
              uni.navigateTo({
                url: '/pages/login?hasChecked=true&navigateBack=true',
              })
              reject({
                bus_id: userInfo.bus_id,
                bus_name: userInfo.bus_name,
                user_id: '',
                openid: '',
                unionid: '',
                msg: '用户已经解绑！',
              })
            }
          } else {
            userStore.setUserInfo({
              is_display_self_qr: resData.is_display_self_qr,
            })
            resolve(userInfo)
          }
        })
    })
  }

  /**
   *
   * @param isGoLogin 是否跳转登录页
   * @param fromBusId 需要获取信息的场馆id
   * @returns
   */
  // eslint-disable-next-line max-params
  function checkLogin(isGoLogin = true, fromBusId?: string, isCheckUnbind = false) {
    //如果是分享等带有bus_id进入的场景首次检测优先使用这个id
    const firstLeavelBusId = userStore.launchBusId ? userStore.launchBusId : userStore.userInfoBusId || ''
    userStore.setLaunchBusId('')
    let busId = fromBusId ? fromBusId : firstLeavelBusId
    const busName = userStore.userInfo.bus_name
    const operationMode = themeStore.operationMode
    const userId = userStore.userInfoUserId
    const mId = userStore.userInfoMId
    const useMerchantStore = useMerchant()
    const resInfo = {
      user_id: userId,
      bus_id: busId,
      bus_name: busName,
      m_id: mId,
    }
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<LoginResult>(async (resolve, reject) => {
      if (userId && busId && operationMode !== undefined && busId === userStore.userInfoBusId) {
        removeRegisterStorage()
        userStore.setUserInfo(resInfo)
        resolve(resInfo)
      } else {
        if (busId !== userStore.userInfoBusId) {
          userStore.setUserId('')
          userStore.setBusId(busId)
        }
        try {
          // 入口函数请求完毕后才能确认一定有本地场馆id
          if (!themeStore.isEntranceReady) {
            // 如果初始化为完成，则等待
            await new Promise((resolve) => {
              const interval = setInterval(() => {
                if (themeStore.isEntranceReady) {
                  clearInterval(interval)
                  resolve()
                }
              }, 100)
            })
            busId = busId || userStore.userInfoBusId || ''
          }
          const { user_id: busUserId, bus_name: busNameGet } = await useMerchantStore.getUserIdByBus(busId, true)
          const info = {
            bus_id: busId,
            user_id: busUserId,
            bus_name: busNameGet,
            m_id: mId,
          }
          userStore.setUserInfo(info)
          if (isGoLogin && !(info?.user_id && info?.bus_id)) {
            uni.navigateTo({
              url: '/pages/login?hasChecked=true&navigateBack=true',
            })
          }
          if ((info?.user_id && info?.bus_id) || !isGoLogin) {
            resolve({
              user_id: info?.user_id,
              m_id: info?.m_id,
              bus_id: info?.bus_id,
            })
          } else {
            reject({
              user_id: info?.user_id,
              m_id: info?.m_id,
              bus_id: info?.bus_id,
              msg: '无用户信息！',
            })
          }
        } catch (error) {
          if (isGoLogin) {
            uni.navigateTo({
              url: '/pages/login?hasChecked=true&navigateBack=true',
            })
            reject({
              bus_id: busId,
              msg: '登录请求出错！',
            })
          } else {
            resolve({
              bus_id: busId,
              user_id: '',
            })
          }
        }
      }
    })
  }

  /**
   * app init
   */
  function appInit() {
    //如果是分享等带有bus_id进入的场景首次检测优先使用这个id
    const busId = userStore.launchBusId ? userStore.launchBusId : userStore.userInfoBusId || ''
    userStore.setLaunchBusId('')
    const operationMode = themeStore.operationMode
    const userId = userStore.userInfoUserId
    const mId = userStore.userInfoMId
    const resInfo = {
      user_id: userId,
      bus_id: busId,
      m_id: mId,
    }
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<LoginResult>(async (resolve, reject) => {
      if (userId && busId && operationMode !== undefined && busId === userStore.userInfoBusId) {
        let unBindInfo = resInfo
        unBindInfo = await checkUnbind(resInfo)
        removeRegisterStorage()
        resolve(unBindInfo)
      } else {
        if (busId !== userStore.userInfoBusId) {
          userStore.setUserId('')
          userStore.setBusId(busId)
        }
        if (!busId && !mId) {
          await userStore.getLocationInfo()
        }
        const loginRes = await uni.login({
          onlyAuthorize: true,
        })
        http
          .get('/Entrance/index', {
            bus_id: busId,
            js_code: loginRes.code,
            openid: userStore.userInfo.openid,
            unionid: userStore.userInfo.unionid,
            lng: userStore.locationInfo.longitude,
            lat: userStore.locationInfo.latitude,
          })
          .then((res) => {
            const info = res.data.info
            userStore.setUserInfo(info)
            themeStore.setIsEntranceReady(true)
            if (operationMode === undefined) {
              // 运营模式全局只设置一次 1 综合体育场馆模式 0 健身瑜伽模式
              themeStore.setOperationMode(info.operation_mode)
              themeStore.changeShowMerchantMode(info.operation_mode === 1)
            }
            Auth.setToken(info.access_token || '')
            Auth.setRefreshToken(info.refresh_token || '')
            resolve({
              user_id: info?.user_id || '',
              m_id: info?.m_id || '',
              bus_id: info?.bus_id || '',
            })
          })
          .catch((err) => {
            reject({
              bus_id: busId || '',
              m_id: mId || '',
              msg: '初始化出错！',
            })
          })
      }
    })
  }

  // 移除注册新场馆会员的部分缓存
  const removeRegisterStorage = () => {
    // 移除用于注册使用的介绍人ID
    uni.removeStorageSync('introducer_id')
    // 移除用于注册使用的source_name
    uni.removeStorageSync('source_name')
    // 移除用于注册使用的邀请有礼id
    uni.removeStorageSync('activity_id')
    uni.removeStorageSync('inviter_user_id')
  }

  return {
    getParam,
    appInit,
    checkLogin,
  }
}
