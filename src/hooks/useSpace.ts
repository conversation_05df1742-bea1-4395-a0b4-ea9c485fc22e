import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
import http from '@/utils/request'
import lodash from 'lodash'
export function useSpace() {
  const userStore = useUserStore()
  function handleSpaceName(id: number, content: string) {
    if (!id || !content) {
      return
    }
    uni.navigateTo({
      url: `/pages/stadium/applicableSpace?san_id=${id}`,
    })
  }

  const continuePay = lodash.throttle(
    async (info, callBack) => {
      const bus_id = info.bus_id || userStore.userInfoBusId
      const user_id = info.user_id || userStore.userInfoUserId
      http
        .post('Booking/continuePay', {
          bus_id,
          user_id,
          space_order_id: info.id,
        })
        .then((res) => {
          if (res.data.jsApiParameters) {
            pay(res.data.jsApiParameters, callBack)
          } else {
            uni.showLoading({
              title: res.errormsg,
              mask: true,
            })
            if (callBack) {
              callBack()
            }
          }
        })
    },
    2000,
    true
  )

  function pay(info, callBack) {
    uni.requestPayment({
      timeStamp: info.timeStamp,
      nonceStr: info.nonceStr,
      package: info.package,
      signType: info.signType,
      paySign: info.paySign,
      provider: 'alipay',
      orderInfo: info.orderInfo || '',
      success: () => {
        uni.showLoading({
          title: '支付成功',
          mask: true,
        })
        if (callBack) {
          callBack()
        }
      },
      fail: () => {
        // fail
        uni.showToast({
          title: '取消支付',
          icon: 'none',
        })
        uni.hideLoading()
      },
    })
  }
  async function cancelReserve(info, callBack) {
    // status 订单状态，0-未支付，1-已支付，2-已到场，3-已离场，4-已退款，5-已取消（未付款）',
    // ssl_status 关联课程状态，0-正常，1-已退订，2-已上课

    if (info.status !== 0 && info.cancel_booking_refund_ladder) {
      const cancel_booking_refund_ladder = JSON.parse(info.cancel_booking_refund_ladder)
      const canCancelTime = (info.start_time - cancel_booking_refund_ladder[0].start * 60) * 1000
      const nowTime = new Date().getTime()
      if (nowTime > canCancelTime) {
        uni.showToast({
          title: '当前时间已不可取消',
          icon: 'none',
        })
        return
      }
    }

    let content = '';
    if (info.status === 1 && info.ssl_status === 0 && info.reservation_type === 2) {
      await http.post('/Booking/getBookingScheduleRefundLadder', {
        bus_id: info.bus_id,
        pt_schedule_id: info.pt_schedule_id,
        user_id: info.user_id,
      }).then((res) => {
        if (res.errorcode === 0) {
          const cpt_refund_rate = Number(res.data.refund_rate) * 100;
          const booking_refund_rate = Number(info.refund_rate) * 100;

          content = `将会同时取消订场和约课\r\n预计退回${booking_refund_rate.toFixed(2)}%的订场花费\r\n预计退回${cpt_refund_rate.toFixed(2)}%的约课花费`;
        }
      })
    } else if (info.ssl_status === 2 || info.status === 2) {
      uni.showModal({
        title: '',
        content: '关联课程已上课，无法取消场地。',
        showCancel: false,
        confirmText: '我知道了',
      });
      return;
    } else if ((info.ssl_status === 1 && info.reservation_type !== null && info.refund_port === 1) || info.status === 5) {
      uni.showModal({
        title: '',
        content: '关联课程已取消，请联系管理员取消场地。',
        showCancel: false,
        confirmText: '我知道了',
      });
      return;
    }


    if (!content) {
      content = info.sports_mark_order_id
        ? '取消订场将一并取消邀约'
        : info.status === 0
        ? '确定取消订场么？'
        : `预计退回${((info.refund_rate || 1) * 100).toFixed(2)}%的订场花费`;
    }
    uni.showModal({
      title: '确定取消吗？',
      content,
      success: (res) => {
        if (res.confirm) {
          const bus_id = info.bus_id || userStore.userInfoBusId
          const user_id = info.user_id || userStore.userInfoUserId

          // let url = '/Booking/cancelBooking'
          // if (info.ssl_status === 0) {
          //   url = '/Booking/cancelBookingAndPt'
          // }

          http
            .post('/Booking/cancelBookingAndPt', {
              bus_id,
              user_id,
              space_order_id: info.id,
              sports_mark_order_id: info.sports_mark_order_id || '', // 约球邀约需要
              pt_schedule_id: info.pt_schedule_id || '', // 约课需要
              loading: true,
            })
            .then((res) => {
              uni.showToast({
                icon: 'success',
                title: '取消成功',
              })
              // 如果是储值卡组合支付,并且有扣除储值卡余额，返还扣除的储值卡余额。
              // 注意，如果使用的储值卡组合支付，后端会将此处支付方式pay_type改为1微信
              if (res.errorcode == 0 && Array.isArray(info.mix_pay_list) && info.mix_pay_list.length > 1) {
                const mixPayItem = info.mix_pay_list.find((v) => v.pay_type === 8)
                if (mixPayItem) {
                  http.post('Booking/storedCardChangeRevoke', {
                    bus_id,
                    user_id,
                    card_user_id: mixPayItem.card_user_id,
                    business_id: info.id,
                    business_type: '2', // 业务类型，1--散场票，2--订场，3--购卡
                  })
                }
              }
              if (callBack) {
                callBack()
              } else {
                setTimeout(() => {
                  uni.navigateBack()
                }, 1000)
              }
            })
        }
      },
    })
  }
  return {
    handleSpaceName,
    cancelReserve,
    continuePay,
  }
}
