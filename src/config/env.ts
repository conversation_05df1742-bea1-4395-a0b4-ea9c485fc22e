const apiEnv: ApiEnv = 'beta'

const envMap = {
  dev: {
    apiBaseUrl: 'https://mf-api-beta.rocketbird.cn/',
    saasUrl: 'https://beta.rocketbird.cn',
    veinUrl: 'https://veinhw-api-beta.rocketbird.cn',
    webViewUrl: 'https://vip-test.rocketbird.cn',
    expertViewUrl: `https://es-beta-fe.rocketbird.cn`,
  },
  beta: {
    apiBaseUrl: 'https://openapi-beta.rocketbird.cn/member_forward/',
    saasUrl: 'https://beta.rocketbird.cn',
    veinUrl: 'https://veinhw-api-beta.rocketbird.cn',
    webViewUrl: 'https://vip-test.rocketbird.cn',
    expertViewUrl: `https://es-beta-fe.rocketbird.cn`,
  },
  sim: {
    apiBaseUrl: 'https://openapi-sim.rocketbird.cn/member_forward/',
    saasUrl: 'https://sim.rocketbird.cn',
    veinUrl: 'https://veinhw-api-sim.rocketbird.cn',
    webViewUrl: 'https://vip-sim.rocketbird.cn',
    expertViewUrl: `https://expert-sim.rocketbird.cn`,
  },
  wx: {
    apiBaseUrl: 'https://openapi-new.rocketbird.cn/member_forward/',
    saasUrl: 'https://wx.rocketbird.cn',
    veinUrl: 'https://veinhw-api.rocketbird.cn',
    webViewUrl: 'https://vip.rocketbird.cn',
    expertViewUrl: `https://expert.rocketbird.cn`,
  },
}
type ApiEnv = keyof typeof envMap
type Env<T extends ApiEnv> = {
  apiEnv: T
} & typeof envMap[T]

function createEnv(apiEnv: ApiEnv): Env<typeof apiEnv> {
  return Object.assign({ apiEnv }, envMap[apiEnv])
}

const env = createEnv(apiEnv)
export default env
