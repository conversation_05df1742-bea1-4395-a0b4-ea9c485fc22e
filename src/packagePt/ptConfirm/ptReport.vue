<template>
  <view>
    <web-view v-if="canIUse" :src="src"></web-view>
    <view wx:else> 请升级微信版本！ </view>
  </view>
</template>

<script setup lang="ts" name="ptReport">
import env from '@/config/env'
import { useLogin } from '@/hooks/useLogin.ts'

const { checkLogin, getParam } = useLogin()
const canIUse = ref(false)

let newOptions: any = {}
const src = ref('')

onLoad(async (options) => {
  canIUse.value = uni.canIUse('web-view')
  if (canIUse.value) {
    newOptions = { ...options }
  }
})

onShow(async() => {
  let user_id = ''
  if (newOptions.scene) {
      const query = await getParam(newOptions.scene)
      newOptions = { ...newOptions, ...query }

      try {
        const login = await checkLogin(true, newOptions.bus_id)
        user_id = login.user_id || ''
        if (!user_id) {
          uni.showModal({
            content: `无法获取到会员信息, userId: ${user_id}`,
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                uni.reLaunch({ url: '/pages/train/index' })
              }
            },
          })
        }
      } catch (error) {
        throw new Error(error)
      }
    } else if (newOptions.bus_id) {
      const login = await checkLogin(true, newOptions.bus_id)
      user_id = login.user_id || ''
    }
    src.value = `${env.expertViewUrl}/exerciseReport?attend_course_id=${newOptions.attend_course_id}&bus_id=${newOptions.bus_id}&service_type=${newOptions.service_type}&user_id=${user_id}`
})
</script>
