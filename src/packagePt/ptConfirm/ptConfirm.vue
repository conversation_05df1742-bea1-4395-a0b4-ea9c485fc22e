<template>
  <view class="box-tips">
    <view class="top">
      剩余
      <CountDown :size="15" :time="time" />
      可确认
    </view>
    <view class="bot">(超时只能到前台完成确认)</view>
  </view>
  <view v-if="info" class="form-items theme-bg">
    <view class="item">
      <text class="label">课程</text>
      <text class="value">{{ info.card_name }}</text>
    </view>
    <view class="item">
      <text class="label">上课教练</text>
      <text class="value">{{ info.coach_name }}</text>
    </view>
    <view class="item">
      <text class="label">时间</text>
      <text class="value">{{ info.class_time }}</text>
    </view>
    <view v-if="info.is_pt_time_limit_card != 1" class="item">
      <text class="label">总课程数</text>
      <text class="value">{{ info.all_num }}节</text>
    </view>
    <view v-if="info.is_pt_time_limit_card == 1" class="item">
      <text class="label">课程到期时间</text>
      <text class="value">{{ info.class_end_time }}</text>
    </view>
    <view class="item">
      <text class="label">已上课数</text>
      <text class="value"
        >{{
          info.is_pt_time_limit_card == 1 ? info.have_class_number : info.all_num - info.after_sign_last_num || ''
        }}节</text
      >
    </view>
    <view v-if="info.is_pt_time_limit_card != 1" class="item">
      <text class="label">本次上课</text>
      <text class="value">{{ info.sign_number }}节</text>
    </view>
    <view v-if="info.is_pt_time_limit_card != 1" class="item">
      <text class="label">本次后剩余</text>
      <text class="value">{{ info.after_sign_last_num }}节</text>
    </view>
  </view>
  <view v-if="info" class="fixed-bottom-wrap theme-bg">
    <button class="normal-btn" @tap="handleConfirm">确认完成</button>
  </view>
</template>
<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import CountDown from '@/components/CountDown'
import _ from 'lodash'
const { checkLogin, getParam } = useLogin()
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})

const params = reactive({
  stop_date: '',
  active_date: '',
  suspend_method: '',
  card_user_id: '',
  amount: '',
})
const info = ref(null)
const time = ref('00:00')
const curOption = ref()
onLoad((options) => {
  curOption.value = options
})
onShow(async () => {
  const curParams = await getParam(curOption.value.scene || '')
  curOption.value = curParams || curOption.value
  getInfo(curOption.value)
  try {
    const login = await checkLogin(true, curOption.value.bus_id)
    const user_id = login.user_id
    if (user_id && user_id !== curOption.value.user_id) {
      uni.showModal({
        content: '会员信息错误，不是这节课上课会员',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            uni.reLaunch({ url: '/pages/index/index' })
          }
        },
      })
    } else if (!user_id) {
      uni.showModal({
        content: `无法获取到会员信息, userId: ${user_id}`,
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            uni.reLaunch({ url: '/pages/index/index' })
          }
        },
      })
    }
  } catch (error) {
    throw new Error(error)
  }
})

const handleConfirm = _.throttle(
  () => {
    if (params.suspend_method === '2' && !params.card_user_id) {
      uni.showToast({ title: '请选择会员卡', icon: 'none' })
      return
    }
    const { bus_id, user_id, attend_id, create_time, service_type } = curOption.value
    http
      .post('Ivep/ivepUserSign', {
        bus_id,
        user_id,
        attend_id,
        create_time,
        service_type: service_type || '',
      })
      .then((res) => {
        uni.showToast({
          title: '确认成功',
        })
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/index/index' })
        }, 1000)
      })
  },
  2000,
  true
)

function getInfo(postData) {
  const { bus_id, user_id, attend_id, create_time, service_type } = postData
  http
    .post('Ivep/ivepSignInfo', {
      bus_id,
      user_id,
      attend_id,
      create_time,
      service_type: service_type || '',
    })
    .then((res) => {
      const data = res.data?.sign_info
      info.value = data
      const timeNum = Math.max(Math.floor(3600 - (Date.now() - new Date(data.end_time * 1000)) / 1000), 0)
      time.value = `${String(Math.floor(timeNum / 60)).padStart(2, '0')}:${String(timeNum % 60).padStart(2, '0')}`
    })
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20rpx;
}
.box-tips {
  margin: 80rpx 20rpx 50rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: bold;
  .bot {
    font-size: 24rpx;
    font-weight: 400;
    margin-top: 30rpx;
  }
}
.oth-text {
  color: $theme-text-color-other;
}
</style>
