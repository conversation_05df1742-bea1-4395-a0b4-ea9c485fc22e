<template>
  <view>
    <uni-card v-if="course.b_time" :title="'上课时间: ' + course.b_time">
      <view class="card-box">
        <view class="photo">
          <image v-if="course.thumb" class="photo-img" mode="widthFix" :src="course.thumb" />
          <image v-else class="photo-img" mode="widthFix" src="https://imagecdn.rocketbird.cn/default/man_coach.jpg" />
        </view>
        <view class="info" v-if="course.class_name">
          <view style="margin: 20rpx 0">
            <uni-title type="h1" :title="course.class_name"></uni-title>
          </view>
          <uni-list>
            <uni-list-item :show-extra-icon="true" :extra-icon="dateIcon" :title="course.class_hour + ' 分钟'" />
            <uni-list-item
              :show-extra-icon="true"
              :extra-icon="peopleIcon"
              :title="coachRoom"
            />
            <uni-list-item :show-extra-icon="true" :extra-icon="homeIcon" :title="course.bus_name" />
          </uni-list>
        </view>
      </view>
    </uni-card>
    <uni-card v-else>
      <view class="no-data">暂无课程信息</view>
    </uni-card>
    <button class="normal-btn" style="width: 690rpx; margin: 30rpx" @click="handleSignCourse" :disabled="!course.b_time">确认到场上课</button>
  </view>
</template>

<script setup lang="ts" name="ptArrival">
import { useLogin } from '@/hooks/useLogin'
import http from '@/utils/request'
import _ from 'lodash'

const dateIcon = {
  size: '22',
  type: 'fire-filled',
}
const peopleIcon = {
  size: '22',
  type: 'contact-filled',
}
const homeIcon = {
  size: '22',
  type: 'location-filled',
}

const busId = ref('')
const userId = ref('')
const coachRoom = computed(() => {
  if (course.value.coach_name && course.value.classroom_name) {
    return `${course.value.coach_name} | NO.${course.value.classroom_name}`
  } else if (course.value.coach_name) {
    return course.value.coach_name
  } else {
    return ''
  }
})

const { checkLogin, getParam } = useLogin()
let newOptions: any = {}

onLoad((options) => {
  newOptions = { ...options }
})

onShow(async () => {
  if (newOptions.scene) {
    const query = await getParam(newOptions.scene||'')
    newOptions = { ...newOptions, ...query }
    console.log('newOptions: ', newOptions)

    try {
      const login = await checkLogin(true, newOptions.bus_id)
      userId.value = login.user_id || ''
      busId.value = newOptions.bus_id
      if (!userId.value) {
        uni.showModal({
          content: `无法获取到会员信息, userId: ${userId.value}`,
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              uni.reLaunch({ url: '/pages/train/index' })
            }
          },
        })
      } else {
        getCourse()
      }
    } catch (error: any) {
      throw new Error(error)
    }
  } else if (newOptions.bus_id) {
    const login = await checkLogin(true, newOptions.bus_id)
    userId.value = login.user_id || ''
    busId.value = newOptions.bus_id
    if (userId.value) {
      getCourse()
    }
  }
})

const course = ref({
  b_time: '',
  class_name: '',
  class_hour: '',
  coach_name: '',
  classroom_name: '',
  bus_name: '',
  thumb: '',
})
const getCourse = () => {
  return http
    .post('/Course/getUserClassSignInfo', {
      bus_id: busId.value,
      user_id: userId.value,
      // user_id: 'B_BBK-tBNweJ30z',
      course_schedule_id: newOptions.course_schedule_id,
      attend_course_id: newOptions.attend_course_id,
      service_type: newOptions.service_type,
      showToast: false,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        course.value = res.data
      }
    }).catch((error) => {
      uni.showToast({
        // title: '二维码已过期',
        title: error.errormsg,
        icon: 'error',
      })
    })
}

// throttle submit
const handleSignCourse = _.throttle(
  () => {
    return http
      .post('/Course/classSign', { 
        bus_id: busId.value,
        user_id: userId.value,
        course_schedule_id: newOptions.course_schedule_id,
        attend_course_id: newOptions.attend_course_id,
        service_type: newOptions.service_type,
      })
      .then((res) => {
        if (res.errorcode === 0) {
          uni.showToast({
            title: res.errormsg,
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                uni.navigateTo({ url: '/pages/my/reserveRecord' })
              }, 1500)
            },
          })
        } else {
          uni.showToast({
            title: res.errormsg,
            icon: 'error',
          })
        }
      })
  },
  2000,
  true
)
</script>

<style lang="scss" scoped>
.photo {
  display: flex;
  justify-content: center;
  align-items: center;

  .photo-img {
    width: 100%;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: $uni-text-color-grey;
  font-weight: bold;
  width: 100%;
  height: 400rpx;
}
</style>
