<template>
  <view class="info-box">
    <view class="info-tips">跳转体测小程序查看信息</view>
    <view class="info-main">
      <button class="btn-info" @tap="handleJump">去查看</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="class">
const info = ref('')
// 中转页 唯一参数info 需要先encode
onLoad((options) => {
  info.value = options.info || '{}'
})

const handleJump = () => {
  const { jump_url, wx_appid, wx_version } = JSON.parse(decodeURIComponent(info.value))
  console.log(jump_url, wx_appid, wx_version)
  if (!jump_url) return
  wx.navigateToMiniProgram({
    appId: wx_appid,
    path: jump_url,
    envVersion: wx_version || 'release',
  })
}
</script>

<style lang="scss" scoped>
.info-box {
  padding-top: 188rpx;
  // background: #161823 url('https://imagecdn.rocketbird.cn/minprogram/member/image/info-bg.png')
  background-size: cover;
  font-size: 28rpx;
  // color: #fff;
  position: relative;
  .info-main {
    width: 580rpx;
    margin: 0 auto;
  }
  .info-tips {
    widows: 700rpx;
    text-align: center;
  }
}

.btn-info {
  width: 522rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: var(--THEME-COLOR);
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 52rpx;
  margin: 78rpx auto 0;
  color: #fff;
}
</style>
