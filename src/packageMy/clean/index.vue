<template>
  <view class="locker-cleans-page theme-bg">
    <view class="locker-cleans-container">
      <button
        v-if="isSubscribe"
        class="normal-btn normal-btn-small subscribe-btn"
        open-type="openSetting">
        <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/clean-notification.png" alt="icon"/>
        已订阅
      </button>
      <button
        v-else
        class="normal-btn normal-btn-small subscribe-btn"
        @click="handleSubscribe(true)">
        <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/clean-notification.png" alt="icon"/>
        订阅
      </button>
      <view class="count-wrap">
        <ThemeIcon class="icon-mr" type="t-icon-mendianqiehuan-01" :size="13" color="#FF7427" />
        <text class="locker-days-row">
          {{ curOptions.bus_name }}
        </text>
        <view class="locker-count-box">
          <view class="locker-count-item">
            <text class="type-name">今日已清洁</text>
            <text class="count-text">
              <text class="count">{{ cleanData.today_clean || 0 }}</text>
            </text>
          </view>
          <view class="locker-count-item">
            <text class="type-name">今日待清洁</text>
            <text class="count-text">
              <text class="count">{{ cleanData.treat_clean || 0 }}</text>
            </text>
          </view>
        </view>
      </view>

      <view class="list-wrap">
        <template v-if="cleanList.length">
          <template v-for="(item, index) in cleanList" :key="item.device_id">
            <view class="locker-box">
              <view class="locker-title-row">
                <text class="locker-title">{{ item.device_name }}</text>
                <text class="unclearn-num">待清洁 {{ item.cabinet_num.length }}</text>
              </view>
              <template v-if="item.cabinet_num.length">
                <view
                  v-for="(subItem, subIndex) in item.cabinet_num"
                  :key="subItem.cabinet_no"
                  class="locker-item">
                  <text class="locker-num">{{ subItem.cabinet_name || subItem.cabinet_no }}</text>
                  <text class="return-time">退柜时间 {{ subItem.refund_time }}</text>
                  <view class="buttons">
                    <!-- :disabled="!subItem.isOpen" -->
                    <button
                      class="normal-btn the-page-min"
                      @click="handleComplete(item, index, subItem, subIndex)">
                      完成清洁
                    </button>
                    <button
                      class="normal-btn the-page-min friend"
                      @click="handleOpen(item, index, subItem, subIndex)">
                      开柜
                    </button>
                  </view>
                </view>
              </template>
            </view>
          </template>
        </template>
        <view v-else class="nodata">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="trainOver">
import http from '@/utils/request'
import ThemeIcon from '@/components/ThemeIcon.vue'
import { formatDate } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin'
import { useMerchant } from '@/store/merchant'
import { useUserStore } from '@/store/user'
import { checkBusIdAndSwitch } from '@/utils/urlMap'

const useMerchantStore = useMerchant()
const userStore = useUserStore()
const { checkLogin, getParam } = useLogin()

const curOptions = reactive({
  bus_id: '',
  user_id: '',
  bus_name: '',
  scene: ''
})
onLoad((options) => {
  Object.assign(curOptions, options)
})
onShow(async () => {
  if (curOptions.scene !== undefined) {
    Object.assign(curOptions, await getParam(curOptions.scene || ''))
  }
  if (!curOptions.bus_id) {
    return uni.showToast({
      title: '未获取到场馆ID',
      icon: 'none',
    })
  }

  // curOptions.bus_id = '_BBK6oBCKkl' // 个人测试，当前有缓存，切换到其他场馆时的情况
  // curOptions.bus_id = '_BBK-sBAakl' // 个人测试，当前无缓存

  await checkLogin(true, curOptions.bus_id)
  const { user_id, bus_name } = await useMerchantStore.getUserIdByBus(curOptions.bus_id, true)
  curOptions.user_id = user_id
  curOptions.bus_name = bus_name
  getSubscribeSetting()
  getData()
})

onPullDownRefresh(() => {
  getData().then(uni.stopPullDownRefresh)
})


const isSubscribe = ref(false) // 是否订阅
// 获取订阅消息设置
const getSubscribeSetting = () => {
  uni.getSetting({
    withSubscriptions: true,
    success (res) {
      // res.authSetting = {
      //   "scope.userInfo": true,
      //   "scope.userLocation": true
      // }

      // res.subscriptionsSetting = {
      //   mainSwitch: true, // 订阅消息总开关
      //   itemSettings: { // 只返回用户勾选过订阅面板中的“总是保持以上选择，不再询问”的订阅消息
      //     SYS_MSG_TYPE_INTERACTIVE: 'accept', // 小游戏系统订阅消息
      //     SYS_MSG_TYPE_RANK: 'accept'
      //     zun-LzcQyW-edafCVvzPkK4de2Rllr1fFpw2A_x0oXE: 'reject', // 普通一次性订阅消息
      //     ke_OZC_66gZxALLcsuI7ilCJSP2OJ2vWo2ooUPpkWrw: 'ban',
      //   },
      //   zun-LzcQyW-edafCVvzPkK4de2Rllr1fFpw2A_x0oXE: 'reject', // 返回用户未勾选“总是保持以上选择，不再询问”的订阅消息
      // }

      const tmplId = cleanData.value.template_id
      const { mainSwitch, itemSettings } = res.subscriptionsSetting
      // 订阅消息总开关 并且 该模板消息是否同意
      isSubscribe.value = mainSwitch && itemSettings && itemSettings[tmplId] === 'accept'
    }
  })
}
const subscribeLoading = ref(false)
// 订阅消息
const handleSubscribe = (showToast = true) => {
  if (subscribeLoading.value) {
    // console.log('请勿重复点击');
    return
  }
  subscribeLoading.value = true

  const tmplId = cleanData.value.template_id
  uni.requestSubscribeMessage({
    tmplIds: [tmplId],
    success(res) {
      // console.log('success', res);
      /*
        接口调用成功时errMsg值为'requestSubscribeMessage:ok'
        当用户勾选了订阅面板中的“总是保持以上选择，不再询问”时，模板消息才会被添加到用户的小程序设置页
        'accept'表示用户同意订阅该条id对应的模板消息，
        'reject'表示用户拒绝订阅该条id对应的模板消息，
        'ban'表示已被后台封禁，
        'filter'表示该模板因为模板标题同名被后台过滤
        */
      if (showToast && res.errMsg === 'requestSubscribeMessage:ok' && res[tmplId] === 'accept') {
        uni.showToast({
          title: '订阅成功',
          icon: 'none',
        })
      }
    },
    fail(res) {
      if (showToast) {
        uni.showToast({
          title: '订阅失败' + res.errCode,
          icon: 'none',
        })
      }
    },
    complete(res) {
      // console.log('complete', res);
      subscribeLoading.value = false
      getSubscribeSetting()
    }
  })
}

const cleanData = ref<Record<string, any>>({
  template_id: '', // jFZX0ef47cR6kSuTGuqAEveXUuhbd4nCPpweEHc-BZQ
  today_clean: 0,
  treat_clean: 0,
})
const cleanList = ref<Record<string, any>[]>([])

// 获取数据
function getData() {
  const params = {
    bus_id: curOptions.bus_id,
    user_id: curOptions.user_id,
  }
  return http.post('Qrcabinet/CleanCabinetList', params).then((res) => {
    if (res.errorcode === 0) {
      const {
        cabinet_clean_list,
        ...rest
      } = res.data

      cleanData.value = rest
      cleanList.value = cabinet_clean_list
      return res
    }
  })
}

const isAsk = ref(false) // 本次进入页面是否被动询问过订阅
// 开柜
function handleOpen(item, index, subItem, subIndex) {
  http.post('Qrcabinet/cleanOpen', {
    bus_id: curOptions.bus_id,
    user_id: curOptions.user_id,
    device_id: item.device_id,
    cabinet_no: subItem.cabinet_no,
  }).then((res) => {
    if (res.errorcode === 0) {
      uni.showToast({
        title: res.errormsg,
        icon: 'none',
      })

      // cleanList.value[index].cabinet_num[subIndex].isOpen = true

      if (!isAsk.value && !isSubscribe.value) {
        handleSubscribe()
        isAsk.value = true
      }
    }
  })
}

// 完成清洁
function handleComplete(item, index, subItem, subIndex) {
  // uni.showModal({
  //   title: '提示',
  //   content: '确定完成清洁？',
  //   success(res) {
  //     if (res.confirm) {
        http.post('Qrcabinet/CompleteClean', {
          bus_id: curOptions.bus_id,
          user_id: curOptions.user_id,
          device_id: item.device_id,
          cabinet_no: subItem.cabinet_no,
        }).then((res) => {
          if (res.errorcode === 0) {
            uni.showToast({
              title: res.errormsg,
              icon: 'none',
            })

            /*
            要求每次完成刷新一下，重新调接口
            cleanList.value[index].cabinet_num.splice(subIndex, 1)
            if (!cleanList.value[index].cabinet_num.length) {
              cleanList.value.splice(index, 1)
            }
            cleanData.value.today_clean += 1
            cleanData.value.treat_clean -= 1 */
            getData()

            handleSubscribe(false)

          }
        })
  //     }
  //   }
  // })
}


</script>

<style lang="scss" scoped>
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.locker-cleans-page {
  position: relative;
  height: 100%;
  font-size: 24rpx;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/clean-top-bg.png') left top/100% no-repeat;

  .locker-cleans-container {
    // overflow-y: auto;
    padding: 30rpx 20rpx;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
}

.subscribe-btn {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  width: 126rpx;
  height: 50rpx;
  line-height: 48rpx;
  color: #fff;
  background: $theme-text-color-other;
  border-radius: 50rpx;
  image {
    margin-right: 8rpx;
    width: 26rpx;
    height: 26rpx;
  }
}

.count-wrap {
  margin: 28rpx 10rpx 0;
  .locker-days-row {
    font-size: 30rpx;
    font-weight: bold;
    .days-num {
      font-size: 48rpx;
      color: $theme-text-color-other;
    }
  }
  .locker-count-box {
    display: flex;
    margin-top: 64rpx;
    .locker-count-item {
      display: flex;
      flex-direction: column;
      padding-right: 40rpx;
      margin-right: 39rpx;
      text-align: center;
      border-right: 1rpx solid #E7E7E7;
      &:last-child {
        margin-right: 0;
        padding-right: 0;
        border-right: 0;
      }
      .type-name {
        font-size: 26rpx;
      }
    }
    .count-text {
      margin-top: 11rpx;
      font-weight: bold;
      line-height: 1;
      .count {
        font-size: 64rpx;
      }
    }
  }
}

.list-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  margin-top: 300rpx;
  padding: 0rpx 20rpx 24rpx;
  .nodata {
    padding-bottom: 50rpx;
  }
  .more {
    display: flex;
    align-items: center;
    justify-content: center;
    text {
      font-size: 24rpx;
      font-weight: 400;
      line-height: 30rpx;
    }
    image {
      height: 20rpx;
      width: 12rpx;
      margin-left: 12rpx;
    }
  }

  .locker-box {
    margin-bottom: 20rpx;
    padding: 0 20rpx 28rpx;
    font-size: 26rpx;
    border-radius: 12rpx;
    background-color: #F6F6F8;
  }

  .locker-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14rpx 0;
    min-height: 80rpx;
    font-weight: bold;
    box-sizing: border-box;

    .unclearn-num {
      min-width: 150rpx;
      height: 46rpx;
      line-height: 44rpx;
      text-align: center;
      color: #FF3942;
      background-color: #FFE5E1;
      border-radius: 46rpx;
    }
  }

  .locker-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 20rpx;
    padding: 40rpx 30rpx;
    background-color: #fff;
    border-radius: 10rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .locker-num {
      font-weight: bold;
      font-size: 30rpx;
      line-height: 1;
    }

    .return-time {
      margin: 30rpx 0;
    }
  }

  .buttons {
    justify-content: space-between;
    .the-page-min {
      width: 280rpx;
      height: 70rpx;
      font-size: 26rpx;
      background: #A1EA2B;;
      border-radius: 70rpx;
    }

    .friend {
      background: #ff7427;
    }
  }
}

</style>
