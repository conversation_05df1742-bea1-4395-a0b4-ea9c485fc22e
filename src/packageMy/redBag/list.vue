<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
// import { useLogin } from '@/hooks/useLogin'

const userStore = useUserStore()
// const { checkLogin } = useLogin()

// list
const list0 = ref([]) as any
const ref0 = ref()
const getList = (pageNo, pageSize) => {
  http
    .get('/Bonus/getList', {
      bus_id: userStore.userInfoBusId,
      // user_id: userStore.userInfoUserId,
      // openid: userStore.userInfo.openid,
      page_no: pageNo,
      page_size: pageSize,
      loading: true,
    })
    .then((res) => {
      const list = res.data.list
      ref0.value.complete(list)
    })
    .catch(() => {
      ref0.value.complete(false)
    })
}
const handleDetail = (id) => {
  uni.navigateTo({
    url: `/packageMy/redBag/open?bonusId=${id}`,
  })
}

// life hook
onLoad((options) => {})
onShow(() => {
  // checkLogin(true, userStore.userInfoBusId).then(() => {
  getList(1, 10)
  // })
})
</script>

<template>
  <div class="box">
    <!-- :show-loading-more-no-more-view="list0.length > 10 ? true : false" -->
    <z-paging
      ref="ref0"
      v-model="list0"
      class="list"
      :loading-more-enabled="false"
      :show-loading-more-no-more-view="false"
      empty-view-text="暂无数据"
      :fixed="false"
      :auto="false"
      @query="getList"
    >
      <div v-for="(item, index0) in list0" :key="index0" class="card">
        <div class="card-header">
          <div class="avatar">
            <img class="photo" :src="`../imageMy/store-${(index0 % 3) + 1}.png`" />
          </div>
          <div class="name">{{ item.bus_name }}</div>
        </div>
        <div class="card-buddy" @click="handleDetail(item.bonus_id)">
          <text class="title">{{ item.theme }}</text>
        </div>
      </div>
    </z-paging>
  </div>
</template>

<style lang="scss" scoped>
@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.box {
  .list {
    position: absolute;
    top: 0rpx;
    left: 0;
    right: 0;
    bottom: 60rpx;
    background-color: white;

    .card {
      display: flex;
      flex-direction: row;
      width: 644rpx;
      height: 190rpx;
      margin: 20rpx auto;

      .card-header {
        display: flex;
        flex-direction: column;

        .avatar {
          width: 100rpx;
          height: 100rpx;

          .photo {
            width: 100%;
            height: 100%;
          }
        }

        .name {
          margin-top: 10rpx;
          width: 100rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          font-size: 24rpx;
          font-weight: 400;
          color: #000000;
        }
      }

      .card-buddy {
        margin-left: 20rpx;
        width: 100%;
        display: flex;
        flex-direction: column;
        background-image: url('../imageMy/red-bag-panel.png');
        background-size: 520rpx 140rpx;
        background-repeat: no-repeat;

        .title {
          font-size: 30rpx;
          font-weight: bold;
          color: #ffffff;
          position: absolute;
          margin-top: 50rpx;
          margin-left: 160rpx;
        }
      }
    }
  }
}
</style>
