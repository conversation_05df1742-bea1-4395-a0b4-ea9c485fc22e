<template>
  <div class="box">
    <div class="options">
      <!-- <div class="action" @click="handleBack">返回</div>
      <div class="action" @click="handleLogPage">红包记录</div> -->
    </div>
    <div class="avatar">
      <img class="photo" :src="info.logo" />
    </div>
    <div class="name">{{ info.bus_name }}</div>
    <div class="title">
      <img class="icon" src="../imageMy/bonus.png" />
      <text class="label">{{ info.theme }}</text>
    </div>
    <div class="price" v-if="info.type == 1">
      <text class="amount">{{ info.definite }}</text>
      <text class="unit">元</text>
    </div>
    <div class="price" v-else-if="info.type == 5">
      <text class="amount">{{ info.definite }}</text>
      <text class="unit">积分</text>
    </div>
    <div class="price" v-else>
      <text class="unit">{{ info.definite_name }}</text>
    </div>

    <div v-if="info.status == 1" class="describe">领取成功</div>
    <div v-else-if="info.exist_experience_card == 1 && (info.type == 2 || info.type == 4)" class="describe">仅能送朋友使用</div>

    <div v-if="info.status == 0 && info.exist_experience_card == 0" class="btn receive" @tap="activateGift">领取</div>
    <div v-else-if="info.status == 1 && info.type == 3" class="btn receive" @tap="activateCoupon">查看详情</div>
    <div v-else-if="info.status == 1 && info.type == 5" class="btn receive" @tap="activateScore">查看详情</div>
    <div v-else-if="info.status == 1 && (info.type == 2 || info.type == 4)" class="btn receive" @tap="activateGiftDetail">查看详情</div>
    <div v-else-if="info.status == 2 && (info.type == 2 || info.type == 4) && info.exist_experience_card == 1" class="describe">已赠送</div>
    <button v-else-if="info.status == 0 && (info.type == 2 || info.type == 4) && info.exist_experience_card == 1" class="btn receive" open-type="share">送朋友</button>

    <div class="btn share" @tap="handleShare">分享</div>

    <!-- <uni-popup ref="showPopup">
      <view class="bonus-modal">
        <image class="bg" src="../imageMy/bonus-modal.png" />
        <view class="tit">{{ modalInfo.tit }}</view>
        <view class="con">{{ modalInfo.con }}</view>
        <button class="sure-btn" @tap="closePopup">确定</button>
      </view>
    </uni-popup> -->
  </div>
</template>

<script setup lang="ts" name="result">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import { useThemeStore } from '@/store/theme'

const userStore = useUserStore()
const { checkLogin } = useLogin()
const themeStore = useThemeStore()

const awardId = ref('')
const info = ref({}) as any
const getInfo = () => {
  const openid = userStore.userInfo.openid
  if (!openid) {
    uni.showToast({ title: '信息有误', image: '/static/img/danger.png' })
    return false
  }
  http
    .get('/Bonus/lotteryResult', {
      award_id: awardId.value,
      openid: openid,
      loading: true,
    })
    .then((res) => {
      if (res.errorcode == 0) {
        info.value = res.data.info
      }
    })
}

const isShowMerchantMode = computed(() => themeStore.isShowMerchantMode)
const checkLoginStatus = async () => {
  uni.setStorageSync('source_name', '营销红包')
  if (isShowMerchantMode.value) {
    await checkLogin(true)
  } else {
    await checkLogin(true, info.value.bus_id)
  }
}
const activateGift = () => {
  checkLoginStatus()
  if (!userStore.userInfo.user_id) {
    return false
  }
  http
    .post('/Bonus/activateGift', {
      award_id: awardId.value,
      openid: userStore.userInfo.openid,
      loading: true,
    })
    .then((res) => {
      if (res.errorcode == 0) {
        // if (info.value.type == 1) {
        //   modalInfo.value = {
        //     tit: '提现成功！',
        //     con: '预计1-5个工作日发放到您的微信零钱中。',
        //   }
        // } else {
        //   modalInfo.value = {
        //     tit: '激活成功！',
        //     con: '请在“我的-会员卡”中查看。',
        //   }
        // }
        // openPopup()
        uni.showToast({
          title: '激活成功！',
          success() {
            getInfo()
          }
        })
      } else if (res.errorcode == 60049) {
        // 直接跳登录页面
        uni.navigateTo({
          url: '/pages/login?hasChecked=true&navigateBack=true',
        })
      }
    }).catch((err) => {
      if (err.errorcode == 60049) {
        // 直接跳登录页面
        uni.navigateTo({
          url: '/pages/login?hasChecked=true&navigateBack=true',
        })
      }
    })
}
const activateScore = async () => {
  checkLoginStatus()
  uni.navigateTo({
    url: '/pages/my/point',
  })
}
const activateCoupon = async () => {
  checkLoginStatus()
  uni.navigateTo({
    url: '/pages/my/coupon',
  })
}
const activateGiftDetail = async () => {
  checkLoginStatus()
  uni.navigateTo({
    url: '/pages/my/card',
  })
}
const handleShare = async () => {
  uni.navigateTo({ url: `/packageMy/redBag/share?bonusId=${info.value.bonus_id}` })
}

// const modalInfo = ref({
//   tit: '提现成功！',
//   con: '预计1-5个工作日发放到您的微信零钱中。',
// })
// const showPopup = ref()
// async function openPopup() {
//   if (showPopup.value) {
//     showPopup.value.open()
//   } else {
//     await nextTick()
//     showPopup.value.open()
//   }
// }
// async function closePopup() {
//   if (showPopup.value) {
//     showPopup.value.close()
//   } else {
//     await nextTick()
//     showPopup.value.close()
//   }
// }

onLoad((options) => {
  awardId.value = options.awardId || ''
})

onShow(async () => {
  getInfo()
})

onReady(() => {
  setTimeout(() => {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#DC5E45',
    })
  }, 1000)
})
onShareAppMessage((options) => {
  if (options.from === 'button') {
    return {
      title: '送您一张体验卡',
      imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/member/image/share_gift_card.png',
      path: `/packageMy/redBag/giftCard?awardId=${awardId.value}&bus_id=${info.value.bus_id}&fromOpenId=${userStore.userInfo.openid}&from=share`,
    }
  }
})
</script>

<style lang="scss" scoped>
.box {
  min-height: 100%;
  background-color: #dc5e45;
  display: flex;
  flex-direction: column;
  align-items: center;

  .options {
    width: 100%;
    height: 86rpx;
    /* display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .action {
      font-size: 24rpx;
      color: #eace9a;
      margin: 0 40rpx;
    } */
  }

  .avatar {
    width: 190rpx;
    height: 190rpx;
    
    .photo {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 4rpx solid #ffffff;
      box-sizing: border-box;
    }
  }

  .name {
    font-size: 36rpx;
    font-weight: 400;
    color: #ffffff;
    line-height: 50rpx;
    margin-top: 12rpx;
  }

  .title {
    margin-top: 80rpx;
    display: flex;
    align-items: center;

    .icon {
      width: 36rpx;
      height: 36rpx;
    }

    .label {
      font-size: 36rpx;
      font-weight: bold;
      color: #eace9a;
      line-height: 50rpx;
      margin-left: 10rpx;
    }
  }

  .price {
    margin-top: 80rpx;

    .amount {
      font-size: 100rpx;
      font-family: Roboto, Roboto;
      font-weight: 600;
      color: #eace9a;
      line-height: 50rpx;
    }

    .unit {
      font-size: 36rpx;
      font-weight: 400;
      color: #eace9a;
      line-height: 50rpx;
      margin-left: 10rpx;
    }
  }

  .describe {
    font-size: 30rpx;
    font-weight: 400;
    color: #e9ce99;
    line-height: 50rpx;
    margin-top: 40rpx;
  }

  .btn {
    width: 380rpx;
    height: 80rpx;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    font-size: 30rpx;
    line-height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .receive {
    margin-top: 160rpx;
    background: #eace9a;
    font-weight: 400;
    color: #000000;
  }

  .share {
    margin-top: 30rpx;
    border: 2rpx solid #eace9a;
    font-weight: bold;
    color: #eace9a;
  }
}

.bonus-modal {
  width: 507rpx;
  height: 385rpx;
  color: #313131;
  position: relative;
  text-align: center;
  .bg {
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .tit {
    font-size: 36rpx;
    font-weight: bold;
    padding: 50rpx 0 30rpx;
  }
  .con {
    font-size: 22rpx;
    color: #888;
    padding: 0 37rpx;
    line-height: 1.25;
    text-align: left;
  }
  .sure-btn {
    position: absolute;
    left: 50%;
    bottom: 50rpx;
    transform: translateX(-50%);
    width: 230rpx;
    height: 72rpx;
    line-height: 72rpx;
    background: #eacf9a;
    color: #313131;
    font-weight: bold;
    font-size: 30rpx;
    border-radius: 10rpx;
  }
}
</style>
