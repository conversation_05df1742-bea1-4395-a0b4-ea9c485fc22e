<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// tabs
const tabIndex = ref(0)
const handleTabClick = (tab) => {
  tabIndex.value = tab
  getList(1, 10)
}

// list
const list0 = ref([]) as any
const list1 = ref([]) as any
const ref0 = ref()
const ref1 = ref()
const getList = (pageNo, pageSize) => {
  http
    .get('/Bonus/getAwardLog', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      openid: userStore.userInfo.openid,
      type: tabIndex.value === 1 ? 0 : 1,
      page_no: pageNo,
      page_size: pageSize,
      loading: true,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        const list = res.data
        if (tabIndex.value === 0) {
          ref0.value.complete(list)
        } else if (tabIndex.value === 1) {
          ref1.value.complete(list)
        }
      }
    })
    .catch(() => {
      if (tabIndex.value === 0) {
        ref0.value.complete(false)
      } else if (tabIndex.value === 1) {
        ref1.value.complete(false)
      }
    })
}

const handleGet = (id) => {
  uni.navigateTo({
    url: `/packageMy/redBag/result?awardId=${id}`,
  })
}

// life hook
onLoad((options) => {})
onShow(() => {
  getList(1, 10)
})
</script>

<template>
  <div class="box">
    <div class="tab-box">
      <div class="tab" :class="{ active: tabIndex === 0 }" @click="handleTabClick(0)">
        <div class="label">已领取</div>
        <div class="line"></div>
      </div>
      <div class="tab" :class="{ active: tabIndex === 1 }" @click="handleTabClick(1)">
        <div class="label">未领取</div>
        <div class="line"></div>
      </div>
    </div>
    <!-- :show-loading-more-no-more-view="list0.length > 10 ? true : false" -->
    <z-paging
      v-if="tabIndex === 0"
      ref="ref0"
      v-model="list0"
      class="list"
      :loading-more-enabled="false"
      :show-loading-more-no-more-view="false"
      empty-view-text="暂无数据"
      :fixed="false"
      :auto="false"
      @query="getList"
    >
      <div v-for="(item, index0) in list0" :key="index0" class="card">
        <div class="name">{{ item.bus_name }}</div>
        <!-- 奖品类型 1现金 2体验卡 3折扣券 4体验课 5积分 -->
        <div v-if="item.type === 1" class="price">+{{ item.definite }}元</div>
        <div v-else-if="item.type === 5" class="price">+{{ item.definite }}积分</div>
        <div v-else class="price">{{ item.definite_name }}</div>
        <div class="date">{{ item.create_time }}</div>
        <!-- 0未领奖 1已领奖(卡就是发放) -->
        <div v-if="item.status === 0" class="action">未领取</div>
        <div v-else class="action">已领取</div>
      </div>
    </z-paging>
    <z-paging
      v-if="tabIndex === 1"
      ref="ref1"
      v-model="list1"
      class="list"
      style="bottom: 0px"
      :loading-more-enabled="false"
      :show-loading-more-no-more-view="false"
      empty-view-text="暂无数据"
      :fixed="false"
      :auto="false"
      @query="getList"
    >
      <div v-for="(item, index1) in list1" :key="index1" class="card">
        <div class="name">{{ item.bus_name }}</div>
        <!-- 奖品类型 1现金 2体验卡 3折扣券 4体验课 5积分 -->
        <div v-if="item.type === 1" class="price">+{{ item.definite }}元</div>
        <div v-else-if="item.type === 5" class="price">+{{ item.definite }}积分</div>
        <div v-else class="price">{{ item.definite_name }}</div>
        <div class="date">{{ item.create_time }}</div>
        <div class="action">
          <div class="button" @click="handleGet(item.id)">领取</div>
        </div>
      </div>
    </z-paging>
  </div>
</template>

<style lang="scss" scoped>
@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.box {
  .tab-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    height: 88rpx;

    .tab {
      font-size: 30rpx;
      font-weight: 400;
      color: #000000;

      .label {
        padding: 14rpx 27rpx;
      }
    }

    .active {
      font-size: 36rpx;
      font-weight: bold;
      color: #03080e;
      @include center;
      flex-direction: column;

      .line {
        width: 38rpx;
        height: 6rpx;
        background: $uni-color-success;
        border-radius: 3rpx;
      }
    }
  }

  .list {
    position: absolute;
    top: 88rpx;
    left: 0;
    right: 0;
    bottom: 60rpx;
    background-color: white;

    .card {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      grid-template-areas: 'name price' 'date action';
      width: 686rpx;
      height: 136rpx;
      border-bottom: 2rpx solid #f6f6f8;
      margin: 0 auto;

      .name {
        grid-area: name;
        font-size: 30rpx;
        font-weight: bold;
        color: #000000;
        line-height: 35rpx;
        display: flex;
        align-items: flex-end;
      }

      .price {
        grid-area: price;
        font-size: 30rpx;
        /* font-weight: bold; */
        color: #000000;
        line-height: 35rpx;
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }

      .date {
        grid-area: date;
        font-size: 24rpx;
        font-weight: 400;
        color: #7d7d7d;
        line-height: 28rpx;
        display: flex;
        align-items: center;
      }

      .action {
        grid-area: action;
        font-size: 24rpx;
        font-weight: 400;
        color: #ff7427;
        line-height: 28rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .action > .button {
        width: 144rpx;
        height: 50rpx;
        background: #ff7427;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 26rpx;
        font-weight: bold;
        color: #ffffff;
        line-height: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
