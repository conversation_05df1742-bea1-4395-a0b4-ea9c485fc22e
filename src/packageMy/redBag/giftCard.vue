<template>
  <PageExperienceCard :card-info="cardInfo" position="您的好友" @get-card="handleSubmitClick" />
</template>

<script setup lang="ts" name="expCardConfirm">
import PageExperienceCard from '@/pages/card/components/PageExperienceCard.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'

const userStore = useUserStore()
const { checkLogin } = useLogin()
const cardInfo = ref({}) as any
const data = reactive({
  awardId: '', // 红包开出体验卡的获奖ID
  // newAwardId: '', // 红包定向体验卡的新获奖ID
  shareBusId: '',
  fromOpenId: '',
  // shareUserId: '',
  // ucid: '', // 魅力值兑换的体验卡
  // el_id: '0',
})

onLoad((options) => {
  data.awardId = options.awardId || ''
  data.shareBusId = options.bus_id || ''
  data.fromOpenId = options.fromOpenId || ''
  getInfo()
})

function getInfo() {
  http
    .get('/Bonus/giftGivingDetail', {
      award_id: data.awardId
    })
    .then((res) => {
      if (res.errorcode === 0) {
        // cardInfo.value = res.data?.info
        cardInfo.value = res.data
        cardInfo.value.validity = res.data.end_time === 0 ? '永久有效' : res.data.end_time + '天'
        cardInfo.value.bu_name = res.data.bus_name
        cardInfo.value.status = res.data.status === 0 ? 0 : 99
      }
    })
}

const handleSubmitClick = () => {
  // uni.setStorageSync('introducer_id', data.shareUserId)
  uni.setStorageSync('source_name', '营销红包')

  checkLogin(true, data.shareBusId).then((userInfo) => {
    http
      .post('/Bonus/giftGivingActivate', {
        openid: userStore.userInfo.openid,
        award_id: data.awardId,
        from_openid: data.fromOpenId
      })
      .then((res) => {
        uni.showToast({
          title: '领取成功！',
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/my/card',
          })
        }, 1000)
      })
  })
}
</script>
