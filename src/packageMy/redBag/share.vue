<template>
  <view class="bonuser-box">
    <view class="con">
      <view class="top-wrap">
        <image class="logo" :src="bonusInfo.logo" />
        <text class="logo-name">{{ bonusInfo.bus_name }}</text>
        <span class="bounuses-name">{{ bonusInfo.theme }}</span>
      </view>
      <view class="open-wrap">
        <image class="bg" src="../imageMy/open-bg.jpg" />
        <image class="btn-open" :src="bonusInfo.qrcode" />
        <image class="top" src="../imageMy/bonus-top.png" />
      </view>
      <view class="bottom-wrap">
        <button class="btn" hover-class="none" open-type="share">转发到好友或群聊</button>
        <button class="btn btn-oth" @tap="saveDom" hover-class="none">生成分享图</button>
      </view>
    </view>
    <canvas style="width: 750px; height: 1239px" :style="{ display: canvasShow }" canvas-id="myCanvas"></canvas>
  </view>
</template>
<script setup lang="ts" name="share">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
// import { useLogin } from '@/hooks/useLogin'
import { shareCanvasInPhoto } from '@/utils'

const userStore = useUserStore()
// const { checkLogin } = useLogin()

const bonusId = ref('')
const bonusInfo = ref({}) as any
onLoad((options) => {
  bonusId.value = options.bonusId || ''
  getInfo(options.bonusId)
})

onReady(() => {
  setTimeout(() => {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#d8593f',
    })
  }, 1000)
})

onShareAppMessage((options) => {
  return {
    title: bonusInfo.value.title,
    path: `/packageMy/redBag/open?bus_id=${bonusInfo.value.bus_id}&bonusId=${bonusId.value}&fromOpenid=${userStore.userInfo.openid}&from=share`,
  }
})

const getInfo = (id) => {
  const openid = userStore.userInfo.openid
  if (!openid) {
    uni.showToast({ title: '信息有误', image: '/static/img/danger.png' })
    return false
  }
  http
    .get('/Bonus/getShareInfo', {
      bonus_id: id,
      from_openid: openid,
      loading: true,
    })
    .then((res) => {
      if (res.errorcode == 0) {
        bonusInfo.value = res.data.info
      }
    })
}

const canvasShow = ref('none')
const saveDom = () => {
  uni.showLoading({ mask: true })
  const _this = this
  canvasShow.value = 'block'
  const infoObj = bonusInfo.value
  const ctx = uni.createCanvasContext('myCanvas')
  ctx.drawImage('../imageMy/bonus-bg.jpg', 0, 0, 750, 1239)
  const busLogoImg = infoObj.logo
  const busCodeImg = infoObj.qrcode
  uni.downloadFile({
    url: busLogoImg,
    success: function (res) {
      if (res.statusCode === 200) {
        ctx.drawImage(res.tempFilePath, 308, 100, 130, 130)
        uni.downloadFile({
          url: busCodeImg,
          success: function (res) {
            if (res.statusCode === 200) {
              ctx.drawImage(res.tempFilePath, 285, 497, 180, 180)
              ctx.setFillStyle('#fce1ac')
              ctx.setFontSize(30)
              ctx.setTextAlign('center')
              ctx.fillText(infoObj.bus_name, 375, 268)
              ctx.setFontSize(48)
              ctx.fillText(infoObj.theme, 375, 360)
              ctx.setFontSize(28)
              ctx.fillText('长按开红包', 375, 900)
              ctx.drawImage('../imageMy/bonus-top.png', 260, 480, 230, 212)
              ctx.draw(false, function () {
                shareCanvasInPhoto({
                  width: 750,
                  height: 1239,
                  id: 'myCanvas',
                  callBack() {
                    canvasShow.value = 'none'
                  },
                })
              })
            }
          },
          fail: function () {
            uni.showToast({ title: '保存小程序码失败！', image: '/static/img/danger.png' })
          },
        })
      }
    },
    fail: function () {
      uni.showToast({ title: '保存logo失败！', image: '/static/img/danger.png' })
    },
  })
}
</script>
<style lang="scss">
page {
  height: 100%;
}
.bonuser-box {
  height: 100%;
  overflow: hidden;
  background-color: #cd553c;
  color: #fce1ac;
  text-align: center;

  .con {
    height: 100%;
  }
  .top-wrap {
    background-color: #d8593f;
    overflow: hidden;
  }

  .logo {
    display: block;
    border-radius: 10rpx;
    width: 130rpx;
    height: 130rpx;
    margin: 90rpx auto 22rpx;
  }

  .logo-name {
    display: block;
    font-size: 30rpx;
    margin-bottom: 76rpx;
  }

  .bounuses-name {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 85rpx;
  }
}

.open-wrap {
  position: relative;
  margin-bottom: 38px;
  font-size: 26rpx;

  .bg {
    width: 100%;
    height: 212rpx;
  }

  .top {
    width: 230rpx;
    height: 212rpx;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .btn-open {
    width: 180rpx;
    height: 180rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -4rpx;
    transform: translate(-50%, -50%);
  }
}

.bottom-wrap {
  font-size: 26rpx;
  padding-top: 90rpx;
  .btn {
    width: 368rpx;
    height: 92rpx;
    background: #eacf9a;
    color: #cd533c;
    font-size: 34rpx;
    font-weight: bold;
    border-radius: 10rpx;
    margin: 0 auto 22rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .btn-oth {
    border: 2rpx solid #eacf9a;
    border-radius: 10rpx;
    color: #eacf9a;
    background: #cd553c;
  }
  .des {
    font-size: 24rpx;
  }
}
</style>
