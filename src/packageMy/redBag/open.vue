<template>
  <view class="bonuser-box">
    <navigator class="lef-nav" open-type="switchTab" url="/pages/index/index" hover-class="none">返回</navigator>
    <navigator class="rig-nav" url="/packageMy/redBag/log" hover-class="none">红包记录</navigator>
    <view class="top-wrap">
      <image class="logo" :src="bonusInfo.logo" />
      <text class="logo-name">{{ bonusInfo.bus_name }}</text>
      <span class="bounuses-name"><img style="width: 36rpx; height: 36rpx; margin-right: 12rpx;" src="../imageMy/bonus.png" />{{ bonusInfo.theme }}</span>
    </view>
    <view class="open-wrap">
      <image class="bg" src="../imageMy/open-bg.jpg" />
      <button
        open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
        hover-class="none"
        class="btn-open"
        v-if="(!bonusInfo.is_over && !bonusInfo.phone) || ((awardId || couponId) && !bonusInfo.phone)"
      >
        <image v-if="awardId || couponId" src="../imageMy/open2.png" />
        <image src="../imageMy/open.png" v-else />
      </button>
      <button
        @tap="openBonus"
        hover-class="none"
        class="btn-open"
        v-if="(!bonusInfo.is_over && bonusInfo.phone) || ((awardId || couponId) && bonusInfo.phone)"
      >
        <image v-if="awardId || couponId" src="../imageMy/open2.png" />
        <image src="../imageMy/open.png" v-else />
      </button>
      <span class="open-text" v-if="bonusInfo.is_over && !awardId && !couponId"> 手慢了，红包已领完 </span>
    </view>
    <view class="bottom-wrap" v-if="!bonusInfo.is_over">
      <span v-if="bonusInfo.number">您还有{{ bonusInfo.number }}次机会哦</span>
      <span v-else>分享增加机会</span>
      <navigator :url="`/packageMy/redBag/share?bonusId=${bonusId}`" hover-class="none">
        <button class="btn" v-if="bonusInfo.single_share">分享{{ bonusInfo.single_share }}次，开1次</button>
        <button class="btn" v-else>分享给好友</button>
      </navigator>
      <view class="des">{{ bonusInfo.desc }}</view>
    </view>
  </view>
  <!-- 位置权限弹窗 -->
  <!-- <LocationPermissionDialog :show="showLocationDialog" direction="down" :distance="80" @close="showLocationDialog = false" /> -->
</template>

<script setup lang="ts" name="open">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
// import { useLogin } from '@/hooks/useLogin'
// import LocationPermissionDialog from '@/components/LocationPermissionDialog.vue'

const userStore = useUserStore()
// const { checkLogin } = useLogin()

const option = ref<any>({})
onLoad((options) => {
  option.value = options || {}
})

const bonusId = ref('')
const fromOpenid = ref('')
const awardId = ref('')
const couponId = ref('')
onShow(async () => {
  let curOptions = option.value
  if (typeof option.value.scene !== 'undefined') {
    curOptions = await getScene(option.value.scene)
  }
  bonusId.value = curOptions.bonusId
  fromOpenid.value = curOptions.fromOpenid || ''
  awardId.value = curOptions.awardId || '' //定向转赠的体验卡
  couponId.value = curOptions.couponId || '' //定向转赠的折扣券
  getInfo()
})

onReady(() => {
  setTimeout(() => {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#d8593f',
    })
  }, 1000)
})

// event handler
const latitude = ref('')
const longitude = ref('')
// const showLocationDialog = ref(false)
const isLocation = (info) => {
  if (info.open_geo) {
    return userStore
      .getLocationInfo(true)
      .then((res: any) => {
        // showLocationDialog.value = userStore.getShowLocationDialog()
        latitude.value = res.latitude
        longitude.value = res.longitude
        uni.setStorageSync('latitude', res.latitude)
        uni.setStorageSync('longitude', res.longitude)
      })
      .catch(() => {
        latitude.value = ''
        longitude.value = ''
      })
  }
}

const getPhoneNumber = (e) => {
  if (e.detail.iv) {
    const accountInfo = uni.getAccountInfoSync()
    const appid = accountInfo.miniProgram.appId
    let phoneInfo = {
      phone_code: e.detail.code,
      appid: appid
    }
    openBonus(phoneInfo)
  } else {
    uni.showToast({
      title: '手机授权失败',
      image: '../imageMy/danger.png',
    })
  }
}

const bonusInfo = ref('') as any
const openBonus = async (phoneInfo) => {
  let curBonusInfo = bonusInfo.value
  if (!curBonusInfo) {
    uni.showToast({ title: '等待数据返回！', image: '../imageMy/danger.png' })
    return false
  }

  let info = {
    bonus_id: bonusId.value,
    award_id: awardId.value,
    coupon_receive_id: couponId.value,
    openid: userStore.userInfo.openid,
    from_openid: fromOpenid.value,
    phone: curBonusInfo.phone?curBonusInfo.phone:'',
    loading: true,
  } as any

  if (phoneInfo.phone_code) {
    // const accountInfo = uni.getAccountInfoSync()
    // const appid = accountInfo.miniProgram.appId
    // phoneInfo.phone_code = jsCode.value
    // phoneInfo.appid = appid
    info = {
      ...info,
      ...phoneInfo,
    }
  }
  await isLocation(curBonusInfo)

  // if (showLocationDialog.value) {
  //   uni.showToast({ title: '请先授权位置信息', icon: 'none' })
  //   userStore.setShowLocationDialog(true)
  //   return false
  // }

  if (curBonusInfo.open_geo) {
    info = {
      ...info,
      lng: longitude.value,
      lat: latitude.value,
    }
  }
  http.post('/Bonus/bonusLottery', info).then((res) => {
    if (res.errorcode == 0) {
      if (couponId.value != 'undefined' && couponId.value != '0' && couponId.value) {
        //定向转赠的折扣券
        uni.navigateTo({
          url: '/pages/my/coupon'
          // url: `/pages/my/couponDetail?from=share&busId=${uni.getStorageSync('bus_id')}&cBusId=${
          //   bonusInfo.value.bus_id
          // }&couponId=${res.data.coupon_receive_id}&openId=${userStore.userInfo.openid}&fromOpenid=${
          //   fromOpenid.value
          // }`,
        })
      } else if (awardId.value) {
        //定向转赠的体验卡
        uni.navigateTo({
          url: '/pages/my/card'
          // url: `/pages/bonuses/giftCard?bus_id=${bonusInfo.value.bus_id}&awardId=${awardId.value}&newAwardId=${res.data.lottery_id}&from=share`,
        })
      } else {
        uni.navigateTo({
          url: `/packageMy/redBag/result?awardId=${res.data.lottery_id}`,
        })
      }
    }
  })
}

const jsCode = ref('')
const getInfo = async () => {
  const openid = userStore.userInfo.openid
  if (!openid) {
    const login: any = await uni.login({
      onlyAuthorize: true,
    })
    if (login.errMsg === 'login:ok') {
      jsCode.value = login.code
    } else {
      uni.showToast({ title: '网络错误', image: '../imageMy/danger.png' })
    }
  }
  http
    .get('/Bonus/getInfo', {
      bonus_id: bonusId.value,
      openid: openid,
      loading: true,
      js_code: jsCode.value,
    })
    .then(async (res) => {
      if (res.errorcode == 0) {
        const loginNew: any = await uni.login({
          onlyAuthorize: true,
        }) //防止手机号解密session_key错误先拿code
        jsCode.value = loginNew.code
        bonusInfo.value = res.data.info
      }
    })
}

const queryParser = (search) => {
  return JSON.parse(
    '{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}',
    function (key, value) {
      return key === '' ? value : decodeURIComponent(value)
    }
  )
}
const getScene = (scene_id) => {
  return http.get('/Bonus/getSceneParam', { scene_id }).then((res) => {
    if (res.errorcode == 0) {
      return queryParser(res.data.info.param)
    }
  })
}
</script>

<style lang="scss">
$bonus-size: 212rpx;
page {
  height: 100%;
  overflow-y: scroll;
}
.bonuser-box {
  min-height: 100%;
  background-color: #cd553c;
  color: #fce1ac;
  text-align: center;
  position: relative;

  .rig-nav {
    position: absolute;
    right: 30rpx;
    top: 20rpx;
    font-size: 24rpx;
  }

  .lef-nav {
    position: absolute;
    left: 30rpx;
    top: 20rpx;
    font-size: 24rpx;
  }

  .top-wrap {
    background-color: #d8593f;
    overflow: hidden;
  }

  .logo {
    display: block;
    border-radius: 10rpx;
    width: 130rpx;
    height: 130rpx;
    margin: 90rpx auto 22rpx;
  }

  .logo-name {
    display: block;
    font-size: 30rpx;
    margin-bottom: 76rpx;
  }

  .bounuses-name {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 85rpx;
  }
}

.open-wrap {
  position: relative;
  margin-bottom: 38px;
  font-size: 26rpx;

  .bg {
    width: 100%;
    height: $bonus-size;
  }

  .btn-open {
    width: 224rpx;
    height: 224rpx;
    position: absolute;
    padding: 0;
    background-color: transparent;
    color: transparent;
    border: 0;
    font-size: 0;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    &::before,&::after{
      display: none;
    }
    image{
      width: 100%;
      height: 100%;
    }
  }

  .open-text{
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    font-size: 42rpx;
    text-align: center;
    width: 100%;
    height: $bonus-size;
    line-height: $bonus-size;
  }
}

.bottom-wrap {
  font-size: 26rpx;
  background-color: #cd553c;
  .btn {
    width: 368rpx;
    height: 92rpx;
    background: #eacf9a;
    color: #cd533c;
    font-size: 34rpx;
    font-weight: bold;
    margin: 190rpx auto 0;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .des {
    margin-top: 36rpx;
    font-size: 24rpx;
    line-height: 1.3;
    padding: 0 32rpx 10rpx;
  }
}
</style>
