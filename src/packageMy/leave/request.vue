<template>
  <view v-if="resInfo" class="box-tips">
    当前剩余请假<text class="oth-text">【{{ resInfo.suspend_surplus_number }}】</text>次，共<text class="oth-text"
      >【{{ resInfo.suspend_surplus_day }}】</text
    >天。单次最长可请<text class="oth-text">【{{ maxLeaveDay }}】</text>天
  </view>
  <view class="form-items theme-bg mt20">
    <view class="item">
      <view class="label">请假场馆</view>
      <view class="value">
        <MerchantBusPick
          v-model="loginUserInfo.bus_id"
          :has-all-bus-option="false"
          :is-filter-user-id-bus="true"
          selecte-icon-color="origin"
          :is-show-icon="false"
          @change="handleChangeBus"
        />
      </view>
    </view>
    <view class="item" @tap="handleCardChoose">
      <view class="label">请假停卡</view>
      <view class="value mr20" :class="{ 'rig-sel': params.suspend_method === '2' }">{{ cardName }}</view>
    </view>
    <view class="item">
      <view class="label">开始时间</view>
      <view class="value">
        <picker mode="date" :value="params.stop_date" :start="dateStart" end="2030-01-01" @change="bindDateChange">
          <view class="value rig-sel">
            {{ params.stop_date }}
          </view>
        </picker>
      </view>
    </view>
    <view class="item">
      <view class="label">结束时间</view>
      <view class="value">
        <picker
          mode="date"
          :value="params.active_date"
          :start="params.stop_date"
          :end="dateEnd"
          @change="bindEndDateChange"
        >
          <view class="value rig-sel">
            {{ params.active_date }}
          </view>
        </picker>
      </view>
    </view>
    <view class="item">
      <view class="label">请假费用</view>
      <view class="value mr20 oth-text">￥{{ params.amount }}</view>
    </view>
  </view>
  <view class="fixed-bottom-wrap theme-bg">
    <view class="buttons">
      <view class="lef-flex price-text mgr">
        ￥<text>{{ params.amount }}</text>
      </view>
      <button class="normal-btn rig-flex" :disabled="loginUserInfo.bus_id === ''" @tap="handlePayClick">
        {{ params.amount === 0 ? '提交' : '立即支付' }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { formatDate, addDays, dateDiff } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import _ from 'lodash'
import { useMerchant } from '@/store/merchant'
const { checkLogin } = useLogin()
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
const params = reactive({
  stop_date: '',
  active_date: '',
  suspend_method: '',
  card_user_id: '',
  amount: 0,
})
const dateStart = ref('') //时间选择起始时间
const dateEnd = ref('') //时间选择结束时间
const monthPrice = ref(0) //单个月价格
const maxLeaveDay = ref(0)
const isPayByTime = ref(true) //是否按次数支付
const resInfo = ref()
const cardName = ref('全部会员卡')
onLoad(() => {
  const tempDate = new Date()
  dateStart.value = formatDate(tempDate, 'yyyy-MM-dd')
  params.stop_date = dateStart.value
  tempDate.setDate(tempDate.getDate() + 1)
  params.active_date = formatDate(tempDate, 'yyyy-MM-dd')
})
onShow(() => {
  checkLogin()
})
function handleChangeBus(info) {
  if (info.bus_id) {
    loginUserInfo.user_id = info.user_id
    Object.assign(params, {
      suspend_method: '',
      card_user_id: '',
      amount: 0,
    })
    getInfo()
  }
}
function handleCardChoose() {
  if (params.suspend_method === '2') {
    uni.navigateTo({
      url: `/pages/my/card?chooseLeaveCard=true`,
      events: {
        acceptDataFromOpenedPage: (data) => {
          cardName.value = data.data.name
          params.card_user_id = data.data.card_user_id || ''
        },
      },
    })
  }
}
const handlePayClick = _.throttle(
  () => {
    if (params.suspend_method === '2' && !params.card_user_id) {
      uni.showToast({ title: '请选择会员卡', icon: 'none' })
      return
    }
    http
      .post('Usersuspend/doSuspend', {
        ...loginUserInfo,
        ...params,
      })
      .then((res) => {
        if (res.data && !!res.data.appId) {
          pay(res.data)
        } else {
          uni.navigateBack()
        }
      })
  },
  2000,
  true
)
function pay(info) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success: () => {
      navigateToPage()
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      uni.hideLoading()
    },
  })
}
function navigateToPage() {
  uni.showLoading({
    title: '正在生成订单',
    mask: true,
  })
  setTimeout(() => {
    uni.navigateBack({
      success: () => {
        uni.hideLoading()
      },
      fail: () => {
        uni.hideLoading()
      },
    })
  }, 3000)
}

function bindDateChange(e) {
  params.stop_date = e.detail.value
  validDate()
}
function bindEndDateChange(e) {
  params.active_date = e.detail.value
  validDate()
}
function validDate() {
  if (dateDiff(params.stop_date, params.active_date) > 0) {
    uni.showToast({ title: '结束时间必须大于开始时间', icon: 'none' })
    params.active_date = addDays(params.stop_date, 1)
  }

  if (dateDiff(addDays(params.stop_date, maxLeaveDay.value), params.active_date) < 0) {
    ///开始时间+最大可请假天数  小于 结束时间
    uni.showToast({ title: '结束时间必须小于可请假天数或单次请假时常', icon: 'none' })
    params.active_date = addDays(params.stop_date, maxLeaveDay.value)
  }
  dateEnd.value = addDays(params.stop_date, maxLeaveDay.value)
  if (!isPayByTime.value && resInfo.value?.suspend_setting.suspend_limit) {
    let diffDay = dateDiff(params.active_date, params.stop_date)
    diffDay = Math.ceil((diffDay >= 0 ? diffDay : 0) / 30)
    params.amount = Number((monthPrice.value * diffDay).toFixed(2))
  }
}
function getInfo() {
  http
    .post('Usersuspend/getSuspendInfo', { ...loginUserInfo })
    .then((res) => {
      const resData = res.data
      resInfo.value = resData

      params.suspend_method = resData.suspend_setting.member_suspend_rule.suspend_method
      if (params.suspend_method === '2') {
        ///是否会员请假 否则为单张卡
        cardName.value = '请选择会员卡'
      } else {
        cardName.value = '全部会员卡'
      }
      if (resData.suspend_setting.charging_type === 1) {
        ///收费类型1按请假次数收费，每次请假收取x元，2按请假时间收费，请x月，收取x元（按请假时间收费，请假时间不足1月时按1月计费）
        params.amount = resData.suspend_setting.charging_rule.number_price
      } else {
        isPayByTime.value = false
        monthPrice.value = Number(
          (
            Number(resData.suspend_setting.charging_rule.time_price.price) /
            Number(resData.suspend_setting.charging_rule.time_price.month)
          ).toFixed(2)
        )
        params.amount = monthPrice.value
      }
      params.amount = resData.suspend_setting.suspend_limit ? params.amount : 0
      if (
        parseInt(resData.suspend_setting.member_suspend_rule.each_longest_day) >
          parseInt(resData.suspend_surplus_day) ||
        parseInt(resData.suspend_setting.member_suspend_rule.each_longest_day) === 0
      ) {
        maxLeaveDay.value = +resData.suspend_surplus_day
      } else {
        maxLeaveDay.value = +resData.suspend_setting.member_suspend_rule.each_longest_day
      }
      dateEnd.value = addDays(new Date(), maxLeaveDay.value)
    })
    .catch((err) => {
      loginUserInfo.bus_id = ''
      Object.assign(params, {
        suspend_method: '',
        card_user_id: '',
        amount: 0,
      })
      cardName.value = '全部会员卡'
      uni.showToast({
        title: err.errormsg,
        icon: 'none',
        duration: 2500,
      })
    })
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20rpx;
}
.box-tips {
  margin: 20rpx 20rpx 0;
  text-align: center;
  font-size: 26rpx;
}
.oth-text {
  color: $theme-text-color-other;
}
</style>
