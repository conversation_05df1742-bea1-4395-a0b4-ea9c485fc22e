<template>
  <view class="box-view">
    <view class="paging-box footer-hasfixed">
      <z-paging
        ref="paging"
        v-model="dataList"
        :auto="false"
        :fixed="false"
        :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
        @query="loadList"
      >
        <template #top>
          <view class="merchant-bus-wrap">
            <MerchantBusPick v-model="busId" @change="handleChangeBus" />
          </view>
        </template>
        <view class="buddy">
          <view
            v-for="item in dataList"
            :key="item.id"
            class="item theme-bg"
            :class="!item.stop_status ? 'selected' : ''"
          >
            <view class="item-row">
              <view class="label">申请时间</view>
              <view class="value">{{ item.create_time }}</view>
            </view>
            <view class="item-row">
              <view class="label">请假时间</view>
              <view class="value">{{ item.stop_time }}~{{ item.active_time }}</view>
            </view>
            <view class="item-row">
              <view class="label">申请门店</view>
              <view class="value">{{ item.bus_name }}</view>
            </view>
            <view class="item-row">
              <view class="label">请假卡范围</view>
              <view class="value">{{ item.card_name }}</view>
            </view>
            <view v-if="!item.stop_status" class="item-row btn-row" @tap="handleEndSuspend(item)">
              <view class="item-btn">结束请假</view>
            </view>
          </view>
        </view>
      </z-paging>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="handleLeave">我要请假</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils/urlMap'

const { checkLogin } = useLogin()
const dataList = ref<Record<string, any>[]>([])
const paging = ref()
const busId = ref('')
const useMerchantStore = useMerchant()
const busUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
onShow(() => {
  checkLogin().then(() => {
    // 每次进入都请求全部门店 重新进入的时候场馆id为空但已经有数据 需要去刷新
    if (busId.value) {
      busId.value = ''
    } else if (!busId.value && dataList.value.length) {
      busId.value = ''
      useMerchantStore.setBusId('')
      loadList(1, 10)
    }
  })
})
function handleChangeBus({ bus_id, user_id }) {
  busUserInfo.bus_id = bus_id
  busUserInfo.user_id = user_id
  loadList(1, 10)
}
function loadList(pageNo, pageSize) {
  if (!busUserInfo.user_id) {
    paging.value && paging.value.complete([])
    return
  }
  http
    .get('Usersuspend/getList', {
      ...busUserInfo,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
async function handleEndSuspend(info) {
  const { bus_id, id } = info
  const user_id = await useMerchantStore.getUserIdByBus(bus_id)
  http
    .post('Usersuspend/endSuspend', {
      bus_id,
      user_id,
      id,
    })
    .then((res) => {
      paging.value.reload()
    })
}
function handleLeave() {
  goUrlPage(`/packageMy/leave/request`)
}
</script>

<style lang="scss" scoped>
.paging-box {
  height: 100%;
  box-sizing: border-box;
}
.box-view {
  height: 100%;
  .buddy {
    .item {
      margin: 20rpx 50rpx;
      color: #7d7d7d;
      border-radius: 20rpx;

      .item-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;
        margin-left: 20rpx;
        padding-right: 20rpx;
        height: 90rpx;
        border-top: 1rpx solid #f6f6f8;
        .value {
          font-weight: bold;
        }
        &.btn-row {
          justify-content: flex-end;
        }
        .item-btn {
          padding: 10rpx 20rpx;
          background: var(--THEME-COLOR);
          color: #000;
          border-radius: 10rpx;
          align-items: center;
          font-size: 24rpx;
        }
      }
    }
    .selected {
      border: 1px solid var(--THEME-COLOR);
    }
  }
  .v-bottom {
    right: 0;
    padding: 24rpx 30rpx 60rpx 30rpx;
    box-shadow: 2px 3px 11px 0px rgba(0, 0, 0, 0.2);

    .btn-bottom {
      background: #ca2e53;
    }
  }
}
</style>
