<template>
  <view class="detail-container theme-bg">
    <view class="card-item">
      <view class="card-info">
        <view class="card-top">
          <text class="card-name">{{ card.card_name }}</text>
        </view>
        <view class="card-top-row">
          <text class="card-type">次数卡</text>
        </view>
        <view class="card-des">
          <view class="card-num"></view>
          <view class="overplus"> 剩余 <text>1次</text> </view>
        </view>
        <view class="card-bottom-row">
          <view class="left-btn"> </view>
          <view>{{ card.expiry_date }}</view>
        </view>
      </view>
    </view>
    <view class="count-down">
      领取截止时间剩余
      <CountDown :size="15" :time="time" />
    </view>
    <view class="bot-tips">
      <view class="tit">领取规则</view>
      <view>1.每个链接只能领取1次</view>
      <view>2.领取后拥有卡1次的使用权利</view>
      <view>3.领取后卡仅限自己使用</view>
      <view>4.卡不允许退款、转让</view>
      <view>5.请注意卡到期时间，过期不再延期</view>
    </view>
    <view v-if="card.card_name" class="fixed-bottom-wrap theme-bg">
      <button
        class="normal-btn"
        :disabled="card.status === 1 || card.invalid_date === ''"
        @tap="getShareCard"
      >
        {{ statusText }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import CountDown from '@/components/CountDown.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
const { checkLogin } = useLogin()
const userStore = useUserStore()
const cardShareLogId = ref('')
const shareBusID = ref('')
const time = ref('00:00')
onLoad((options) => {
  shareBusID.value = options.bus_id || ''
  cardShareLogId.value = options.card_share_log_id || ''
  getDetail()
})
const statusText = computed(() => {
  const isSelfGetText = card.get_user_id === userStore.userInfoUserId ? '已领取' : '已被他人领取'
  return card.status === 1 ? isSelfGetText : card.invalid_date === '' ? '已过期' : '领取'
})
// 卡数据
const card = reactive<Record<string, string | number>>({
  active_time: '',
  buy_time: '',
  card_id: 0,
  card_source: '',
  card_user_id: 0,
  deleted: 0,
  description: '',
  expiry_date: '',
  initial: '',
  is_receive: '',
  member_share: 0,
  name: '',
  remark: '',
  surplus: '',
})
function getDetail() {
  http
    .get('/Membershare/getCardGetInfo', {
      bus_id: shareBusID.value || userStore.userInfoBusId,
      card_share_log_id: cardShareLogId.value,
      loading: true,
    })
    .then((res) => {
      const info = res.data.info
      time.value = info.invalid_date
      Object.assign(card, info)
    })
}
async function getShareCard() {
  await checkLogin(true, shareBusID.value || userStore.userInfoBusId)
  http
    .post('/Membershare/cardGet', {
      bus_id: shareBusID.value || userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_share_log_id: cardShareLogId.value,
      loading: true,
    })
    .then((res) => {
      uni.redirectTo({
        url: '/pages/my/card',
      })
    })
}
onShareAppMessage(() => {
  return {
    title: '分享给你一张会员卡',
    path: `/packageMy/cardshare/detail?card_share_log_id=${cardShareLogId.value}&bus_id=${userStore.userInfoBusId}`,
    imageUrl: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
  }
})
</script>

<style lang="scss" scoped>
.detail-container {
  height: 100%;
}
.card-item {
  position: relative;
  margin: 0 auto;
  width: 690rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
}
.bot-tips {
  width: 690rpx;
  box-sizing: border-box;
  padding: 30rpx;
  margin: 20rpx auto;
  background: #f6f6f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #000000;
  line-height: 2;
  .tit {
    font-weight: bold;
  }
}
.count-down {
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 24rpx;
  margin-top: 20rpx;
}
</style>
