<template>
  <custom-tabs v-model="current">
    <custom-tab-pane label="分享记录">
      <CardShareRecord :id="cardUserId" />
    </custom-tab-pane>
    <custom-tab-pane label="领取记录">
      <CardGetRecord v-if="current === 1" :id="cardUserId" />
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import CardShareRecord from './components/CardShareRecord.vue'
import CardGetRecord from './components/CardGetRecord.vue'
const current = ref(0)
const cardUserId = ref('')
onLoad((options) => {
  cardUserId.value = options.card_user_id || ''
  current.value = +(options.type || 0)
})
</script>

<style lang="scss" scoped>
.record-main {
  height: 100%;
}
.item-rig {
  flex: 1;
  display: flex;
  align-items: center;
}
.warm-tips {
  display: flex;
  align-items: center;
}
.invoice-form {
  margin-top: 30rpx;
}
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
</style>
