<template>
  <view class="detail-container theme-bg">
    <view class="card-item">
      <view class="card-info">
        <view class="card-top">
          <text class="card-name">{{ card.name }}</text>
          <text v-if="card.initial" class="card-init">(初始{{ card.initial }})</text>
        </view>
        <view class="card-top-row">
          <text class="card-type">次数卡</text>
        </view>
        <view class="card-des">
          <view class="card-num">{{ card.card_sn ? 'NO. ' + card.card_sn : '' }}</view>
          <view class="overplus">
            剩余 <text>{{ card.surplus }}</text>
          </view>
        </view>
        <view class="card-bottom-row">
          <view class="left-btn"> </view>
          <view>{{ card.expiry_date }}</view>
        </view>
      </view>
    </view>
    <view class="bot-tips">
      <view class="tit">分享规则</view>
      <view>1.每次分享操作将分享1次他人进行领取 </view>
      <view>2.分享后链接只能被领取1次</view>
      <view>3.领取成功后当前会员卡剩余扣除1次 </view>
      <view>4.当会员卡剩余次数为0次时，分享链接领取失败</view>
      <view>5.分享链接有效期24小时，超时无法领取</view>
    </view>
    <view v-if="card.remark !== '已用完' && card.surplus" class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" open-type="share">进行分享</button>
    </view>
    <view v-if="card.remark === '已用完'" class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" disabled>卡余额不足</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import _ from 'lodash'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
const { checkLogin } = useLogin()
const userStore = useUserStore()
const cardUserId = ref('')
onLoad((options) => {
  cardUserId.value = options.card_user_id || ''
  getDetail()
})
onShow(() => {
  checkLogin().then((info) => {
    getDetail()
  })
})
// 卡数据
const card = reactive<Record<string, string | number>>({
  active_time: '',
  buy_time: '',
  card_id: 0,
  card_source: '',
  card_user_id: 0,
  deleted: 0,
  description: '',
  expiry_date: '',
  initial: '',
  is_receive: '',
  member_share: 0,
  name: '',
  remark: '',
  surplus: '',
})
function getDetail() {
  http
    .get('/Membershare/getCardShareInfo', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_user_id: cardUserId.value,
      loading: true,
    })
    .then((res) => {
      const info = res.data.info
      Object.assign(card, info)
      if (info.remark === '已用完' || !info.surplus) {
        // @ts-ignore
        uni.hideShareMenu()
      }
    })
}
onShareAppMessage(() => {
  const promise = new Promise((resolve) => {
    http
      .post('/Membershare/createCardShareLog', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        card_user_id: cardUserId.value,
        loading: true,
      })
      .then((res) => {
        const id = res.data.card_share_log_id
        resolve({
          title: '分享给你一张会员卡',
          path: `/packageMy/cardshare/detail?card_share_log_id=${id}&bus_id=${userStore.userInfoBusId}`,
          imageUrl: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
        })
      })
  })
  return {
    title: '分享给你一张会员卡',
    path: `/packageMy/cardshare/detail?card_share_log_id=&bus_id=${userStore.userInfoBusId}`,
    imageUrl: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
    promise, // 如果该参数存在，则其它的参数将会以 resolve 结果为准，如果三秒内不 resolve，分享会使用上面传入的默认参数
  } as any
})
</script>

<style lang="scss" scoped>
.detail-container {
  height: 100%;
}
.card-item {
  position: relative;
  margin: 0 auto;
  width: 690rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
}
.bot-tips {
  width: 690rpx;
  box-sizing: border-box;
  padding: 30rpx;
  margin: 40rpx auto;
  background: #f6f6f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #000000;
  line-height: 2;
  .tit {
    font-weight: bold;
  }
}
</style>
