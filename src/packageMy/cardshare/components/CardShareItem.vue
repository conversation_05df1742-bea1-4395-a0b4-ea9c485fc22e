<template>
  <view class="share-item" :class="{ expired: info.invalid_date === '' }" @tap="goDetail">
    <view class="share-item-left">
      <view class="name">{{ info.create_date }} 创建分享 </view>
      <view class="item-bot">{{ info.invalid_date ? `${info.invalid_date}过期` : '已过期' }}</view>
    </view>
    <view class="share-status" :class="{ 'has-get': info.status === 1 }">{{
      info.status === 1 ? '已领取' : '待领取'
    }}</view>
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const props = defineProps<{
  info: Record<string, string | number>
}>()
function goDetail() {
  uni.navigateTo({
    url: `/packageMy/cardshare/detail?card_share_log_id=${props.info.id}&bus_id=${userStore.userInfoBusId}`,
  })
}
</script>

<style lang="scss" scoped>
.share-item {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  background: #ffebe0;
  padding: 30rpx;
  margin: 0 30rpx 20rpx;
  font-size: 28rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  .share-item-left {
    overflow: hidden;
  }
  .name {
    width: 100%;
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 40rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item-bot {
    font-size: 24rpx;
    color: $theme-text-color-other;
  }
  .share-status {
    font-size: 30rpx;
    font-weight: bold;
    color: $theme-text-color-other;
    &.has-get {
      color: #000;
    }
  }
}
.expired {
  background: #dcdcdc;
  .item-bot {
    color: #000;
  }
}
</style>
