<template>
  <view class="invoice-content theme-bg">
    <z-paging
      ref="paging"
      v-model="dataList"
      :fixed="false"
      :show-loading-more-no-more-view="false"
      @query="loadList"
    >
      <view class="get-list">
        <view v-for="(item, index) in dataList" :key="index" class="get-list-item">
          <view class="item-left">
            <image :src="item.avatar" class="avatar" />
            <text class="name">{{ item.nickname }}</text>
          </view>
          <view class="item-cen">{{ item.create_date }}</view>
          <view class="item-rig">成功领取</view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const dataList = ref<Record<string, string>[]>([])
const paging = ref()
const props = defineProps<{
  id: string
}>()
function handleAddInvoice() {}

function loadList(pageNo, pageSize) {
  http
    .get('/Membershare/getCardGetLog', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_user_id: props.id,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
</script>

<style lang="scss" scoped>
.invoice-content {
  height: 100%;
}
.get-list {
  padding-top: 20rpx;
}
.get-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  height: 110rpx;
  line-height: 110rpx;
  background: #f6f6f8;
  border-radius: 20px;
  margin: 0 30rpx 20rpx;
  font-size: 26rpx;
  color: #03080e;
  .item-left {
    display: flex;
    align-items: center;
    width: 250rpx;
    white-space: nowrap;
    overflow: hidden;
  }
  .avatar {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
    margin-right: 20rpx;
    flex-shrink: 0;
  }
  .name {
    width: 160rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
