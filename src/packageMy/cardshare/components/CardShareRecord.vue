<template>
  <view class="invoice-content theme-bg">
    <z-paging
      ref="paging"
      v-model="dataList"
      :fixed="false"
      :show-loading-more-no-more-view="false"
      @query="loadList"
    >
      <view class="share-list">
        <template v-for="(item, index) in dataList" :key="index">
          <CardShareItem :info="item" />
        </template>
      </view>
      <template #bottom>
        <view v-if="isShowShareBtn" class="zpaging-bottom-wrap theme-bg">
          <button class="normal-btn" @tap="handleAdd">新建分享</button>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CardShareItem from './CardShareItem.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const dataList = ref([])
const paging = ref()
const props = defineProps<{
  id: string
}>()
const isShowShareBtn = ref(true)
function handleAdd() {
  uni.navigateTo({
    url: `/packageMy/cardshare/add?card_user_id=${props.id}`,
  })
}
onShow(() => {
  paging.value && paging.value.reload()
})
function loadList(pageNo, pageSize) {
  http
    .get('/Membershare/getCardShareLog', {
      bus_id: userStore.userInfoBusId,
      card_user_id: props.id,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      isShowShareBtn.value = true
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      if(pageNo === 1) {
        isShowShareBtn.value = false
      }
      paging.value.complete(false)
    })
}
</script>

<style lang="scss" scoped>
.invoice-content {
  height: 100%;
}
.share-list {
  padding-top: 20rpx;
}
</style>
