<template>
  <div class="detail-container theme-bg">
    <view class="card-item">
      <view class="card-info">
        <view class="card-top">
          <text class="card-name">{{ card.name }}</text>
          <text v-if="card.initial" class="card-init">(初始{{ card.initial }})</text>
        </view>
        <!-- <view v-if="card.remark" class="status">{{ card.remark }}</view> -->
        <view class="card-top-row">
          <text class="card-type">储值卡</text>
          <text v-if="card.user_number > 1" class="card-member"> 已添加{{ card.user_number }}个成员 </text>
        </view>
        <view class="card-des">
          <view class="card-num">{{ card.card_sn ? 'NO. ' + card.card_sn : '' }}</view>
          <view class="overplus">
            剩余 <text>{{ card.surplus }}</text>
          </view>
        </view>
        <view class="card-bottom-row">
          <view class="left-btn" @tap="handleTapCard">
            <text class="drop-down">{{ !showMore ? '会员卡详情' : '收起' }}</text>
          </view>
          <view>{{ card.expiry_date }}</view>
        </view>
      </view>

      <view class="card-info-more box-shadow" :hidden="!showMore">
        <view class="more-row">
          <text class="label">归属门店</text>
          <text class="content">{{ card.bus_name }}</text>
        </view>
        <view class="more-row">
          <text class="label">获取时间</text>
          <text class="content">{{ card.buy_time }}</text>
        </view>
        <view class="more-row">
          <text class="label">获取方式</text>
          <text class="content">{{ card.card_source || '-' }}</text>
        </view>
        <view class="more-row">
          <text class="label">购买剩余</text>
          <text v-if="curVolumeInfo" class="content">{{ curVolumeInfo.purchase_surplus }}</text>
        </view>
        <view class="more-row">
          <text class="label">赠送剩余</text>
          <text v-if="curVolumeInfo" class="content">{{ curVolumeInfo.gift_surplus }}</text>
        </view>
      </view>
    </view>

    <view class="select-label">请选择充值套餐</view>

    <view v-if="rechargeList && rechargeList.length" class="list" @tap="handleTapPay">
      <view v-for="(item, index) in rechargeList" :key="index" class="item">
        <image
          class="left-icon"
          src="https://imagecdn.rocketbird.cn/minprogram/uni-member/recharge-icon.png"
          mode="aspectFit"
        />
        <view class="center">
          <text class="number">￥{{ item.number }}</text>
          <text class="supply">
            {{ item.gift_number ? `赠${item.gift_number}` : '' }}
            {{ item.gift_number && item.delay_number ? '，' : '' }}
            {{ item.delay_number ? `卡延期${item.delay_number}天` : '' }}
          </text>
        </view>
        <view
          class="right-btn"
          :class="{ disabled: card.customStatus !== 0 || card.self_recharge !== 1 }"
          :data-id="item.id"
        >
          ￥{{ item.amount }}
        </view>
      </view>
    </view>
    <view v-else class="nodata">暂无数据</view>
  </div>
</template>

<script setup lang="ts" name="recharge">
import _ from 'lodash'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'

interface rechargeItem {
  id?: string
  amount: number
  number: number
  gift_number: number
  delay_number: number
}

const userStore = useUserStore()
const instanceEventChannel = ref()
const cardUserId = ref('')
onLoad((options) => {
  cardUserId.value = options.card_user_id || ''
  instanceEventChannel.value = getCurrentInstance().proxy.getOpenerEventChannel()
})
onShow(() => {
  getDetail()
})

// 卡数据
const card = ref({}) as any
const rechargeList = ref<rechargeItem[]>([])
function getDetail() {
  http
    .get('/Card/getBuyValueCardInfo', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_user_id: cardUserId.value,
      loading: true,
    })
    .then((res) => {
      const info = res.data.info
      info.customStatus =
        info.active_time === 0
          ? 3 // 未激活
          : info.deleted === 4
          ? 2 // 请假中（审批通过后）
          : 0

      card.value = info
      rechargeList.value = info.recharge_package_list
    })
}

// 展开收起卡详情
const showMore = ref(false)
const handleTapCard = () => {
  showMore.value = !showMore.value
  if (!curVolumeInfo.value) {
    getCardUserVolume()
  }
}

// 卡剩余数值
const curVolumeInfo = ref()
function getCardUserVolume() {
  http
    .get('/Personalcenter/getCardUserVolume', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_user_id: cardUserId.value,
      loading: true,
    })
    .then((res) => {
      curVolumeInfo.value = res.data
    })
}

const loading = ref(false)
const handleTapPay = _.throttle((e) => {
  if (e.target.dataset.id) {
    // self_recharge当前卡是否可续充，0不可以 1可以
    if (card.value.customStatus !== 0 || card.value.self_recharge !== 1) {
      const toastTitle = {
        0: '该储值卡未开启续充',
        2: '请先结束请假',
        3: '请先激活卡',
      }
      uni.showToast({
        title: toastTitle[card.value.customStatus || card.value.self_recharge] || '未知状态',
        icon: 'none',
      })
      return
    }

    if (loading.value) {
      return 'loading'
    }
    loading.value = true
    http
      .post('/Card/rechargeValueCard', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        card_user_id: cardUserId.value,
        recharge_package_id: e.target.dataset.id,
      })
      .then((res) => {
        if (res.data.info) {
          pay(res.data.info)
        } else {
          loading.value = false
        }
      })
      .catch(() => {
        loading.value = false
      })
  }
}, 800)

function pay(info: UniApp.RequestPaymentOptions) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success: successGo,
    fail(result) {
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
    },
    complete() {
      loading.value = false
    },
  })
}

function successGo() {
  // const pages = getCurrentPages()
  // const page = pages[pages.length - 3]

  // uni.navigateBack({
  //   delta: page.route === 'packageMy/my/index' ? 1 : 2
  // })
  instanceEventChannel.value && instanceEventChannel.value.emit('refresh-page')
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.card-item {
  position: relative;
  margin: 0 auto;
  width: 691rpx;
  font-size: 24rpx;
  border-radius: 8rpx;

  .card-info-more {
    margin: 0 auto 11rpx;
    padding: 10rpx 30rpx 26rpx;
    width: 644rpx;
    border: 1px solid $theme-text-color-other;
    border-top-width: 0;
    border-radius: 0 0 4rpx 4rpx;
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    .more-row {
      display: flex;
      margin-top: 28rpx;
      line-height: 28rpx;
      font-size: 24rpx;
      .label {
        margin-right: 36rpx;
        min-width: 124rpx;
        color: $theme-text-color-grey;
      }
      .content {
        flex: 1;
        text-align: right;
      }
    }
  }
  .status {
    top: 10rpx;
  }
}

.select-label {
  margin: 40rpx auto 20rpx;
  width: 691rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.list {
  // overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx 30rpx 40rpx;
  // height: calc(100% - 225rpx);
  // box-sizing: border-box;
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    width: 100%;
    padding: 0 34rpx 0 16rpx;
    height: 142rpx;
    font-weight: bold;
    border: 2rpx solid #d3d3d3;
    border-radius: 10rpx;
    .left-icon {
      margin-right: 36rpx;
      width: 110rpx;
      min-width: 110rpx;
      height: 110rpx;
    }
    .center {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 110rpx;
      .number {
        font-size: 36rpx;
      }
      .supply {
        font-size: $uni-font-size-sm;
        color: $theme-text-color-other;
      }
    }
    .right-btn {
      margin-left: auto;
      min-width: 180rpx;
      max-width: 45%;
      height: 70rpx;
      line-height: 70rpx;
      text-align: center;
      font-size: 30rpx;
      color: $uni-text-color-inverse;
      background-color: $theme-text-color-other;
      border-radius: 35rpx;
      &.disabled {
        background-color: #b5b5b5;
      }
    }
  }
}
</style>
