<template>
  <div class="recharge-container theme-bg">
    <view class="head-box">
      <view>
        <image
          class="left-icon"
          src="https://imagecdn.rocketbird.cn/minprogram/uni-member/value-card-icon.png"
          mode="aspectFit"
        />
        <text class="label">储值卡余额</text>
        <text class="sum">{{ balance }}元</text>
        <text class="gift-num">(含赠送{{ gift_volume }})</text>
      </view>
    </view>
    <view class="merchant-bus-wrap">
      <MerchantBusPick v-model="busId" />
    </view>
    <view class="card-page">
      <view v-for="item in cardList" :key="item.card_user_id" class="card-item">
        <view class="card-info">
          <!-- :class="{ notuse: item.目前没有该字段 }" -->
          <button
            class="right-btn"
            :class="{ disabled: item.customStatus !== 0 || item.self_recharge !== 1 }"
            @tap="handleToDetail(item)"
          >
            去充值
          </button>
          <view class="card-top">
            <text class="card-name">{{ item.name }}</text>
            <text v-if="item.initial" class="card-init">(初始{{ item.initial }})</text>
          </view>
          <view v-if="item.remark === '未激活'" class="status-des">
            <uni-icons type="info-filled" size="20" color="#ff7427"></uni-icons>激活后才正式生效
          </view>
          <view v-else-if="item.remark" class="status">{{ item.remark }}</view>
          <view class="card-top-row">
            <text class="card-type">
              储值卡
              <!-- {{ item.experience_card == 1 ? '体验卡' : cardTypeIds[item.card_type_id] }} -->
            </text>
            <text v-if="item.user_number > 1" class="card-member"> 已添加{{ item.user_number }}个成员 </text>
          </view>
          <view class="card-des">
            <view class="card-num">{{ item.card_sn ? 'NO. ' + item.card_sn : '' }}</view>
            <view class="overplus">
              剩余 <text>{{ item.surplus }}</text>
            </view>
          </view>
          <view class="card-bottom-row">
            <view class="left-btn" @tap="handleTapCard(item)">
              <text class="drop-down">{{ item.card_user_id !== cardId ? '会员卡详情' : '收起' }}</text>
            </view>
            <view>{{ item.expiry_date }}</view>
          </view>
        </view>

        <view class="card-info-more box-shadow" :hidden="item.card_user_id !== cardId">
          <view class="more-row">
            <text class="label">归属门店</text>
            <text class="content">{{ item.bus_name }}</text>
          </view>
          <view class="more-row">
            <text class="label">获取时间</text>
            <text class="content">{{ item.buy_time }}</text>
          </view>
          <view class="more-row">
            <text class="label">获取方式</text>
            <text class="content">{{ item.card_source || '-' }}</text>
          </view>
          <view v-if="curVolumeInfo" class="more-row">
            <text class="label">购买剩余</text>
            <text class="content">{{ curVolumeInfo.purchase_surplus }}</text>
          </view>
          <view v-if="curVolumeInfo" class="more-row">
            <text class="label">赠送剩余</text>
            <text class="content">{{ curVolumeInfo.gift_surplus }}</text>
          </view>
        </view>
      </view>
      <view v-if="cardList && cardList.length == 0" class="nodata"> 暂未购买储值卡产品 </view>
    </view>
  </div>
</template>

<script setup lang="ts" name="recharge">
import http from '@/utils/request'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils/urlMap'
const busId = ref('')
onShow(() => {
  getData()
})
const useMerchantStore = useMerchant()
watch(
  () => useMerchantStore.userInfoBusId,
  (val, oldVal) => {
    if (useMerchantStore.userInfoUserId) {
      getData()
    } else {
      cardList.value = []
    }
  }
)
// 获取数据
const balance = ref('0')
const gift_volume = ref('0')
const cardList = ref<Record<string, any>[]>([])
const getData = () => {
  return http
    .get('/Personalcenter/getStoredList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      is_overdue: '2', // 2 可用不可用都会返回
      loading: true,
    })
    .then((res) => {
      res.data.list.forEach((t) => {
        t.customStatus =
          t.active_time === 0
            ? 3 // 未激活
            : t.deleted === 4
            ? 2 // 请假中（审批通过后）
            : 0
        t.description = t.description.replace(/\r\n|\n|\r/g, '  ')
      })

      balance.value = res.data.balance
      gift_volume.value = res.data.gift_volume
      cardList.value = res.data.list
    })
}

// 展开收起卡详情
const cardId = ref(0)
function handleTapCard(info) {
  cardId.value = cardId.value === info.card_user_id ? 0 : info.card_user_id
  getCardUserVolume(cardId.value, info)
}

// 卡剩余数值
const curVolumeInfo = ref()
function getCardUserVolume(id, info) {
  if (!id) {
    return
  }
  const { bus_id, user_id, card_user_id } = info
  http
    .get('/Personalcenter/getCardUserVolume', {
      bus_id,
      user_id,
      card_user_id,
      loading: true,
    })
    .then((res) => {
      curVolumeInfo.value = res.data
    })
}

// 跳转储值卡充值页面
const handleToDetail = (item) => {
  // self_recharge当前卡是否可续充，0不可以 1可以
  if (item.customStatus !== 0 || item.self_recharge !== 1) {
    const toastTitle = {
      0: '该储值卡未开启续充',
      2: '请先结束请假',
      3: '请先激活卡',
    }
    uni.showToast({
      title: toastTitle[item.customStatus || item.self_recharge] || '未知状态',
      icon: 'none',
    })
    return
  }
  goUrlPage(`/packageMy/recharge/detail?card_user_id=${item.card_user_id}&bus_id=${item.bus_id}`)
}
</script>

<style lang="scss" scoped>
.recharge-container {
  height: 100%;
}
.head-box {
  padding: 0 26rpx;
  height: 80rpx;
  line-height: 80rpx;
  .left-icon {
    width: 29rpx;
    height: 23rpx;
  }
  .label {
    margin: 0 22rpx 0 15rpx;
    font-weight: bold;
    font-size: 30rpx;
  }
  .sum {
    font-weight: bold;
    font-style: 36rpx;
    color: $theme-text-color-other;
  }
  .gift-num {
    color: $theme-text-color-other;
  }
}

.card-page {
  overflow-y: auto;
  padding-top: 20rpx;
  height: calc(100% - 170rpx);
  box-sizing: border-box;
}
.card-item {
  position: relative;
  margin: 0 auto 26rpx;
  width: 691rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  .card-info {
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
  }
  .card-info-more {
    margin: 0 auto 11rpx;
    padding: 10rpx 30rpx 26rpx;
    width: 644rpx;
    border: 1px solid $theme-text-color-other;
    border-top-width: 0;
    border-radius: 0 0 4rpx 4rpx;
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    .more-row {
      display: flex;
      margin-top: 28rpx;
      line-height: 28rpx;
      font-size: $uni-font-size-sm;
      .label {
        margin-right: 36rpx;
        min-width: 124rpx;
        color: $theme-text-color-grey;
      }
      .content {
        flex: 1;
        text-align: right;
      }
    }
  }
  .status {
    top: 10rpx;
  }
  .status-des {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 20rpx;
    text-align: right;
    font-weight: bold;
    color: $theme-text-color-other;
    display: flex;
    align-items: center;
  }
  .right-btn {
    position: absolute;
    top: 126rpx;
    right: 30rpx;
    width: 130rpx;
    height: 50rpx;
    line-height: 50rpx;
    color: $uni-text-color-inverse;
    background: $theme-text-color-other;
    border-radius: 10rpx;
    margin: 0;
    font-weight: bold;
    font-size: $uni-font-size-sm;
    &.disabled {
      background-color: #b5b5b5;
    }
  }
}
</style>
