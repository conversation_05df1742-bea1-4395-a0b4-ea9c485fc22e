<template>
  <view class="invoice-item theme-bg" :class="info.bill_flow ? '' : 'padd-left'" @tap="handleSeePdf(info)">
    <view
      v-if="info.bill_flow"
      class="status-text"
      :class="info.status === '0' ? 'theme-status' : info.status === '2' ? '' : 'org-text'"
    >
      {{ info.status === '0' ? '已完成' : info.status === '2' ? '失败' : info.status === '1' ? '开票中' : '重试中' }}
    </view>
    <view class="invoice-item-left">
      <view class="name">
        <text v-if="info.operate_type" class="type-tag">{{ info.operate_type }}</text>
        <text>{{ info.description || info.flow_desc }}</text>
      </view>
      <view class="item-bot">
        <ThemeIcon class="icon-mr" type="t-icon-shijian" />
        时间
        <text class="bold">{{ info.deal_time || info.create_time }}</text>
      </view>
      <view class="item-bot">
        <ThemeIcon class="icon-mr" type="t-icon-danhao" />
        业务单号
        <text class="bold">{{ info.flow_sn }}</text>
      </view>
    </view>
    <view class="invoice-price"> ￥{{ info.amount || info.amount_tax }} </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
const props = defineProps({
  info: {
    type: Object,
  },
})
function handleSeePdf(info) {
  const url = info.bill_pdf
  if (url) {
    uni.downloadFile({
      url,
      success: function (res) {
        const filePath = res.tempFilePath
        uni.openDocument({
          filePath: filePath,
          success: function (res) {
            console.log('打开文档成功')
          },
        })
      },
      fail: function (res) {
        uni.showToast({ title: res.errMsg, icon: 'none' })
      },
    })
  }
}
</script>

<style lang="scss" scoped>
.invoice-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  &.padd-left {
    padding-left: 90rpx;
  }
  .invoice-item-left {
    overflow: hidden;
  }
  .name {
    width: 100%;
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item-bot {
    margin-bottom: 15rpx;
  }
  .type-tag {
    height: 35rpx;
    line-height: 35rpx;
    padding: 0 8rpx;
    font-size: 24rpx;
    display: inline-block;
    vertical-align: middle;
    margin-right: 15rpx;
    background: #ffffff;
    color: #000;
    border: 1rpx solid $theme-text-color-other;
    border-radius: 6rpx;
  }
  .bold {
    font-weight: bold;
    margin-left: 15rpx;
  }
  .invoice-price {
    font-size: 36rpx;
    font-weight: bold;
    color: $theme-text-color-other;
  }
}
</style>
