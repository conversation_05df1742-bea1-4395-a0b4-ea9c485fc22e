<template>
  <view class="contract-item theme-bg">
    <!-- order_sign_status 0未签 1已签 9免签 -->
    <!-- 已签状态 根据contract_sign_id !== 0 来判断是否是电子签已签 -->
    <!-- 未签状态 sign_type 0免签 1仅线下签署 2会员端与线下签署 -->

    <!-- v-if控制除免签以外才显示 -->
    <!-- src控制显示待签->会员端待签/线下待签, 已签->会员端已签,  线下已签->线下已签 -->
    <image
      v-if="item.order_sign_status !== 9 && item.order_sign_status !== -1"
      class="has-sign"
      :src="
        item.order_sign_status === 0
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/waitSign.png'
          : item.order_sign_status === 1 && item.contract_sign_id !== 0
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/sign.png'
          : item.order_sign_status === 1 && item.contract_sign_id === 0
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/offlineSign.png'
          : ''
      "
    />
    <view class="item">
      <view class="name">
        <text class="type-tag">{{ item.name }}</text>
        <text class="card-name">{{ item.card_name }}</text>
      </view>
    </view>
    <view class="item">
      <view class="name">合同编号</view>
      <view class="value">{{ item.order_sn }}</view>
    </view>
    <view class="item">
      <view class="name">合同金额</view>
      <view class="value">￥{{ item.amount }}</view>
    </view>
    <view class="item">
      <view class="name">签署门店</view>
      <view class="value">{{ item.bus_name }}</view>
    </view>
    <view class="item">
      <view class="name">签订时间</view>
      <view class="value">
        {{ item.deal_time }}
      </view>
    </view>
    
    <!-- 使用提取出来的按钮组件 -->
    <ContractButtons 
      :item="item" 
      @view-detail="handleViewDetail" 
      @sign="handleSign"
    />
  </view>
</template>

<script setup lang="ts">
import ContractButtons from './ContractButtons.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

const emit = defineEmits(['view-detail', 'sign'])

// 查看详情
const handleViewDetail = () => {
  emit('view-detail', props.item)
}

// 进行签名
const handleSign = () => {
  emit('sign', props.item)
}
</script>

<style lang="scss" scoped>
.contract-item {
  font-size: 26rpx;
  border-radius: 30rpx;
  margin: 20rpx 30rpx;
  padding: 0 30rpx;
  position: relative;
  .has-sign {
    position: absolute;
    right: 30rpx;
    top: 10rpx;
    width: 93rpx;
    height: 93rpx;
  }
  .item {
    border-bottom: 1rpx solid #f6f6f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 89rpx;
    line-height: 89rpx;
    &:last-child {
      border-bottom: none;
    }
    .name {
      display: flex;
      align-items: center;
    }
  }
  .btn-content {
    justify-content: flex-end;
  }
  .type-tag {
    height: 34rpx;
    line-height: 34rpx;
    border-radius: 6rpx;
    background: rgba(var(--THEME-RGB), 0.2);
    color: #000;
    font-size: 24rpx;
    margin-right: 8rpx;
    padding: 0 6rpx;
  }
  .card-name {
    display: block;
    max-width: 500rpx;
    font-size: 24rpx;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .value {
    font-weight: bold;
  }
  .sign-btn {
    background: var(--THEME-COLOR);
    display: inline-block;
    vertical-align: middle;
    height: 50rpx;
    line-height: 50rpx;
    border-radius: 10rpx;
    text-align: center;
    margin-left: 17rpx;
    font-size: 24rpx;
    padding: 0 30rpx;
    border: 1rpx solid var(--THEME-COLOR);
  }
  .none-background {
    background: #fff;
    color: #666;
    border: 1rpx solid #999;
  }
}
</style>
