<template>
  <view class="invoice-content">
    <z-paging
      ref="paging"
      v-model="dataList"
      :fixed="false"
      :show-loading-more-no-more-view="false"
      data-key="invoice"
      @query="loadList"
    >
      <view v-for="item in dataList" :key="item.id" class="item-wrap">
        <InvoiceItem :key="item.id" :info="item" />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import InvoiceItem from './InvoiceItem.vue'
import http from '@/utils/request'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils/urlMap'
const useMerchantStore = useMerchant()
const dataList = ref<Record<string, any>[]>([])
const paging = ref()

function loadList(pageNo, pageSize) {
  http
    .get('/Invoice/getRecord', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.completeByKey(res.data.list, 'invoice')
    })
    .catch((res) => {
      paging.value.completeByKey(false)
    })
}
watch(
  () => useMerchantStore.userInfoBusId,
  (val, oldVal) => {
    if (useMerchantStore.userInfoUserId) {
      paging.value.reload()
    } else {
      paging.value.completeByKey([], 'invoice')
    }
  }
)
</script>

<style lang="scss" scoped>
.invoice-content {
  height: 100%;
}
.item-wrap {
  margin: 0 20rpx 20rpx;
  &:first-child {
    margin-top: 30rpx;
  }
}
</style>
