<template>
  <!-- 未签并且是线下签署显示文案 -->
  <view v-if="item.order_sign_status === 0 && item.sign_type === 1" class="item">
    <view class="name"></view>
    <view class="value">请联系工作人员完成合同签署</view>
  </view>
  <!-- 免签以外才显示功能按钮 -->
  <view v-if="item.order_sign_status !== 9 && item.order_sign_status !== -1" class="item btn-content">
    <!-- 除免签和线下签署才显示"查看详情"按钮/仅会员端签署才显示"查看详情"按钮 -->
    <view
      v-if="
        (item.order_sign_status === 1 && item.contract_sign_id !== 0) ||
        (item.order_sign_status === 0 && item.sign_type === 2)
      "
      class="sign-btn none-background"
      @tap="handleViewDetail"
    >
      查看详情
    </view>
    <!-- 开启会员端签署并且未签状态下显示"进行签名"按钮 -->
    <view v-if="item.order_sign_status === 0 && item.sign_type === 2" class="sign-btn" @tap="handleSign">
      进行签名
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  item: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

const emit = defineEmits(['view-detail', 'sign'])

// 查看详情
const handleViewDetail = () => {
  emit('view-detail', props.item)
}

// 进行签名
const handleSign = () => {
  emit('sign', props.item)
}
</script>

<style lang="scss" scoped>
.item {
  border-bottom: 1rpx solid #f6f6f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 89rpx;
  line-height: 89rpx;
  
  &:last-child {
    border-bottom: none;
  }
  
  .name {
    display: flex;
    align-items: center;
  }
}

.btn-content {
  justify-content: flex-end;
}

.value {
  font-weight: bold;
}

.sign-btn {
  background: var(--THEME-COLOR);
  display: inline-block;
  vertical-align: middle;
  height: 50rpx;
  line-height: 50rpx;
  border-radius: 10rpx;
  text-align: center;
  margin-left: 17rpx;
  font-size: 24rpx;
  padding: 0 30rpx;
  border: 1rpx solid var(--THEME-COLOR);
}

.none-background {
  background: #fff;
  color: #666;
  border: 1rpx solid #999;
}
</style>
