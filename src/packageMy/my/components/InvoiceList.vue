<template>
  <view class="invoice-content">
    <z-paging ref="paging" v-model="dataList" :fixed="false" :show-loading-more-no-more-view="false" @query="loadList">
      <radio-group class="check-list" @change="radioChange">
        <label
          v-for="(item, index) in dataList"
          :key="index"
          class="check-list-label"
          :class="checkedVal == item.flow_sn ? 'checked' : ''"
        >
          <InvoiceItem :info="item" />
          <view class="checkbox-con">
            <radio :value="item.flow_sn" />
            <uni-icons v-if="checkedVal == item.flow_sn" type="checkbox-filled" size="17" color="#FF7427"></uni-icons>
            <view v-else class="checkbox-circle"></view>
          </view>
        </label>
      </radio-group>
      <template #bottom>
        <view class="zpaging-bottom-wrap theme-bg">
          <button class="normal-btn" @tap="handleAddInvoice">申请开票</button>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import InvoiceItem from './InvoiceItem.vue'
import http from '@/utils/request'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils/urlMap'
const useMerchantStore = useMerchant()
const dataList = ref<Record<string, any>[]>([])
const paging = ref()
const billSet = ref()
const checkedVal = ref('')
const curinfo = reactive({
  bus_id: '',
  amount: '',
  flow_sn: '',
  type: '',
})
watch(
  () => useMerchantStore.userInfoBusId,
  (val, oldVal) => {
    if (useMerchantStore.userInfoUserId) {
      paging.value.reload()
    } else {
      paging.value.complete([])
    }
  }
)
function handleAddInvoice() {
  if (!checkedVal.value) {
    uni.showToast({ title: '请先选择！', icon: 'none' })
    return false
  }
  goUrlPage(
    `/packageMy/my/invoiceAdd?amount=${curinfo.amount}&flow_sn=${curinfo.flow_sn}&type=${curinfo.type}&bill_type=${billSet.value.bill_type}&bus_id=${curinfo.bus_id}`
  )
}
function radioChange(e) {
  const sn = e.detail.value
  checkedVal.value = sn
  for (const iterator of dataList.value) {
    if (iterator.flow_sn === sn) {
      Object.assign(curinfo, iterator)
      break
    }
  }
}

function loadList(pageNo, pageSize) {
  http
    .get('/Invoice/getFlowList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      billSet.value = res.data.setting
      checkedVal.value = ''
      Object.assign(curinfo, {
        bus_id: '',
        amount: '',
        flow_sn: '',
        type: '',
      })
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
</script>

<style lang="scss" scoped>
.invoice-content {
  height: 100%;
}

.check-list {
  radio,
  checkbox {
    display: none;
  }
  .check-list-label {
    position: relative;
    display: block;
    margin: 0 20rpx 20rpx;
    &:first-child {
      margin-top: 30rpx;
    }
  }
  .checkbox-con {
    position: absolute;
    left: 30rpx;
    z-index: 9;
    top: 50%;
    transform: translateY(-50%);
  }
  .checkbox-circle {
    box-sizing: border-box;
    width: 34rpx;
    height: 34rpx;
    background: #ffffff;
    border: 4rpx solid #e8e8e8;
    border-radius: 50%;
  }
}
</style>
