<template>
  <view class="contract-item theme-bg">
    <image
      v-if="item.order_sign_status !== 9 && item.order_sign_status !== -1"
      class="has-sign"
      :src="
        item.order_sign_status === 0
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/waitSign.png'
          : item.order_sign_status === 1 && item.contract_sign_id !== 0
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/sign.png'
          : item.order_sign_status === 1 && item.contract_sign_id === 0
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/offlineSign.png'
          : ''
      "
    />

    <!-- 合同类型和名称 -->
    <view class="item">
      <view class="name">
        <text v-if="item.zfb_pay" class="payment-tag">支付宝月付</text>
        <text v-if="item.wechat_pay" class="payment-tag">微信月付</text>
        <text class="card-name">{{ item.card_name }}</text>
      </view>
    </view>
    
    <!-- 合同编号 -->
    <view class="item">
      <view class="name">合同编号</view>
      <view class="value">{{ item.order_sn }}</view>
    </view>
    
    <!-- 月付总金额 -->
    <view class="item">
      <view class="name">月付总金额</view>
      <view class="value">￥{{ item.amount }}</view>
    </view>
    
    <!-- 分期期数 -->
    <view class="item">
      <view class="name">分期期数</view>
      <view class="value">{{ item.periods }}期</view>
    </view>
    
    <!-- 签署门店 -->
    <view class="item">
      <view class="name">签署门店</view>
      <view class="value">{{ item.bus_name }}</view>
    </view>
    
    <!-- 签订时间 -->
    <view class="item">
      <view class="name">签订时间</view>
      <view class="value">{{ item.deal_time }}</view>
    </view>
    
    <!-- 使用提取出来的按钮组件 -->
    <ContractButtons 
      :item="item" 
      @view-detail="handleViewDetail" 
      @sign="handleSign"
    />
  </view>
</template>

<script setup lang="ts">
import ContractButtons from './ContractButtons.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

const emit = defineEmits(['view-detail', 'sign'])

// 查看详情
const handleViewDetail = () => {
  emit('view-detail', props.item)
}

// 进行签名
const handleSign = () => {
  emit('sign', props.item)
}
</script>

<style lang="scss" scoped>
.contract-item {
  font-size: 26rpx;
  border-radius: 30rpx;
  margin: 20rpx 30rpx;
  padding: 0 30rpx;
  position: relative;

  .has-sign {
    position: absolute;
    right: 30rpx;
    top: 10rpx;
    width: 93rpx;
    height: 93rpx;
  }

  .item {
    border-bottom: 1rpx solid #f6f6f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 89rpx;
    line-height: 89rpx;

    &:last-child {
      border-bottom: none;
    }

    .name {
      display: flex;
      align-items: center;
    }
  }

  .payment-tag {
    height: 34rpx;
    line-height: 34rpx;
    border-radius: 6rpx;
    background: rgba(var(--THEME-RGB), 0.2);
    color: #333;
    font-size: 24rpx;
    margin-right: 8rpx;
    padding: 0 10rpx;
    border: 1px solid #e0e0e0;
  }

  .card-name {
    display: block;
    max-width: 500rpx;
    font-size: 24rpx;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .value {
    font-weight: bold;
  }
}
</style>
