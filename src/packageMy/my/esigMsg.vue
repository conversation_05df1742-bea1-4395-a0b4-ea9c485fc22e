<template>
  <view class="pwd-box">
    <view class="con theme-bg con-mt">
      <view class="input-wrap">
        <input placeholder="请输入手机号" v-model="postData.phone" type="number" disabled />
        <view class="input-label">
          <button class="input-code" @tap="getCode" :disabled="codeDisabled">{{ codeText }}</button>
        </view>
      </view>
      <view class="input-wrap">
        <input placeholder="请输入验证码" v-model="postData.code" type="number" />
      </view>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="handleNext">完成认证</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useTimeInterval } from '@/hooks/useTimeInterval.ts'
const { setTimeInterval, disButton, codeText, codeDisabled} = useTimeInterval()
const userStore = useUserStore()
const postData = reactive({
  phone: '',
  flowId: '',
  order_id: '',
  code: '',
})
onLoad((options) => {
  postData.order_id = options.orderId
  postData.flowId = options.flowId || ''
  // const contractUserInfo = uni.getStorageSync('contractUserInfo')
  // if (contractUserInfo) {
  //   postData.phone = contractUserInfo.phone || ''
  // } else {
  getCertificationCount()
  // }
})

function getCertificationCount() {
  return http.post('Esign/factorsPersonAuth', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
  }).then(res => {
    if (res.errorcode === 0) {
      postData.phone = res.data.phone || ''
    }
  })
}

function checkPhone() {
  if (!/^1\d{10}$/.test(postData.phone)) {
    uni.showToast({
      title: '手机号码不正确',
      icon: 'none',
    })
    return false
  } else {
    return true
  }
}
function getCode() {
  if (!checkPhone()) {
    return false
  }
  setTimeInterval()
  http
    .get('Esign/sendSignContractCode', {
      ...postData,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      postData.flowId = (res.data && res.data.flowId) || ''
      uni.showToast({
        title: res.errormsg
      })
    }).catch((err)=> {
      disButton()
    })
}
function handleNext() {
  if (!checkPhone()) {
    return false
  }
  http
    .post('Esign/signContract', {
      ...postData,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      uni.showToast({
        title: res.errormsg
      })
      uni.redirectTo({ url: '/packageMy/my/contract' });
    })
}
</script>

<style lang="scss" scoped>
.pwd-box {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  .con {
    margin: 32rpx 20rpx;
    border-radius: 20rpx;
    padding: 0 40rpx 100rpx;
    overflow: hidden;
  }
  .con-mt {
    padding-top: 44rpx;
  }
  .input-code {
    width: 40%;
    height: 90rpx;
    /* text-align: right; */
    font-size: 24rpx;
    line-height: 90rpx;
    background: transparent;
    color: $theme-text-color-other;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
}
.input-wrap {
  width: 100%;
  /* margin-bottom: 30rpx; */
  .input-label {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  input {
    background: #f5f6f7;
    color: #7d7d7d;
    border-radius: 45rpx;
    height: 90rpx;
    line-height: 90rpx;
    /* margin-bottom: 16rpx; */
    padding: 0 40rpx;
    font-size: 30rpx;
  }
}
</style>
