<template>
  <view class="contract-box">
    <z-paging
      ref="paging"
      v-model="dataList"
      :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
      empty-view-text="暂无数据"
      :auto="true"
      @query="loadList"
    >
      <template #top>
        <view class="merchant-bus-wrap">
          <MerchantBusPick v-model="busId" />
        </view>
      </template>

      <template v-for="(item, index) in dataList" :key="index">
        <MonthlyContractItem
          v-if="item.zfb_pay || item.wechat_pay"
          :item="item"
          @view-detail="goDetail"
          @sign="(item) => goDetail(item, 1)"
        />
        <ContractItem
          v-else
          :item="item"
          @view-detail="goDetail"
          @sign="(item) => goDetail(item, 1)"
        />
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils/urlMap'
import ContractItem from './components/ContractItem.vue'
import MonthlyContractItem from './components/MonthlyContractItem.vue'

const busId = ref('')
const { checkLogin } = useLogin()
const dataList = ref<Record<string, any>[]>([])
const paging = ref()

onShow(() => {
  checkLogin().then(() => {
    if (paging.value) {
      paging.value.reload()
    }
  })
})

onLoad(() => {
  uni.$on('refreshContractRecord', () => {
    if (paging.value) {
      paging.value.reload()
    }
  })
})

// 处理: 离开前上一个路由不是"我的"那么将会返回至"我的"
onUnload(() => {
  const pages = getCurrentPages()
  const page = pages[pages.length - 2]
  const path = page.route
  const currentPage = pages[pages.length - 1].route;
  if(currentPage !== 'packageMy/my/contract') {
    return;
  }
  if (path && path !== 'pages/my/index') {
    uni.switchTab({
      url: '/pages/my/index',
    })
  }
})
const useMerchantStore = useMerchant()
watch(
  () => useMerchantStore.userInfoBusId,
  () => {
    if (paging.value) {
      paging.value.reload()
    }
  }
)
function goDetail(info: Record<string, any>, isSignContract?: number) {
  if (info.name !== '拆分') {
    goUrlPage(
      `/packageMy/my/contractDetail?orderId=${info.id}&isSignContract=${isSignContract || ''}&bus_id=${info.bus_id}`
    )
  }
}
function loadList(pageNo: number, pageSize: number) {
  if (!useMerchantStore.userInfoUserId) {
    paging.value && paging.value.complete([])
    return
  }

  http
    .get('/Personalcenter/getContractList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
    })
    .catch(() => {
      paging.value.complete(false)
    })
}
</script>

<style lang="scss">
/* Styles moved to the ContractItem component */
</style>
