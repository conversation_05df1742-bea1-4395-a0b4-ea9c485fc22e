<template>
  <view class="pwd-box">
    <view class="con theme-bg con-mt">
      <view
        class="input-wrap"
        :class="{
          'input-disabled': (userAccountAuthType === 0 && !initName) || userAccountAuthType === 2 ? false : true,
        }"
      >
        <view class="input-label">
          <uni-icons type="info" size="20" color="red"></uni-icons>
          <view v-if="status === 1">
            {{
              userAccountAuthType === 1
                ? '请确认身份信息准确性，若信息有误请联系管理员修改'
                : '请认真填写您的身份信息，提交后将不能修改'
            }}
          </view>
          <view v-if="status === 2">请填写您接收到的验证码</view>
        </view>
        <input
          v-if="status === 1"
          v-model="postData.name"
          placeholder="请输入姓名"
          :disabled="(userAccountAuthType === 0 && !initName) || userAccountAuthType === 2 ? false : true"
        />
      </view>
      <view
        v-if="status === 1"
        class="input-wrap"
        :class="{
          'input-disabled': (userAccountAuthType === 0 && !initIdNo) || userAccountAuthType === 2 ? false : true,
        }"
      >
        <input
          v-model="postData.id_no"
          placeholder="请输入身份证号"
          type="idcard"
          :disabled="(userAccountAuthType === 0 && !initIdNo) || userAccountAuthType === 2 ? false : true"
        />
      </view>
      <view v-if="status === 1 && certificationCount === 3" class="input-wrap input-disabled">
        <input v-model="postData.phone" placeholder="请输入手机号" type="number" disabled />
      </view>
      <view v-if="status === 2" class="input-wrap line-bot">
        <input v-model="postData.code" placeholder="请输入验证码" type="number" />
      </view>
    </view>
  </view>
  <label v-if="certificationCount === 4" class="fixed-protocol-col theme-bg">
    <checkbox-group class="checkbox-group" @change="checkboxChange">
      <checkbox class="cb-transform" value="1" :checked="hasReadAgreement" />
        我已阅读并同意
        <navigator url="/packageMy/agreement/esign/digital" class="link-text" hover-class="none">《数字证书服务协议》</navigator>、
        <navigator class="link-text" url="/packageMy/agreement/esign/certification" hover-class="none">《认证服务协议》</navigator>和
        <navigator class="link-text" url="/packageMy/agreement/esign/private" hover-class="none">《隐私政策》</navigator>
    </checkbox-group>
  </label>
  <view class="fixed-bottom-wrap theme-bg">
    <button v-if="status === 1" class="normal-btn" @tap="handleNext">下一步</button>
    <button v-if="status === 2" class="normal-btn" @tap="handleVerifySubmit">下一步</button>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin } = useLogin()
const userStore = useUserStore()
const orderId = ref('')
const postData = reactive({
  name: '',
  id_no: '',
  phone: '',
  flow_id: '',
  code: '',
  channel_type: 1,
  auth_method: 1,
})
const certificationCount = ref(2)
const initName = ref('')
const initIdNo = ref('')
const userAccountAuthType = ref(1)
const status = ref(1)
onLoad((options) => {
  orderId.value = options.orderId || ''
  getCertificationCount()
})

// 2023/12/26 更改逻辑 由原认证后储存认证信息然后再onload回显 改为 始终由接口获取用户信息 并且手机号无法更改 名字是否能更改看接口返回值来判断
// user_account_auth_type 会员实名信息 0 会员仅能添写缺失信息 1 仅能从系统中读取会员信息 2 可填写/修改会员信息进行认证
function getCertificationCount() {
  return http
    .post('Esign/factorsPersonAuth', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        const resData = res.data
        certificationCount.value = Number(resData.factors_person_auth)
        userAccountAuthType.value = resData.user_account_auth_type
        initName.value = resData.username
        initIdNo.value = resData.id_code
        postData.name = resData.username
        postData.id_no = resData.id_code
        postData.phone = resData.phone
      }
    })
}

function handleVerifySubmit() {
  if (!postData.code) {
    uni.showToast({ title: '验证码不能为空!', icon: 'none' })
    return false
  }
  return http
    .post('Esign/codeAuth', {
      ...postData,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        uni.redirectTo({ url: `/packageMy/my/autograph?orderId=${orderId.value}` })
      }
    })
    .catch((err) => {
      console.error(err)
    })
}

onShow(() => {
  checkFaceResult()
})
function checkFaceResult() {
  const esignFaceReturnInfo = uni.getStorageSync('esignFaceReturnInfo')
  console.log('---esignFaceReturnInfo', esignFaceReturnInfo)
  if(esignFaceReturnInfo){
    uni.setStorageSync('esignFaceReturnInfo', '')
    const { appId, extraData } = esignFaceReturnInfo.referrerInfo
    // 电子签 上链公证签小程序APPID
    if (appId == 'wx1cf2708c2de46337' && extraData && extraData.faceResult) {
      const hasFailed = extraData.faceResult.ErrorCode !== 0
      noticeSerciveGetResult(hasFailed)
      if(hasFailed) {
        uni.navigateTo({
          url: `/packageMy/my/faceResult?errortit=验证失败&errormsg=${extraData.faceResult.ErrorMsg}`
        })
      }
    }
  }
}
const hasReadAgreement = ref(false)
const checkboxChange = (e) => {
  hasReadAgreement.value = e.detail.value[0] || false
}

const faceAuthInfo = ref({})
function noticeSerciveGetResult(hasFailed = false) {
  console.log('getFaceResult', faceAuthInfo.value)
  http.post('Esign/memberFaceCheckFlowId', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    flowId: faceAuthInfo.value.flowId
  }).then(res => {
    console.log('memberFaceCheckFlowId res', res)
    if(hasFailed) {
      return
    }
    // 状态 0认证中 1认证成功 2认证失败
    const status = res.data.status
    if(status === 0) {
      uni.navigateTo({
        url: `/packageMy/my/faceResult?errortit=网络异常&errormsg=请重新验证`
      })
    } else if(status === 1) {
      uni.redirectTo({ url: `/packageMy/my/autograph?orderId=${orderId.value}&flowId=${faceAuthInfo.value.flowId}` })
    } else {
      uni.navigateTo({
        url: '/packageMy/my/faceResult'
      })
    }
  }).catch(err => {
    if(hasFailed) {
      return
    }
    console.log('memberFaceCheckFlowId err', err)
    uni.navigateTo({
      url: `/packageMy/my/faceResult?errortit=网络异常&errormsg=请重新验证`
    })
  })
}

function handleFaceAuth() {
  if(!hasReadAgreement.value) {
    return uni.showToast({ title: '请先阅读并同意协议', icon: 'none' })
  }
  http
    .post('Esign/memberFaceAuthApi', {
      ...postData,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      auth_method: 4,
      consume_type: 5, // 4 人脸意愿验证 5 人脸实名认证
    })
    .then((res) => {
      faceAuthInfo.value = res.data
      if (res.data.is_before_auth) {
        uni.redirectTo({ url: `/packageMy/my/autograph?orderId=${orderId.value}` })
      } else {
        goFaceMiniProgram(res.data)
      }
    })
}
function goFaceMiniProgram(info) {
  wx.navigateToMiniProgram({
    appId: 'wx1cf2708c2de46337',  // 上链公证签小程序APPID
    path: `/pages/face/index?bizToken=${info.faceToken}`, // 刷脸页面地址
    fail() {
      noticeSerciveGetResult(true)
    }
  })
}

async function handleNext() {
  if (!postData.name) {
    uni.showToast({ title: '姓名不能为空!', icon: 'none' })
    return false
  } else if (/^.*[0-9]{1,}.*$/.test(postData.name)) {
    uni.showToast({ title: '姓名不能含有数字!', icon: 'none' })
    return false
  } else if (postData.id_no.length === 0) {
    uni.showToast({ title: '证件号不能为空!', icon: 'none' })
    return false
  } else if (!/\d{17}(\d|X|x)/.test(postData.id_no)) {
    uni.showToast({ title: '身份证号格式错误!', icon: 'none' })
    return false
  }
  if(certificationCount.value === 4) {
    handleFaceAuth()
    return
  }
  http
    .post('Esign/memberAuth', {
      ...postData,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      if (certificationCount.value === 3) {
        if (res.data.is_before_auth) {
          uni.redirectTo({ url: `/packageMy/my/autograph?orderId=${orderId.value}` })
        } else {
          status.value = 2
          postData.flow_id = res.data.flowId
        }
      } else {
        uni.redirectTo({ url: `/packageMy/my/autograph?orderId=${orderId.value}` })
      }
    })
    .catch((err) => {
      uni.navigateTo({
        url: '/packageMy/train/signResult?errormsg=实名认证失败,请到前台进行纸制合同签署&fromPage=实名认证',
      })
    })
}
</script>

<style lang="scss" scoped>
.pwd-box {
  width: 100%;
  font-size: 28rpx;
  .con {
    margin: 32rpx 20rpx;
    border-radius: 20rpx;
    padding: 0 40rpx 100rpx;
    overflow: hidden;
  }
  .normal-btn {
    margin-top: 60rpx;
  }
  .con-mt {
    padding-top: 44rpx;
  }
}
.input-wrap {
  width: 100%;
  margin-bottom: 30rpx;
  .input-label {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
  }
  input {
    background: #f5f6f7;
    color: #7d7d7d;
    border-radius: 45rpx;
    height: 90rpx;
    line-height: 90rpx;
    margin-bottom: 16rpx;
    padding: 0 40rpx;
    font-size: 30rpx;
  }
}
.input-disabled {
  input {
    background: #ccc;
  }
}
</style>
