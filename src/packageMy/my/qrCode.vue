<template>
  <QrCode v-if="loginUserInfo.user_id" />
</template>

<script setup lang="ts">
import QrCode from '@/components/QrCode'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin } = useLogin()
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
onShow(() => {
  checkLogin().then((info) => {
    Object.assign(loginUserInfo, info)
  })
})
</script>

<style lang="scss" scoped></style>
