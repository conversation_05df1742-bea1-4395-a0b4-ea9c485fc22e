<template>
  <view class="pwd-box">
    <block v-if="isAvailable">
      <view v-if="!isReset" class="con theme-bg">
        <view class="password-wrap">
          <text v-for="(item, index) in pwdArr" :key="index">{{ item }}</text>
        </view>
        <button class="normal-btn" @tap="getCode">重置密码</button>
      </view>
      <view v-else class="con theme-bg con-mt">
        <view class="input-wrap">
          <input v-model="code" placeholder="请输入短信验证码" type="number" :focus="codeFocus" />
          <view class="input-label">
            <view>已向 {{ phoneStr }} 发送验证码</view>
            <button class="input-code" :disabled="codeDisabled" @tap="getCode">{{ codeText }}</button>
          </view>
        </view>
        <view class="input-wrap">
          <input v-model="pwd" placeholder="请输入6位新密码" type="number" />
        </view>
        <view class="input-wrap">
          <input v-model="pwdAgain" placeholder="确认新密码" type="number" />
        </view>
        <button class="normal-btn" @tap="resetPwd">保 存</button>
      </view>

      <view class="close-pwd" @click="handleClosePwd">关闭密码开柜</view>
    </block>

    <view v-else style="margin-top: 100rpx;">
      <uni-title type="h1" align="center" title="暂未开通密码开柜功能" style="margin-top: 100rpx;"></uni-title>
      <button class="normal-btn normal-btn-min" style="width: 300rpx; margin: 100rpx auto;" @tap="handleOpenPwd">启用密码开柜</button>
    </view>

    <uni-popup ref="alertDialog" type="dialog">
      <uni-popup-dialog type="info" cancelText="取消" confirmText="确认" title="通知" :content="message" @confirm="dialogConfirm" @close="dialogClose"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { useUserStore } from '@/store/user'
import { useTimeInterval } from '@/hooks/useTimeInterval.ts'
const { setTimeInterval, disButton, codeText, codeDisabled } = useTimeInterval('重新发送')
const { checkLogin } = useLogin()
const pwdArr = ref(['*', '*', '*', '*', '*', '*'])
const isReset = ref(false)
const codeFocus = ref(false)
const disabled = ref(false)
const pwd = ref('')
const pwdAgain = ref('')
const code = ref('')
const userStore = useUserStore()
const phoneStr = ref(userStore.userInfo.phone)
phoneStr.value = phoneStr.value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
onShow(() => {
  checkLogin().then((info) => {
    Object.assign(loginUserInfo, info)
    getInfo()
  })
})
function resetPwd() {
  if (pwdAgain.value !== pwd.value) {
    uni.showToast({
      title: '新旧密码不一致',
      icon: 'none',
    })
    return false
  } else if (code.value && pwd.value && pwd.value.length === 6) {
    http
      .post('Personalcenter/modifyOpenLockerPwd', {
        ...loginUserInfo,
        code: code.value,
        pwd: pwd.value,
      })
      .then((res) => {
        pwdArr.value = pwd.value.split('')
        isReset.value = false
      })
  } else {
    uni.showToast({
      title: '请正确填写',
      icon: 'none',
    })
  }
}
function getCode() {
  setTimeInterval()
  isReset.value = true
  codeFocus.value = true
  http
    .get('Personalcenter/sendOpenLockerCode', {
      ...loginUserInfo,
    })
    .then((res) => {
      uni.showToast({
        title: res.errormsg,
      })
    })
    .catch((err) => {
      disButton()
    })
}
const getInfo = () => {
  http.get('Personalcenter/getOpenLockerPwd', { ...loginUserInfo }).then((res) => {
    pwdArr.value = res.data.pwd.split('')
  })
}

// 密码开柜
const isAvailable = ref(false)
const TO_CLOSE = '关闭后无法使用「密码开柜」功能'
const TO_OPEN = '启用「密码开柜」功能？'
const message = ref('')
const alertDialog = ref()
const handleOpenPwd = () => {
  message.value = TO_OPEN
  alertDialog.value.open()
}
const handleClosePwd = () => {
  message.value = TO_CLOSE
  alertDialog.value.open()
}
const dialogConfirm = () => {
  http
    .post('/Personalcenter/openPwdUse', {
      user_id: userStore.userInfoUserId,
      open_pwd: isAvailable.value ? 0 : 1,
    })
    .then((res) => {
      uni.showToast({
        title: res.errormsg,
      })
      isAvailable.value = !isAvailable.value
    })
}
const dialogClose = () => {
  alertDialog.value.close()
}

http.post('/Personalcenter/openPwdInfo', {
  user_id: userStore.userInfoUserId,
}).then((res) => {
  isAvailable.value = Number(res.data.open_pwd ?? 0) === 1
})
</script>

<style lang="scss">
.pwd-box {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  .con {
    margin: 32rpx 20rpx;
    border-radius: 20rpx;
    padding: 0 40rpx 100rpx;
    overflow: hidden;
  }
  .normal-btn {
    margin-top: 60rpx;
  }
  .con-mt {
    padding-top: 44rpx;
  }
  .input-code {
    min-width: 60rpx;
    line-height: 60rpx;
    font-size: 24rpx;
    background: transparent;
    color: $theme-text-color-other;
  }
}
.password-wrap {
  margin-top: 100rpx;
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  text {
    width: 92rpx;
    height: 102rpx;
    line-height: 102rpx;
    font-size: 60rpx;
    border-radius: 10rpx;
    border: 2rpx solid #e7e7e7;
    margin-right: 16rpx;
    text-align: center;
    &:last-child {
      margin-right: 0;
    }
  }
}

.input-wrap {
  width: 100%;
  .input-label {
    height: 60rpx;
    line-height: 60rpx;
    font-size: 26rpx;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
  }
  input {
    background: #f5f6f7;
    color: #7d7d7d;
    border-radius: 45rpx;
    height: 90rpx;
    line-height: 90rpx;
    margin-bottom: 16rpx;
    padding: 0 40rpx;
    font-size: 30rpx;
  }
}

.close-pwd {
  font-size: 24rpx;
  color: $theme-text-color-other;
  margin-top: 100rpx;
  text-align: center;
}
</style>
