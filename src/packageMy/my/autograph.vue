<template>
  <view class="contract-box">
    <view class="square">
      <view class="paper">
        <canvas
          class="autograph-canvas"
          canvas-id="autographCanvas"
          @touchmove="move"
          @touchstart="start"
          @touchend="end"
          @touchcancel="cancel"
          @longtap="tap"
          disable-scroll="true"
          @error="error"
        ></canvas>
      </view>
    </view>
    <view class="alert">请在下方空白处签字</view>
    <view class="btn-wrap">
      <view class="min-btn outer-org" @tap='clearClick'>重签</view>
      <view class="min-btn" @tap='saveClick'>保存</view>
    </view>

    <canvas class="rotate" canvas-id="rotate" :style="`width:${canvash}px;height:240px;`"></canvas>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import env from '@/config/env'
const userStore = useUserStore()
const orderId = ref('')
const flowId = ref('')
const factorsPersonAuth = ref('')
const from = ref('')
const canvash = ref('')
const canvasw = ref(240)
const isPosting = ref(false)
const isSign = ref(false)
const resInfo = reactive({
  user_auth_status: '',
  order_sign_status: '',
})
let content = null
let touchs = []
onLoad((options) => {
  orderId.value = options.orderId || ''
  flowId.value = options.flowId || ''
  factorsPersonAuth.value = options.factorsPersonAuth || ''
  from.value = options.from || ''
  uni.getSystemInfo({
    success: function(res) {
      canvash.value = res.windowHeight-32;
    }
  });
  content = wx.createCanvasContext("autographCanvas");
  content.setStrokeStyle("#000000");
  content.setLineWidth(5);
  //设置线两端端点样式更加圆润
  content.setLineCap("round");
  //设置两条线连接处更加圆润
  content.setLineJoin("round");
})
onShow(() => {
  checkFaceResult()
})
function checkFaceResult() {
  const esignFaceReturnInfo = uni.getStorageSync('esignFaceReturnInfo')
  if(esignFaceReturnInfo){
    uni.setStorageSync('esignFaceReturnInfo', '')
    const { appId, extraData } = esignFaceReturnInfo.referrerInfo
    // 电子签 上链公证签小程序APPID
    if (appId == 'wx1cf2708c2de46337' && extraData && extraData.faceResult) {
      const hasFailed = extraData.faceResult.ErrorCode !== 0
      if(!hasFailed) {
        handleNext()
      }
    }
  }
}
//绘制
function draw(touchs) {
  let point1 = touchs[0]
  let point2 = touchs[1]
  touchs.shift()
  content.moveTo(point1.x, point1.y)
  content.lineTo(point2.x, point2.y)
  content.stroke()
  content.draw(true)
  isSign.value = true
}
// 画布的触摸移动手势响应
function move(e) {
  let point = { x: e.touches[0].x, y: e.touches[0].y }
  touchs.push(point)
  if (touchs.length >= 2) {
    draw(touchs)
  }
}

// 画布的触摸移动结束手势响应
function end(e) {
  for (let i = 0; i < touchs.length; i++) {
    touchs.pop()
  }
}

//清除操作
function clearClick() {
  //清除画布
  content.clearRect(0, 0, canvasw.value, canvash.value)
  content.draw(true)
  isSign.value = false
}
//保存图片
function saveClick() {
  if (!isSign.value) {
    uni.showToast({
      icon: 'none',
      title: '请先进行签名',
    })
    return false
  }
  if (isPosting.value) {
    return false
  }
  isPosting.value = true
  uni.canvasToTempFilePath({
    canvasId: 'autographCanvas',
    success: function (res) {
      // rotate canvas.
      let ctx = uni.createCanvasContext('rotate')
      ctx.translate(0, canvasw.value)
      ctx.rotate((270 * Math.PI) / 180)
      ctx.drawImage(res.tempFilePath, 0, 0, canvasw.value, canvash.value)
      ctx.draw(false, () => {
        uni.canvasToTempFilePath({
          canvasId: 'rotate',
          success: function (params) {
            //  uni.saveImageToPhotosAlbum({
            //       // 下载图片
            //       filePath: params.tempFilePath,
            //       success: function() {
            //           uni.showToast({
            //               title: '保存成功',
            //               icon: 'success',
            //           })
            //       },
            //   })
            uni.showToast({
              icon: 'loading',
              title: '正在上传',
            })
            uni.uploadFile({
              url: `${env.apiBaseUrl}Esign/uploadImageSeal`,
              filePath: params.tempFilePath,
              header: http.getGatewayParams(),
              fileType: 'image',
              name: 'file',
              timeout: 30*1000,
              useHighPerformanceMode:true,
              formData: {
                order_id: orderId.value,
                flowId: flowId.value,
                bus_id: userStore.userInfoBusId,
                user_id: userStore.userInfoUserId,
              },
              success: function (db) {
                // success
                const dd = JSON.parse(db.data)
                if (dd.errorcode == 0) {
                  if(flowId.value || factorsPersonAuth.value === '4') {
                    // 人脸验证成功后的后续流程
                    handleFaceFlow()
                  } else {
                    uni.redirectTo({ url: `/packageMy/my/esigMsg?orderId=${orderId.value}` })
                  }
                } else {
                  uni.showToast({ title: dd.errormsg, icon: 'none' })
                }
              },
              fail: function () {
                uni.showToast({ title: '微信版本过低，不支持上传图片！' })
              },
              complete: function () {
                isPosting.value = false
              },
            })
          },
        })
      })
    },
  })
}

function handleFaceFlow() {
  // pc认证方式设置为人脸 并且之前已经过了实名认证 直接从合同详情跳转到当前页的情况
  if(from.value === 'detail' && factorsPersonAuth.value === '4') {
    getFaceToken()
    return;
  }
  // 获取流程id是否过期
  http.post('Esign/memberFaceCheckFlowId', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    flowId: flowId.value,
    is_expired: 1,
  }).then(res => {
    console.log('memberFaceCheckFlowId res', res)
    // 状态 0认证中 1认证成功 2认证失败
    const status = res.data.status
    console.log('memberFaceCheckFlowId res status', status)
    if(status === 1) {
      handleNext()
    } else {
      uni.redirectTo({ url: `/packageMy/my/esigMsg?orderId=${orderId.value}&flowId=${flowId.value}` })
    }
  }).catch(err => {
    console.log('memberFaceCheckFlowId err', err)
    goFaceMiniProgram(err.data) 
  })
}
function getFaceToken() {
  http
    .post('Esign/memberFaceAuthApi', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      order_id: orderId.value,
      auth_method: 4,
      consume_type: 4, // 4 人脸意愿验证 5 人脸实名认证
    })
    .then((res) => {
      goFaceMiniProgram(res.data)
    })
}
function goFaceMiniProgram(info) {
  flowId.value = info.flowId
  wx.navigateToMiniProgram({
    appId: 'wx1cf2708c2de46337',  // 上链公证签小程序APPID
    path: `/pages/face/index?bizToken=${info.faceToken}`, // 刷脸页面地址
    fail() {
    }
  })
}
// 人脸识别成功后flowId没过期的情况下可以跳过意愿验证直接签署 
function handleNext() {
  http
    .post('Esign/signContract', {
      flowId: flowId.value,
      order_id: orderId.value,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      console.log('signContract res', res)
      uni.showToast({
        title: res.errormsg
      })
      uni.redirectTo({ url: '/packageMy/my/contract' });
    }).catch(err => {
      // 前端人脸意愿验证完成去签署时 电子签那边接口去查询显示刷脸结果具体状态：ING：刷脸进行中的时候
      if(err.errorcode === 103030) {
        setTimeout(() => {
          handleNext();
        }, 1000);
        return;
      }
    })
}
</script>

<style lang="scss" scoped>
.rotate {
  // display: none;
  position: absolute;
  top: -750rpx;
  left: 0;
}

.contract-box {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  box-sizing: border-box;

  .square {
    height: 100%;
    width: 100%;
    background-color: #4a68a4;

    .paper {
      background-color: white;
      position: absolute;
      top: 13rpx;
      left: 13rpx;
      right: 110rpx;
      bottom: 13rpx;

      .autograph-canvas {
        background-color: white;
        position: absolute;
        top: 0;
        left: 140rpx;
        right: 0;
        bottom: 0rpx;
        height: 100%;
        width: 480rpx;
      }
    }
  }

  .min-btn {
    display: block;
    width: 308rpx;
    height: 70rpx;
    box-sizing: border-box;
    text-align: center;
    line-height: 70rpx;
    background: var(--THEME-COLOR);
    color: #000;
    font-size: 30rpx;
    margin-bottom: 350rpx;
    border-radius: 30rpx;
    letter-spacing: 26rpx;
    transform-origin: left top;
    transform: rotate(90deg);
    &.outer-org {
      border: 1rpx solid $theme-text-color-other;
      background-color: #fff;
      color: #1b1b1b;
    }
  }
  .btn-wrap {
    width: 70rpx;
    height: 650rpx;
    position: absolute;
    left: 116rpx;
    top: 50%;
    margin-top: -325rpx;
    z-index: 99;
  }

  

  .alert {
    width: 332rpx;
    height: 33rpx;
    position: absolute;
    transform-origin: left top;
    transform: rotate(90deg);
    top: 50%;
    margin-top: -156rpx;
    right: -315rpx;
    z-index: 99;
    font-size: 30rpx;
    color: #fff;
    font-weight: bold;
    letter-spacing: 6rpx;
  }
}
</style>
