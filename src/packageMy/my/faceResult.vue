<template>
  <view class="result-wrap theme-bg">
    <image
      class="result-img"
      mode="widthFix"
      :src="
        result === 'success'
          ? '/static/img/success.png'
          : '/static/img/fail.png'
      "
      alt="结果"
    />
    <view class="result-title">{{errortit}}</view>
    <view class="result-msg" v-if="errormsg">{{ errormsg }}</view>
    <view class="result-msg msg-more" v-else>
      <view>您的身份验证失败，请根据以下提示调整并重试：</view>
      <view class="face-result-container">
        <view class="face-result-item bold">超时未操作</view>
        <view class="face-result-item">保持正脸，按语音提示做动作</view>
        <view class="face-result-item bold">人脸与身份证信息不一致</view>
        <view class="face-result-item">请核对证件信息是否正确，或更换本人有效证件</view>
        <view class="face-result-item bold">面部遮挡</view>
        <view class="face-result-item">完全露出眉毛、眼睛、鼻梁和嘴巴</view>
        <view class="face-result-item bold">光线问题</view>
        <view class="face-result-item">在柔和的自然光下平时设备，避免背光</view>
        <view class="face-result-item bold">网络原因</view>
        <view class="face-result-item">请检查网络连接后重新尝试</view>
      </view>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="goback">确定</button>
    </view>
  </view>
</template>

<script setup lang="ts">
const errortit = ref('')
const errormsg = ref('')
const result = ref('fail')
function goback() {
  uni.navigateBack()
}

onLoad((option) => {
  errortit.value = option.errortit || '验证失败'
  result.value = option.result || 'fail'
  errormsg.value = option.errormsg || ''
})
</script>

<style lang="scss" scoped>
.result-wrap {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  box-sizing: border-box;
  padding: 60rpx 30rpx;
  text-align: center;
  color: #03080e;
}
.result-img {
  width: 178rpx;
}
.result-title {
  margin: 60rpx auto 78rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #000;
}
.result-msg {
  font-size: 30rpx;
  color: #313131;
  margin-bottom: 38rpx;
}
.msg-more {
  text-align: left;
}
.face-result-container {
  padding: 20rpx;
}

.face-result-item {
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #999;
}

.face-result-item:last-child {
  margin-bottom: 0;
}
.bold {
  color: #333;
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 28rpx;
}
</style>
