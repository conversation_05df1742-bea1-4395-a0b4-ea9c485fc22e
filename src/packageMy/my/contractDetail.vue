<template>
<view class="contract-box">
  <image @tap="handlePreview" mode="widthFix" :src="resInfo.pic_path"></image>
    <view class="fixed-bottom-wrap theme-bg" v-if="resInfo.order_sign_status === 0 && isSignContract === '1'">
      <button class="normal-btn" @tap="handleSign">签署合同</button>
    </view>
</view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const orderId = ref('')
const isSignContract = ref('0')
const resInfo = reactive({
  user_auth_status: '',
  factors_person_auth: '',
  order_sign_status: ''
})
onLoad((options)=> {
  orderId.value = options.orderId;
  isSignContract.value = options.isSignContract || '0'
})
onShow(()=> {
  getDetail()
})
// 处理: 离开前上一个路由不是"我的合同"那么将会返回至"我的合同"
onUnload(() => {
  const pages = getCurrentPages()
  const page = pages[pages.length - 2]
  const path = page.route
  const currentPage = pages[pages.length - 1].route;
  if(currentPage !== 'packageMy/my/contractDetail') {
    return;
  }
  if (path && (path !== 'packageMy/my/contract')) {
    uni.redirectTo({
      url: '/packageMy/my/contract',
    })
  } else {
    uni.navigateBack()
  }
})
function  handleSign() {
  if (resInfo.user_auth_status) {
    wx.navigateTo({
      url: `/packageMy/my/autograph?orderId=${orderId.value}&factorsPersonAuth=${resInfo.factors_person_auth}&from=detail`
    });
  } else {
    wx.navigateTo({
      url: "/packageMy/my/certification?orderId=" + orderId.value
    });
  }
}
function handlePreview() {
  uni.previewImage({
    urls: [resInfo.pic_path]
  });
}
function getDetail() {
  http
    .get('Personalcenter/getContractDetail', {
      order_id: orderId.value,
      is_sign_contract: isSignContract.value
    })
    .then((res) => {
        Object.assign(resInfo, res.data)
    })
}
</script>

<style lang="scss" scoped>
.contract-box {
  min-height: 100%;
  box-sizing: border-box;
  padding-bottom: 120rpx;
  image {
    width: 100%;
  }
}
</style>
