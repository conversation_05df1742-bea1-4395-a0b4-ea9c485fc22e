<template>
  <view class="faceauth-box theme-bg">
    <view class="top-des">
      为方便您快捷入场，我们将从其他门店同步人脸信息至本店。您的人脸信息仅用于本门店入场验证，不另做他用。
    </view>
    <view class="top-des">
      若您暂不授权，可通过到店签到一次开启人脸识别入场服务。
    </view>
    <view class="facepowerd-box">
      <view class="box-tit">授权使用门店</view>
      <view>
        <view class="facepowerd-item">
          {{ authBusInfo.bus_name }}
          <uni-icons type="checkbox-filled" size="24" color="#FF7427" />
        </view>
      </view>
    </view>
    <label class="fixed-protocol-col theme-bg">
        <checkbox-group class="checkbox-group" @change="checkboxChange">
          <checkbox value="1" :checked="agreeChecked">《隐私政策》</checkbox>
        </checkbox-group>
      </label>
      <view class="fixed-bottom-wrap theme-bg">
        <button class="normal-btn" @tap="confirmAuth">确认授权</button>
      </view>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const agreeChecked = ref(false)
const authBusInfo = reactive({
  bus_id: '',
  user_id: '',
  current_bus_id: '',
  current_user_id: '',
  bus_name: '',
})
async function goAgreePage() {
  await nextTick()
  agreeChecked.value = false
  uni.navigateTo({
    url: '/packageMy/agreement/private?showAgree=1',
    events: {
      acceptAgreementStatus: function (data) {
        if (data.isAgreeStatus == 1) {
          agreeChecked.value = true
        } else {
          agreeChecked.value = false
        }
      },
    },
  })
}
async function checkboxChange(e) {
  agreeChecked.value = e.detail.value[0] === '1' ? true : false
  if (agreeChecked.value) {
    goAgreePage()
  }
}
function confirmAuth() {
  if (!agreeChecked.value) {
    uni.showToast({ title: '请先确认协议', icon: 'none' })
    return
  }
  http.post('Personalcenter/authorizeFace', authBusInfo).then((res) => {
    uni.showToast({
      title: res.errormsg,
      icon: 'none',
      success: () => {
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/my/index',
          })
        }, 1000);
      },
    })
  })
}
onLoad((options) => {
  authBusInfo.bus_id = options.bus_id 
  authBusInfo.bus_name = options.bus_name 
  authBusInfo.user_id = options.user_id 
  authBusInfo.current_bus_id = userStore.userInfoBusId
  authBusInfo.current_user_id = userStore.userInfoUserId
})
</script>

<style lang="scss" scoped>
.faceauth-box {
  width: 100%;
  height: 100%;
  padding-top: 20rpx;
  padding-bottom: 180rpx;
  overflow-y: scroll;
  box-sizing: border-box;
}

.top-des {
  padding: 0 40rpx;
  line-height: 1.75;
  text-indent: 2rem;
  font-size: 26rpx;
}

.protocol-col {
  position: fixed;
  left: 0;
  bottom: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  color: #7d7d7d;
  width: 100%;
  display: flex;
  background: #fff;
  padding-left: 30rpx;
}
.facepowerd-box {
  margin: 40rpx;
}
.facepowerd-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
}

</style>
