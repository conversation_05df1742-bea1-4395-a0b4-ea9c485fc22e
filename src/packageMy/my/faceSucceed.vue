<template>
  <view class="result-wrap theme-bg">
    <image class="result-img" mode="widthFix" src="/static/img/success.png" alt="成功" />
    <view class="result-title">头像提交完成</view>
    <ComeInItem v-if="orderSn" :orderSn="orderSn" :orderId="orderId" :type="3" />
    <view class="result-tip">人脸入场信息同步可能存在延迟, 如果无法入场, 请等待 1 分钟再进行尝试</view>
    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <view class="normal-btn outer-gray" @tap="goIndex">我知道了</view>
        <!-- <button class="normal-btn" @tap="goDetail">查看会员卡</button> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import ComeInItem from '@/components/ComeInItem.vue'

const orderSn = ref('')
const orderId = ref('')
onLoad((options) => {
  orderSn.value = options.orderSn || ''
  orderId.value = options.orderId || ''
})
function goIndex() {
  uni.switchTab({
    url: '/pages/my/index',
  })
}
function goDetail() {
  uni.redirectTo({
    url: '/pages/my/card',
  })
}
</script>

<style lang="scss" scoped>
.result-wrap {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  box-sizing: border-box;
  padding: 60rpx 30rpx;
  text-align: center;
  line-height: 35rpx;
  color: #03080e;
}
.result-img {
  width: 178rpx;
}
.result-title {
  margin: 60rpx auto 78rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #000;
}
.result-msg {
  font-size: 30rpx;
  color: #313131;
  margin-bottom: 38rpx;
}
.result-tip {
  font-size: 24rpx;
  color: #999999;
  position: absolute;
  bottom: 250rpx;
  left: 60rpx;
  right: 60rpx;
  text-align: left;
  // text-indent: 2em;
}
.result-des {
  font-weight: bold;
  font-size: 48rpx;
  color: $theme-text-color-other;
  line-height: 25rpx;
  text-align: center;
  margin: 30rpx 0;
}
.outer-gray {
  margin-right: 12rpx;
}
</style>
