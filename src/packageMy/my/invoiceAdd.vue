<template>
  <custom-tabs :model-value="current" @change="tabChange">
    <custom-tab-pane v-for="(item, index) in tabList" :key="item.value" :label="item.label">
      <view class="form-items theme-bg invoice-form">
        <view class="item">
          <view class="label">发票类型</view>
          <view class="value">
            <picker v-if="postData.is_enterprise == 1" :range="genderList" @change="handleGenderChange">
              <view class="value rig-sel">
                {{ genderList[genderIdx] }}
              </view>
            </picker>
            <view v-else>{{ genderList[0] }}</view>
          </view>
        </view>
        <view class="item">
          <view class="label">抬头<span class="required-icon">*</span></view>
          <view class="item-rig">
            <input v-model="postData.bill_title" class="item-input" />
            <view @tap="getWxBillTitle">
              <uni-icons type="list" size="20" color="red"></uni-icons>
            </view>
          </view>
        </view>
        <view class="item">
          <view class="label"
            >{{ postData.is_enterprise == 1 ? '税号' : '身份证' }}<span class="required-icon">*</span></view
          >
          <input v-model="postData.tax_identity_number" class="item-input" />
        </view>
        <view class="item">
          <view class="label">
            <radio-group class="radio-group" @change="radioChange">
              <label class="radio"> <radio value="1" :checked="postData.deliver_type == 1" />电子邮件 </label>
              <label class="radio"> <radio value="2" :checked="postData.deliver_type == 2" />手机 </label>
            </radio-group>
          </view>
        </view>
        <view class="item">
          <view class="label"
            >{{ postData.deliver_type == 1 ? '电子邮件' : '手机' }}<span class="required-icon">*</span></view
          >
          <input v-model="postData.email" class="item-input" placeholder="用于接收电子发票" />
        </view>
        <view class="item">
          <view class="label">地址<span v-if="postData.type == 1" class="required-icon">*</span></view>
          <input v-model="postData.address" class="item-input" />
        </view>
        <view class="item">
          <view class="label">电话<span v-if="postData.type == 1" class="required-icon">*</span></view>
          <input v-model="postData.tel_number" class="item-input" />
        </view>
        <view class="item">
          <view class="label">开户行及账号<span v-if="postData.type == 1" class="required-icon">*</span></view>
          <input v-model="postData.bank_and_account" class="item-input" />
        </view>
        <view class="item">
          <view class="warm-tips">
            <uni-icons type="info" size="20" color="red"></uni-icons>
            <text>请勿随意填写与报销无关的内容</text>
          </view>
        </view>
        <view class="item">
          <view class="label">发票金额</view>
          <view class="price">￥{{ amount }}</view>
        </view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
  <view class="fixed-bottom-wrap theme-bg">
    <button class="normal-btn" @tap="addInvoice">申请开票</button>
  </view>
</template>

<script setup lang="ts">
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import http from '@/utils/request'
import _ from 'lodash'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
const userStore = useUserStore()
const { checkLogin } = useLogin()
const tabList = ref([
  {
    value: 1,
    label: '企业',
  },
  {
    value: 0,
    label: '个人/其它',
  },
])
const postData = reactive({
  type: 2,
  is_enterprise: 1,
  user_id: '',
  bus_id: '',
  bill_title: '',
  tax_identity_number: '',
  email: '',
  deliver_type: 1,
  tel_number: '',
  address: '',
  bank_and_account: '',
  flow_sn: '',
})
const billType = ref('2') // bill_type【1只开普票 2可开专票和普票】
const current = ref(0)
const amount = ref(0)
const genderIdx = ref(0)
const genderList = ref(['普通发票（电子)', '专用发票（纸质)'])
onLoad((options) => {
  postData.flow_sn = options.flow_sn || ''
  billType.value = options.bill_type || ''
  if (options.type === '储值' || billType.value === '1') {
    genderList.value = ['普通发票（电子)']
  }
  amount.value = options.amount
})

onShow(() => {
  checkLogin()
})
const addInvoice = _.throttle(
  () => {
    http
      .post('Invoice/openBill', {
        ...postData,
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
      })
      .then((res) => {
        uni.showLoading({
          title: '提交成功',
          mask: true,
        })
        setTimeout(() => {
          wx.redirectTo({
            url: '/packageMy/my/invoiceRecord?type=1',
          })
        }, 2000)
      })
  },
  2000,
  true
)
function tabChange(e) {
  current.value = e.value
  postData.is_enterprise = tabList.value[e.value].value
}

function radioChange(ev) {
  postData.deliver_type = ev.detail.value
}
function handleGenderChange(ev) {
  genderIdx.value = ev.detail.value
  postData.type = parseInt(ev.detail.value) === 0 ? 2 : 1
}
function getWxBillTitle() {
  uni.chooseInvoiceTitle({
    success: (info) => {
      current.value = parseInt(info.type) === 0 ? 0 : 1
      postData.is_enterprise = parseInt(info.type) === 0 ? 1 : 0
      postData.bill_title = info.title
      postData.tax_identity_number = info.taxNumber
      postData.tel_number = info.telephone
      postData.address = info.companyAddress
      postData.bank_and_account = info.bankName + ' ' + info.bankAccount
    },
  })
}
</script>

<style lang="scss" scoped>
.item-rig {
  flex: 1;
  display: flex;
  align-items: center;
}
.warm-tips {
  display: flex;
  align-items: center;
}
.invoice-form {
  margin-top: 30rpx;
}
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
</style>
