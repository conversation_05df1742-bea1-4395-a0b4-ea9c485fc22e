<template>
  <custom-tabs v-model="current">
    <template #right>
      <MerchantBusPick v-model="busId" />
    </template>
    <custom-tab-pane label="未申请">
      <InvoiceList />
    </custom-tab-pane>
    <custom-tab-pane label="已申请">
      <HasInvoiceList />
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import InvoiceList from './components/InvoiceList.vue'
import HasInvoiceList from './components/HasInvoiceList.vue'
import { useLogin } from '@/hooks/useLogin'
import MerchantBusPick from '@/components/MerchantBusPick.vue'

const current = ref('0')
const busId = ref('')
const { checkLogin } = useLogin()
onLoad((options) => {
  current.value = options.type || '0'
})
onShow(() => {
  checkLogin()
})
</script>

<style lang="scss" scoped>
.record-main {
  height: 100%;
}
.item-rig {
  flex: 1;
  display: flex;
  align-items: center;
}
.warm-tips {
  display: flex;
  align-items: center;
}
.invoice-form {
  margin-top: 30rpx;
}
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
</style>
