<template>
  <view class="box-tips">
    <view class="top"> 请核对确认信息无误 </view>
  </view>
  <view v-if="info" class="form-items theme-bg">
    <view class="item">
      <text class="label bold">{{ info.bus_name }}</text>
    </view>
    <view class="item">
      <text class="label">地址</text>
      <text class="value">{{ info.address }}</text>
    </view>
    <view class="item">
      <text class="label">电话</text>
      <text class="value">{{ info.bus_phone }}</text>
    </view>
    <view class="item">
      <text class="label">会籍顾问</text>
      <text class="value">{{ info.marketers_name }} {{ info.marketers_phone }}</text>
    </view>
    <view class="item">
      <text class="label">定金金额</text>
      <text class="value price">￥{{ info.deposit_amount }}</text>
    </view>
    <view v-if="info.card_name" class="item">
      <text class="label">其他</text>
      <text class="value price">成功支付后将获得 “{{ info.card_name }}”一张</text>
    </view>
  </view>

  <view v-if="info" class="form-items theme-bg">
    <view class="item">
      <text class="label bold">定金使用人信息</text>
    </view>
    <view class="item">
      <text class="label">使用人姓名</text>
      <input
        class="item-input"
        type="text"
        v-model.trim="formData.username"
        placeholder="请输入使用人姓名">
    </view>
    <view class="item">
      <text class="label">手机号</text>
      <button
        v-show="!formData.phone"
        open-type="getPhoneNumber"
        @click.stop="false"
        @getphonenumber="getPhoneNumber"
      >
        <input
          class="item-input"
          :value="formData.phone"
          :disabled="true"
          placeholder="请输入手机号码"/>
      </button>
      <input
        v-show="formData.phone"
        class="item-input"
        type="number"
        :maxlength="11"
        v-model="formData.phone"
        placeholder="请输入手机号码"/>
    </view>
  </view>

  <view v-if="info" class="fixed-bottom-wrap theme-bg">
    <button class="normal-btn" @tap="handleConfirm">支付定金</button>
  </view>
</template>
<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()

import _ from 'lodash'
const { checkLogin } = useLogin()
const info = ref(null)
const id = ref()
const formData = reactive({
  username: '',
  phone: ''
})
onLoad((options) => {
  id.value = decodeURIComponent(options.scene)
  getInfo(id.value)
})

function validate() {
  let title = ''

  if (!formData.username) {
    title = '请填写使用人姓名'
  } else if (!/^1\d{10}$/.test(String(formData.phone))) {
    title = '请填写有效手机号'
  }

  if (title) {
    uni.showToast({
      title,
      icon: 'none',
    })
  }

  return !title
}

const handleConfirm = _.throttle(
  () => {
    if (!validate()) {
      return
    }

    checkLogin(false, info.value.bus_id).then(() => {
      http.post('Depositexpcard/payOrder', {
        id: id.value,
        bus_id: info.value.bus_id,
        username: formData.username,
        phone: formData.phone,
        openid: userStore.userInfo.openid,
      }).then((req) => {
        pay(req.data)
      })
    })
  },
  2000,
  true
)
function pay(info) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success: () => {
      navigateToPage(info.order_sn)
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      uni.hideLoading()
    },
  })
}
function navigateToPage(order_sn) {
  uni.showLoading({
    title: '正在生成订单',
    mask: true,
  })
  setTimeout(() => {
    http
      .get('Depositexpcard/getPayStatus', { order_sn })
      .then((res) => {
        uni.switchTab({
          url: '/pages/my/index',
          success: (res) => {
            uni.hideLoading()
          },
        })
      })
      .catch(() => {
        uni.hideLoading()
        uni.showToast({
          title: '请稍后查询!',
          success(res) {
            uni.switchTab({
              url: '/pages/my/index',
            })
          },
        })
      })
  }, 3000)
}
function getInfo(id) {
  http.get('Depositexpcard/confirmOrder', { id }).then((res) => {
    info.value = res.data
  })
}

function getPhoneNumber(e) {
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    http
    .post('/user/authPhone', {
      phone_code: e.detail.code,
    })
    .then((res) => {
      if (res?.data?.phone) {
        uni.showToast({
          title: '授权录入成功',
          icon: 'none',
        })
        formData.phone = res.data.phone
      }
    })
  } else {
    uni.showToast({
      title:
        e.detail?.errMsg === 'getPhoneNumber:fail user deny'
          ? '已拒绝,录入失败'
          : e.detail?.errMsg || '手机授权失败',
      icon: 'none',
    })
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20rpx;
}
.bold {
  font-weight: bold;
}
.box-tips {
  margin: 80rpx 20rpx 50rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: bold;
  .bot {
    font-size: 24rpx;
    font-weight: 400;
    margin-top: 30rpx;
  }
}
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
</style>
