<template>
  <view class="shower-wrapper theme-bg">
    <BaseInfo :bath-num="bathNum" />
    <view v-if="!showOccupied" class="bath-info-content">
      <text class="single-price">{{ info.one }}</text>
      <text class="brand-limit {{info.bathStatus === '1' ? 'occupied' : ''}}">{{ info.two }}</text>
    </view>
    <view v-if="roomStatus === 0 && info.bathStatus !== '0' && !rented" class="consume-amount">
      <view>
        <text class="consume-item">费用</text><text class="consume-detail">{{ info.three }}元</text>
      </view>
    </view>
    <view v-if="rented" class="consume-amount">
      <view class="item-border">
        <text class="consume-item">消费金额</text
        ><text class="consume-detail">{{ info.costMoney }}元</text>
      </view>
      <view>
        <text class="consume-item">卡内余额</text
        ><text class="consume-detail">{{ info.cardMoney }}元</text>
      </view>
    </view>
    <view v-if="showOccupied" class="bath-info-content">
      <text class="single-price">{{ errormsg }}</text>
    </view>
    <BathTips v-if="roomStatus === 0 || rented" />
    <view class="fixed-bottom-wrap theme-bg">
      <button v-if="roomStatus === 0 || rented" class="normal-btn" @tap="handleSubmitClick">
        {{ info.bathStatus === '0' ? '租用' : rented ? '继续使用' : '退租' }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import BaseInfo from '../cabinet/components/BaseInfo'
import BathTips from '../cabinet/components/BathTips'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin, getParam } = useLogin()
const rented = ref(false)
const showOccupied = ref(false)
const postData = reactive({
  userId: '',
  bathroomNo: '',
  deviceId: '',
})
const info = reactive({
  one: '--元/分钟',
  two: '每次最长使用--分钟',
  bathNum: '--',
  bathStatus: '0', // 1正在使用 0空闲
  cardMoney: '--',
  costMoney: '--',
})
const roomStatus = ref(0) // 淋浴间状态 0为正常 Number
const errormsg = ref('')
const sessionId = ref('')
const bathNum = ref('--')
const curOption = ref(null)
onLoad((options) => {
  curOption.value = options
})
onShow(async () => {
  const curParams = await getParam(curOption.value.scene || '')
  const busId = curParams.bus_id || curOption.value.bus_id
  postData.deviceId = curParams.deviceId || curOption.value.deviceId || ''
  postData.bathroomNo = curParams.bathroomNo || curOption.value.bathroomNo || ''
  if (!busId) {
    uni.navigateTo({
      url: '/pages/train/signResult?errorcode=-1&errormsg=没有检测到签到场馆',
    })
  }
  checkLogin(true, busId).then((res) => {
    postData.busId = res.bus_id
    postData.userId = res.user_id
    getBathInfo()
  })
})
function getBathInfo() {
  const postInfo = { ...postData, showToast: false }
  delete postInfo.busId
  http
    .post('Vein/Zjm/getBathSituationQr', postInfo, 'veinUrl')
    .then((res) => {
      roomStatus.value = res.status
      Object.assign(info, res.data.info)
      bathNum.value = info.bathNum
      sessionId.value = res.data.sessionId
      if (res.data.sessionId) uni.setStorageSync('bath_session_id', res.data.sessionId)
      bathNum.value = res.data.info.bathNum
      return res.data.info.bathStatus === '0'
    })
    .catch((err) => {
      roomStatus.value = err.status
      errormsg.value = err.msg
      showOccupied.value = true
      return false
    })
}
async function handleSubmitClick() {
  if (rented.value) {
    rented.value = false
    const result = await getBathInfo()
    if (result) {
      setTimeout(() => {
        checkStatus()
      }, 500)
    }
    return
  }
  if (info.bathStatus === '0') {
    checkStatus()
  } else {
    // 需要退租
    uni.showModal({
      title: `退租后将结算费用并停止供水`,
      confirmColor: '#CA2E53',
      cancelColor: '#A0A0A0',
      success(res) {
        if (res.confirm) {
          rentBack()
        }
      },
    })
  }
}
function rentBack() {
  const postInfo = {
    ...postData,
    fromType: 'session',
    imgData: uni.getStorageSync('bath_session_id'),
  }
  delete postInfo.busId
  delete postInfo.userId
  http
    .post('Vein/Zjm/returnBathRoomQr', postInfo, 'veinUrl')
    .then((res) => {
      rented.value = true
      Object.assign(info, res.data)
      info.one = `已使用${info.usingTime}分钟`
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 2000,
      })
    })
    .catch((err) => {
      uni.showToast({
        title: err.msg,
        icon: 'none',
      })
    })
}
function checkStatus() {
  const postInfo = {
    ...postData,
    fromType: 'session',
    imgData: uni.getStorageSync('bath_session_id'),
  }

  if (info.one === '免费使用') {
    uni.showModal({
      title: `确认租用${bathNum.value}号淋浴间吗?`,
      confirmColor: '#CA2E53',
      cancelColor: '#A0A0A0',
      success(res) {
        if (res.confirm) {
          submitRent('')
        }
      },
    })
  } else {
    http
      .post('Vein/Zjm/getBathUserCard', postInfo, 'veinUrl')
      .then((res) => {
        // 一张卡自动租用
        if (res.data.userCards && res.data.userCards.length === 1) {
          const card_user_id = res.data.userCards[0].card_user_id
          uni.setStorageSync('bath_card_id', card_user_id)
          uni.showModal({
            title: `确认租用${bathNum.value}号淋浴间吗?`,
            confirmColor: '#CA2E53',
            cancelColor: '#A0A0A0',
            success(res) {
              if (res.confirm) {
                submitRent(card_user_id)
              }
            },
          })
          return
        }
        uni.navigateTo({
          url: `/packageMy/shower/cardChoose?imgData=${sessionId.value}&bathroomNo=${postData.bathroomNo}&deviceId=${postData.deviceId}`,
        })
      })
      .catch((err) => {
        uni.showToast({
          title: err.msg,
          icon: 'none',
        })
      })
  }
}
function submitRent(cardId) {
  const postInfo = {
    ...postData,
    cardId,
    fromType: 'session',
    imgData: uni.getStorageSync('bath_session_id'),
  }
  delete postInfo.busId
  delete postInfo.userId
  http
    .post('Vein/Zjm/rentBathRoomQr', postInfo, 'veinUrl')
    .then((res) => {
      uni.showToast({
        title: res.msg,
      })
      getBathInfo()
    })
    .catch((err) => {
      uni.showToast({
        title: err.msg,
        icon: 'none',
      })
    })
}
</script>

<style lang="scss" scoped>
.shower-wrapper {
  width: 100%;
  height: 100%;
  .bath-info-content {
    width: 100%;
    text-align: center;
    margin-top: 42rpx;
    .single-price {
      display: block;
      height: 40rpx;
      font-size: 42rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: rgba(27, 27, 27, 1);
    }
    .brand-limit {
      display: block;
      height: 30rpx;
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: 400;
      color: rgba(137, 137, 137, 1);
      line-height: 37px;
    }
    .occupied {
      color: rgba(202, 46, 83, 1);
    }
  }
  .consume-amount {
    margin-left: 50%;
    transform: translateX(-50%);
    width: 431rpx;
    margin-top: 70rpx;
    background-color: rgba(245, 247, 249, 1);
    border-radius: 10rpx;
    line-height: 116rpx;
    .item-border {
      border-bottom: 2rpx solid rgba(229, 229, 229, 1);
      box-sizing: border-box;
    }
    > view {
      font-family: PingFang SC;
      display: block;
      width: 100%;
      font-size: 32rpx;
      color: rgba(27, 27, 27, 1);
      font-weight: 400;
      .consume-item {
        width: 50%;
        font-size: 34rpx;
        display: inline-block;
        text-align: center;
      }
      .consume-detail {
        text-align: center;
        display: inline-block;
        color: rgba(202, 46, 83, 1);
        font-weight: bold;
        font-size: 43rpx;
      }
    }
  }
}
page {
  background-color: #ffffff;
}
</style>
