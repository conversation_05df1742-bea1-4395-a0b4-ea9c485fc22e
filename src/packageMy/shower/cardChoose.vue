<template>
  <view class="shower-wrapper">
    <view class="bath-header">
      <BathroomNo :bath-num="bathNum" size="small"></BathroomNo>
      <view class="detail-right">
        <text>{{ info.one }}</text>
        <text>{{ info.two }}</text>
      </view>
    </view>
    <view class="card-choose">
      <template v-for="(item, index) in cardList" :key="index">
        <BathCard
          class="card-choose-single"
          :is-selected="item.card_user_id === cardId"
          :detail-info="item"
          :left-value="item.last_num"
          :card-name="item.name"
          @on-selected="showId"
        />
      </template>
    </view>
    <BathTips></BathTips>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="handleSubmit">确认</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import BathroomNo from '../cabinet/components/BathroomNo.vue'
import BathCard from '../cabinet/components/BathCard.vue'
import BathTips from '../cabinet/components/BathTips.vue'
const postData = reactive({
  deviceId: '',
})
const info = reactive({
  one: '--元/分钟',
  two: '每次最长使用--分钟',
  bathNum: '003',
  bathStatus: '0', // 1正在使用 0空闲
  costMoney: '15.00', // 扫码人正在使用
  cardMoney: '88',
})
const cardId = ref('')
const bathNum = ref('--')
const cardList = ref<Record<string, any>[]>([])
onLoad((options) => {
  Object.assign(postData, options)
})
onShow(async () => {
  getCards()
})
function showId(id) {
  cardId.value = id
  uni.setStorageSync('bath_card_id', id)
}
function handleSubmit() {
  if (!cardList.value.length) return
  if (!cardId.value) {
    uni.showToast({
      title: '请选择储值卡',
    })
    return
  }
  submitRent()
}
function submitRent() {
  const postInfo = {
    ...postData,
    cardId: cardId.value,
    fromType: 'session',
  }
  http
    .post('Vein/Zjm/rentBathRoomQr', postInfo, 'veinUrl')
    .then((res) => {
      uni.showToast({
        title: res.msg,
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    })
    .catch((err) => {
      uni.showToast({
        title: err.msg,
        icon: 'none',
      })
    })
}
function getCards() {
  const postInfo = {
    ...postData,
    fromType: 'session',
  }
  http
    .post('Vein/Zjm/getBathUserCard', postInfo, 'veinUrl')
    .then((res) => {
      Object.assign(info, res.data.info)
      cardList.value = res.data.userCards
      bathNum.value = res.data.info.bathNum
    })
    .catch((err) => {
      uni.showToast({
        title: err.msg,
        icon: 'none',
      })
    })
}
</script>

<style lang="scss" scoped>
.shower-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .card-choose {
    width: 100%;
    padding: 33rpx 39rpx;
    box-sizing: border-box;
    position: fixed;
    bottom: 360rpx;
    top: 280rpx;
    overflow-y: scroll;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;
    .card-choose-single {
      display: block;
      width: 50%;
    }
  }
  .bath-header {
    width: 100%;
    background-color: #fff;
    padding: 49rpx 0 49rpx 77rpx;
    overflow: hidden;
    display: flex;
    align-items: center;
    .detail-right {
      height: 100%;
      vertical-align: middle;
      margin-left: 40rpx;
      > text:nth-of-type(1) {
        display: block;
        font-size: 40rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(27, 27, 27, 1);
      }
      > text:nth-of-type(2) {
        display: block;
        margin-top: 24rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: rgba(137, 137, 137, 1);
      }
    }
  }

  .error-msg {
    margin-top: 24rpx;
    width: 100%;
    text-align: center;
    font-size: 42rpx;
    font-family: PingFang SC;
    color: rgba(202, 46, 83, 1);
  }
}
</style>
