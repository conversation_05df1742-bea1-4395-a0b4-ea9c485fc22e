<template>
  <view v-if="!showCity" class="bus-page theme-bg">
    <view class="top-wrap">
      <view class="address-wrap" @tap="chooseAddress">
        {{ cityName || '全部' }}
      </view>
      <view class="search-wrap">
        <uni-search-bar
          v-model="search"
          placeholder="搜索"
          :radius="15"
          bg-color="transparent"
          cancel-button="none"
          @confirm="getList"
        />
      </view>
    </view>
    <view class="bus-wrap">
      <view
        v-for="item in busList"
        :key="item.bus_id"
        class="bus-item"
        :class="{ 'before-star': item.user_id }"
        @tap="handleSwitch(item)"
      >
        <view class="tit">
          <view class="con">{{ item.name }}</view>
          <view class="tit-rig">{{ item.dist }}</view>
        </view>
        <view class="address">
          <uni-icons class="icon-mr" type="location-filled" size="12" color="#7d7d7d"></uni-icons>
          {{ item.address }}
        </view>
        <view class="phone">
          <uni-icons class="icon-mr" type="phone-filled" size="12" color="#7d7d7d"></uni-icons>
          {{ item.bus_phone }}
        </view>
      </view>
      <view v-if="noData" class="nodata">暂无对应场馆数据</view>
    </view>
  </view>
  <view v-if="showCity">
    <citySelect
      ref="citys"
      :format-name="formatName"
      :active-city="activeCity"
      :hot-city="hotCity"
      :obtain-citys="obtainCitys"
      :is-search="true"
      @cityClick="cityClick"
    ></citySelect>
  </view>
  <!-- 位置权限弹窗 -->
  <LocationPermissionDialog :show="showLocationDialog" direction="down" :distance="80" @close="showLocationDialog = false" @enable="getList" />
</template>

<script setup lang="ts" name="login">
import citys from '@/components/city-select/citys.js'
import citySelect from '@/components/city-select/index.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import LocationPermissionDialog from '@/components/LocationPermissionDialog.vue'
//需要构建索引参数的名称（注意：传递的对象里面必须要有这个名称的参数）
const formatName = 'cityName'
//当前城市
const activeCity = reactive({
  cityName: '全部',
})
//热门城市
const hotCity = [
  {
    cityName: '全部',
  },
  {
    cityName: '北京市',
  },
  {
    cityName: '上海市',
  },
  {
    cityName: '重庆市',
  },
]
const themeStore = useThemeStore()
//显示的城市数据
const obtainCitys = ref(citys)
const userStore = useUserStore()
const busList = ref<Record<string, string | number>[]>([])
const search = ref('')
const cityName = ref('')
const showCity = ref(false)
const noData = ref(false)

const showLocationDialog = ref(false)
onLoad(() => {
  userStore.getLocationInfo().then((res) => {
    showLocationDialog.value = userStore.getShowLocationDialog()
    getList()
  })
})

function getList() {
  http
    .get('/Business/getRouterList', {
      user_id: userStore.userInfo.user_id,
      openid: userStore.userInfo.openid,
      search: search.value,
      location: cityName.value,
      lng: userStore.locationInfo.longitude,
      lat: userStore.locationInfo.latitude,
      loading: true,
    })
    .then((res) => {
      busList.value = res.data.list
      activeCity.cityName = res.data.location || ''
      cityName.value = res.data.location || ''
      if (res.data.list?.length) {
        noData.value = false
      } else {
        noData.value = true
      }
    })
}
function handleSwitch(info) {
  uni.setStorageSync('check_store_id', info.bus_id)
  uni.setStorageSync('check_store_name', info.name)

  uni.navigateBack()
}
function cityClick(item) {
  cityName.value = item.cityName
  showCity.value = false
  getList()
}
function chooseAddress() {
  showCity.value = true
}
</script>

<style lang="scss">
.bus-page {
  min-height: 100%;
  .uni-searchbar {
    padding: 0;
  }
  .uni-searchbar__box {
    border: 1px solid #e8e8e8;
  }
}

.top-wrap {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
.address-wrap {
  position: relative;
  line-height: 72rpx;
  margin-right: 22rpx;
}
.address-wrap::after {
  position: absolute;
  right: -14rpx;
  top: 50%;
  transform: translateY(-50%);
  content: ' ';
  border: 6rpx solid transparent;
  border-top: 6rpx solid #ff7427;
}
.search-wrap {
  flex: 1;
}
.bus-item {
  position: relative;
  margin-top: 30rpx;
  padding: 28rpx;
  background: rgba(var(--THEME-RGB), 0.1);
  border: 1rpx solid var(--THEME-COLOR);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: $theme-text-color-grey;
  overflow: hidden;
}
.before-star::before {
  position: absolute;
  width: 66rpx;
  height: 40rpx;
  top: -10rpx;
  left: -25rpx;
  content: '*';
  line-height: 20rpx;
  background: var(--THEME-COLOR);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  text-align: center;
  font-size: 22rpx;
  font-weight: bold;
  transform: rotate(-45deg);
}
.bus-item .tit {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
.bus-item .tit .con {
  max-width: 480rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bus-item .address {
  margin-bottom: 20rpx;
  margin-top: 4rpx;
}
.tit-rig {
  font-size: 24rpx;
  font-weight: bold;
}
.bus-page {
  padding: 0 20rpx;
}
</style>
