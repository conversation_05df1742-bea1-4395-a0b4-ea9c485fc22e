<template>
  <view class="result-wrap theme-bg">
    <image class="result-img" mode="widthFix" src="/static/img/success.png" alt="成功" />
    <view class="result-title">兑换成功</view>
    <view class="result-msg">团购券对应商品已自动下发</view>
    <view class="result-des">
      <template v-if="info.receive_type === '1'">
      {{info.card_name}}
      </template>
      <template v-else-if="info.receive_type === '2'">
      {{info.coupon_name}}
      </template>
      <template v-else>
      {{info.san_name}}
      </template>
    </view>
    <ComeInItem v-if="info.order_sn" :orderId="info.order_id" :orderSn="info.order_sn" :type="3" />
    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <button class="normal-btn outer-gray" @tap="goMine">完成</button>
        <view class="normal-btn" @tap="goDetail">查看详情</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import ComeInItem from '@/components/ComeInItem.vue'

const info = ref<any>({})
const orderSn = ref('')
onLoad((options: any) => {
  info.value = JSON.parse(decodeURIComponent(options.info||'{}'))
  orderSn.value = options.orderSn || ''
})
function goMine() {
  uni.switchTab({
    url: '/pages/my/index',
  })
}
function goDetail() {
  //receive_type 领取类型1卡课 2优惠券 3散场票
  const type = info.value.receive_type
  if (type === '1') {
    uni.redirectTo({
      url: `/pages/my/card`,
    })
  } else if (type === '2') {
    uni.redirectTo({
      url: `/pages/my/coupon`,
    })
  } else if (type === '3') {
    uni.redirectTo({
     url: `/pages/my/ticket?from=index`
    })
  }
}
</script>

<style lang="scss" scoped>
.result-wrap {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  box-sizing: border-box;
  padding: 60rpx 30rpx;
  text-align: center;
  color: #03080e;
}
.result-img {
  width: 178rpx;
}
.result-title {
  margin: 60rpx auto 78rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #000;
}
.result-msg {
  font-size: 30rpx;
  color: #313131;
  margin-bottom: 38rpx;
}
.result-des {
  font-weight: bold;
  font-size: 48rpx;
  color: $theme-text-color-other;
  line-height: 55rpx;
  text-align: center;
  margin: 30rpx 0;
}
.result-link {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: $theme-text-color-other;
}
.outer-gray {
  margin-right: 12rpx;
}
</style>
