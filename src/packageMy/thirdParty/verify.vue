<template>
  <view class="verify-box">
    <div class="verify-con">
      <div class="verify-title">请输入卡券的二维码数字</div>
      <radio-group class="check-list" @change="radioChange">
        <label
          v-for="(item, index) in THIRD_PLATFORM_NAME"
          :key="index"
          class="verify-radio-item"
          :class="postData.platform_id == item.value ? 'checked' : ''"
        >
          <radio :value="item.value" />
          <image :src="item.img" />
          <div>{{ item.label }}</div>
        </label>
      </radio-group>
      <div class="input-wrap">
        <input
          class="verify-input"
          type="text"
          v-model.trim="postData.code"
          placeholder="请输入"
          @input="checkCodeInfo"
        />
      </div>

      <div v-show="goodsInfo.group_title">
        <div class="verify-card">
          <image class="verify-img" :src="getImgByType(postData.platform_id)" />
          <div class="verify-info">
            <div class="title">{{ goodsInfo.group_title }}</div>
            <div class="text">{{ goodsInfo.end_time }}</div>
          </div>
        </div>
      </div>

      <!-- 当券码对应的产品是 单店卡 (universal_card=0) 时 会出现 “使用门店”选项 -->
      <!-- 如果商品配置的是  “核销门店” (use_pod=0) 则 这里 使用门店默认加载当前 小程序所在门店且无法切换到其他门店 -->
      <!-- 如果商品配置的是  “卡课发放/使用门店中任一门店” (use_pod=1)  则 这里 使用门店默认加载为空，然后可以选择 “发放/使用门店”其中一个门店作为核销下卡门店 -->
      <uni-list :border="false">
        <!-- <uni-list-item v-show="hasStore" :show-arrow="!storeFixed">
          <template #header>
            <view>
              <text class="required">*</text>
              <text class="item-label">兑换门店</text>
            </view>
          </template>
          <template #footer>
            <view v-if="!storeId" class="item-value" @click="handleStoreChange"> 请选择 </view>
            <view v-else class="item-value text-ellipsis" @click="handleStoreChange">
              {{ storeName }}
            </view>
          </template>
        </uni-list-item> -->

        <!-- 针对与套餐包，会籍卡，私/泳教  也会要求其选择 开卡方式，默认为空 -->
        <uni-list-item v-show="hasApproach" right-text="请选择" show-arrow>
          <template #header>
            <view>
              <text class="required">*</text>
              <text class="item-label">开卡方式</text>
            </view>
          </template>
          <template #footer>
            <picker :value="approachIndex" :range="approachList" @change="handleApproachChange">
              <view v-if="approachIndex === -1" class="item-value"> 请选择 </view>
              <view v-else class="item-value">
                {{ approachList[approachIndex] }}
              </view>
              <view v-if="approachIndex == 1 && activeTip" class="item-tip">
                {{ activeTip }}
              </view>
            </picker>
          </template>
        </uni-list-item>

        <!-- 用户姓名和性别填写 -->
        <template v-if="isUpdate">
          <uni-list-item>
            <template #header>
              <view>
                <view class="item-label" style="line-height: 70rpx">怎么称呼您</view>
                <view class="item-tip">(已便更好安排人员进行服务)</view>
              </view>
            </template>
            <template #footer>
              <view class="information">
                <view style="width: 330rpx; margin-right: 20rpx">
                  <uni-easyinput
                    v-model.trim="username"
                    placeholder="请输入.."
                    placeholderStyle="text-align: right"
                    :inputBorder="false"
                    :maxlength="30"
                  />
                </view>
                <view>
                  <uni-data-checkbox v-model="gender" mode="tag" :localdata="genderRange" />
                </view>
              </view>
            </template>
          </uni-list-item>
        </template>
      </uni-list>

      <div v-show="consumeSnHelp" class="err-msg">
        {{ consumeSnHelp }}
      </div>
    </div>
    <div class="verify-btn-wrap">
      <button class="normal-btn friend" :disabled="submitDisabled" @click="confirmVerify">确认核销</button>
    </div>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import lodash from 'lodash'
const { checkLogin } = useLogin()
const userStore = useUserStore()
const IMAGE_CDN = 'https://imagecdn.rocketbird.cn'
const THIRD_PLATFORM_NAME = [
  { value: '1', label: '美团', img: `${IMAGE_CDN}/mainsite-fe/logo-meituan.png` },
  { value: '2', label: '大众点评', img: `${IMAGE_CDN}/mainsite-fe/logo-dianping.png` },
  { value: '3', label: '抖音', img: `${IMAGE_CDN}/mainsite-fe/logo-ticktok.png` },
]
const getImgByType = (type: string) => {
  return THIRD_PLATFORM_NAME.find((item: any) => item.value === type)?.img
}
const postData = reactive({
  platform_id: '1',
  code: '',
  type: '',
  san_rule_id: '',
  card_rule_id: '',
  group_price: '',
  group_title: '',
})

const consumeSnHelp = ref('')
const goodsInfo = ref<Record<string, any>>({})
function resetGoodsInfo() {
  goodsInfo.value = {
    group_price: '',
    group_title: '',
    san_rule_id: '',
    card_rule_id: '',
  }
  postData.san_rule_id = ''
  postData.card_rule_id = ''
  postData.group_price = ''
  postData.group_title = ''
}
function radioChange(e) {
  postData.platform_id = e.detail.value
  resetGoodsInfo()
  postData.code = ''
  consumeSnHelp.value = ''
}
const checkCodeInfo = lodash.debounce(async () => {
  if (postData.code.length < 3) {
    return
  }
  try {
    postData.code = postData.code.replace(/\s+/g, '')
    consumeSnHelp.value = ''
    const res = await http.post('/Sangroup/getVoucher', {
      platform_id: postData.platform_id,
      code: postData.code,
      bus_id: userStore.userInfo.bus_id,
      showToast: false,
      user_id: userStore.userInfo.user_id,
    })

    if (res.errorcode === 0) {
      goodsInfo.value = res.data
      postData.group_price = goodsInfo.value.group_price
      postData.group_title = goodsInfo.value.group_title
      postData.card_rule_id = goodsInfo.value.card_rule_id
      postData.san_rule_id = goodsInfo.value.san_rule_id
      postData.type = goodsInfo.value.type

      storeFixed.value = Number(goodsInfo.value.use_pod) === 0
      if (hasStore.value && storeFixed.value) {
        storeId.value = userStore.userInfo.bus_id
        storeName.value = userStore.userInfo.bus_name
        uni.setStorageSync('check_store_id', storeId.value)
        uni.setStorageSync('check_store_name', storeName.value)
      } else {
        storeId.value = ''
        storeName.value = ''
        uni.removeStorageSync('check_store_id')
        uni.removeStorageSync('check_store_name')
      }

      if (hasApproach.value) {
        const count = Number(goodsInfo.value?.activation_restriction || 0)
        if (count === 1) {
          approachList.value = ['立即开卡']
        } else {
          approachList.value = ['立即开卡', '到场后再激活']
        }
      }

      // 套餐包立即开卡, 不展示开卡方式
      if (goodsInfo.value.rule_type === 8) {
        approachIndex.value = 0
      } else {
        approachIndex.value = -1
      }

      isUpdate.value = !!res.data.update_name
    }
  } catch (error: any) {
    resetGoodsInfo()
    consumeSnHelp.value = error.errormsg
    storeFixed.value = false
    approachIndex.value = -1
    isUpdate.value = false

    // mock start
    // goodsInfo.value = {
    //   group_price: '502.58',
    //   group_title: 'Handcrafted Fresh Hat',
    //   end_time: '2023-10-31 23:59:59',
    //   universal_card: 0,
    //   rule_type: 8,
    //   use_pod: 1,
    //   san_rule_id: '',
    //   activation_restriction: 47,
    // }

    // storeFixed.value = Number(goodsInfo.value.use_pod) === 0
    // if (hasStore.value && storeFixed.value) {
    //   storeId.value = userStore.userInfo.bus_id
    //   storeName.value = userStore.userInfo.bus_name
    // // } else {
    // //   storeId.value = ''
    // //   storeName.value = ''
    // }

    // if (hasApproach.value) {
    //   const count = Number(goodsInfo.value?.activation_restriction || 0)
    //   if (count === 1) {
    //     approachList.value = ['立即开卡']
    //   } else {
    //     approachList.value = ['立即开卡', '到场后再激活']
    //   }
    // }

    // if (goodsInfo.value.rule_type === 8) {
    //   approachIndex.value = 0
    // } else {
    //   approachIndex.value = -1
    // }

    // isUpdate.value = true
    // mock end
  }
}, 500)
async function confirmVerify() {
  if (!goodsInfo.value.group_title) {
    uni.showToast({
      title: '请输入正确的卡券',
      icon: 'none',
    })
    return
  }

  await checkLogin()

  // 添加加载状态
  uni.showLoading({
    title: '核销中...',
    mask: true,
  })

  http
    .post('CardRule/verification', {
      bus_id: userStore.userInfo.bus_id,
      verify_bus_id: storeId.value,
      user_id: userStore.userInfo.user_id,
      ...postData,
      buy_channel: postData.platform_id,
      consume_sn: postData.code,
      showToast: false,
      active_type: approachIndex.value,
      loading: false,
      username: username.value,
      sex: gender.value,
    })
    .then((res) => {
      uni.hideLoading()
      uni.redirectTo({
        url: `/packageMy/thirdParty/verifyResult?info=${encodeURIComponent(JSON.stringify(res.data))}`,
      })
    })
    .catch((error) => {
      uni.hideLoading()
      uni.showToast({
        title: error.errormsg,
        icon: 'none',
      })
    })
}

const hasStore = computed(() => {
  return (
    Number(goodsInfo.value.universal_card) === 0 &&
    Number(goodsInfo.value.rule_type) !== 5 &&
    !goodsInfo.value.san_rule_id
  )
})
const hasApproach = computed(() => [1, 6, 7].includes(Number(goodsInfo.value.rule_type)))
const submitDisabled = computed(() => {
  const base = !goodsInfo.value.group_title || !postData.code

  if (hasStore.value && hasApproach.value) {
    return base || !storeId.value || approachIndex.value === -1
  } else if (hasApproach.value) {
    return base || approachIndex.value === -1
  } else if (hasStore.value) {
    return base || !storeId.value
  } else {
    return base
  }
})

const activeTip = computed(() => {
  // 到场后，将在{product.activation_restriction}天后激活
  const count = Number(goodsInfo.value?.activation_restriction || 0)
  if (count > 45) {
    const months = count / 30
    const monthText = Number.isInteger(months) ? months : months.toFixed(2)
    return `${monthText}个月(${count}天)未到场，将自动激活`
  } else if (count === 0 || count === 1) {
    return ''
  } else {
    return `${count}天未到场，将自动激活`
  }
})

const storeId = ref('')
const storeName = ref('')
const storeFixed = ref(false)
const handleStoreChange = () => {
  if (storeFixed.value) {
    return
  }

  uni.navigateTo({
    url: '/packageMy/thirdParty/busSelect',
  })
}

const approachIndex = ref<number>(-1)
const approachList = ref<string[]>([])
const handleApproachChange = (event) => {
  approachIndex.value = Number(event.detail.value)
}

const isUpdate = ref<boolean>(false)
const username = ref<string>('')
const gender = ref<number>(0)
const genderRange = ref<any[]>([
  { value: 1, text: '先生' },
  { value: 2, text: '女士' },
])

onShow(() => {
  storeId.value = uni.getStorageSync('check_store_id')
  storeName.value = uni.getStorageSync('check_store_name')
})
</script>

<style lang="scss" scoped>
.err-msg {
  text-align: left;
  color: #ff0000;
  font-size: 24rpx;
  margin: 20rpx 0;
}
.verify-box {
  height: 100%;
  overflow-y: scroll;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/verfiy-bg.png') top center / 100% auto no-repeat;
  text-align: center;
}
.verify-con {
  box-sizing: border-box;
  width: 710rpx;
  margin: 430rpx auto 0;
  background: #fff;
  border-radius: 6rpx;
  overflow: hidden;
  padding: 0 25rpx 40rpx;
}
.verify-btn-wrap {
  width: 710rpx;
  margin: 40rpx auto 20rpx;
}
.verify-title {
  font-family:
    Alimama FangYuanTi VF-Bold,
    Alimama FangYuanTi VF-Bold;
  font-weight: normal;
  font-size: 30rpx;
  color: #000000;
  line-height: 38rpx;
  text-align: center;
  margin: 50rpx 0;
}
.check-list {
  display: flex;
  width: 100%;
  radio,
  checkbox {
    display: none;
  }
}
.verify-radio-item {
  flex: 1;
  border: 1px solid #c6c6c6;
  padding: 8rpx 8rpx 28rpx;
  border-radius: 10rpx;
  margin-right: 16rpx;
  font-size: 30rpx;
  color: #000;
  &:last-child {
    margin-right: 0;
  }
  image {
    display: block;
    width: 70rpx;
    height: 70rpx;
    margin: 20rpx auto;
  }
  &.checked {
    border-color: $theme-text-color-other;
  }
}
.verify-card {
  width: 100%;
  box-sizing: border-box;
  padding: 26rpx;
  display: flex;
  align-items: center;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/coupon-item-card-icon.png') right/ cover
    no-repeat #fff2ea;
  background-size: 150rpx 100%;
  border-radius: 10rpx;
  .verify-img {
    width: 100rpx;
    height: 100rpx;
    margin-right: 20rpx;
  }
  .verify-info {
    .title {
      font-size: 30rpx;
      font-weight: bold;
      color: #000;
      line-height: 38rpx;
      text-align: left;
      margin-bottom: 16rpx;
    }
    .text {
      font-size: 22rpx;
      color: #7d7d7d;
      line-height: 32rpx;
    }
  }
}
.input-wrap {
  position: relative;
  width: 100%;
  margin: 40rpx auto;
}
.input-wrap input {
  width: 618rpx;
  height: 80rpx;
  line-height: 80rpx;
  border: 2rpx solid #c6c6c6;
  border-radius: 10rpx;
  text-indent: 20rpx;
  text-align: left;
  &:focus,
  &:active,
  &:focus-visible {
    border-color: $theme-text-color-other;
  }
}

.verify-input {
  padding: 0 20rpx;
}

.required {
  font-weight: 400;
  font-size: 30rpx;
  color: #e1232a;
  margin-right: 4rpx;
}

.item-label {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #1f2227;
  line-height: 70rpx;
  font-style: normal;
  text-transform: none;
  text-align: left;
}

.item-value {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #7d7d7d;
  line-height: 70rpx;
  font-style: normal;
  text-transform: none;
}

.item-tip {
  font-size: 20rpx;
  color: #e1232a;
}

.information {
  width: 250rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  ::v-deep .uni-easyinput {
    text-align: right;
  }
}
</style>
