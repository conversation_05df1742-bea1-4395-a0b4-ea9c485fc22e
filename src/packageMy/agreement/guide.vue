<template>
  <view class="guide-wrap">
    <ComeInItem :type="2" />
    <image class="guide-img" mode="widthFix" @tap="handlePreview" :src="theme11.image_url" />
  </view>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/store/theme'
import ComeInItem from '@/components/ComeInItem.vue'
const themeStore = useThemeStore()
const theme11 = computed(() => {
  return themeStore.theme11
})
onLoad((optios) => {
  themeStore.getConfig({ type: 11 })
})
function handlePreview() {
  uni.previewImage({
    urls: [theme11.value.image_url]
  });
}
</script>
<style lang="scss" scoped>
.guide-wrap {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-y: scroll;
  .guide-img {
    display: block;
    width: 100%;
  }
}
</style>
