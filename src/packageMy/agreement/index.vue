<template>
  <view class="help-page theme-bg">
    <view class="list">
      <view class="item">
        <view class="item-content">
          <text>门店</text>
        </view>
        <view>
          <MerchantBusPick
            v-model="busId"
            :is-select-default="true"
            :is-filter-user-id-bus="true"
            :height="25"
            :has-all-bus-option="false"
            @change="handleBusChange"
          />
        </view>
      </view>
      <navigator class="item" url="/packageMy/agreement/private">
        <view class="item-content">
          <text>隐私政策</text>
        </view>
        <uni-icons type="forward" size="18" color="#bbb"></uni-icons>
      </navigator>

      <navigator class="item" url="/packageMy/agreement/user">
        <view class="item-content">
          <text>最终用户许可协议</text>
        </view>
        <uni-icons type="forward" size="18" color="#bbb"></uni-icons>
      </navigator>
      <!-- 场馆协议 -->
      <navigator v-if="config.is_open_bus_protocol === 1" class="item" url="/packageMy/agreement/busProtocol">
        <view class="item-content">
          <text>{{ config.bus_protocol_name || userStore.userInfo.bus_name }}</text>
        </view>
        <uni-icons type="forward" size="18" color="#bbb"></uni-icons>
      </navigator>

      <navigator v-if="config.is_open_buycard_protocol === 1" class="item" url="/pages/card/protocol">
        <view class="item-content">
          <text>购卡协议</text>
        </view>
        <uni-icons type="forward" size="18" color="#bbb"></uni-icons>
      </navigator>
    </view>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import MerchantBusPick from '@/components/MerchantBusPick.vue'

const userStore = useUserStore()
const busId = ref('')
const config = ref<Record<string, any>>({})
function getConfig(busId?: string) {
  http.get('Business/getProtocolConfig', { bus_id: busId || userStore.userInfoBusId }).then((res) => {
    if (res.errorcode === 0) {
      config.value = res.data.info
    }
  })
}
function handleBusChange({ bus_id }) {
  getConfig(bus_id)
}
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  .item-content {
    display: flex;
    overflow: hidden;
    flex-direction: row;
    flex: 1;
    padding-right: 16rpx;
  }
}
</style>
