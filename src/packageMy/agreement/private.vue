<template>
  <view class="agreenpage-wrap">
    <scroll-view class="agreen-wrap" :class="showAgree ? 'agree-pb' : ''" scroll-y @scrolltolower="scrollBottom">
      <view
        ><view align="center" style="text-align: center; line-height: 150%"
          ><text
            ><text style="line-height: 150%; font-weight: bold; font-size: 14px"
              ><text>关于勤鸟运动生活管家隐私政策的声明</text></text
            ></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 14px"></text></text></view
        ><view align="center" style="text-align: center; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            ><text>更新日期：</text>2</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            >021</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            ><text>年</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            >4</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            ><text>月</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            >30</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 0)"
            ><text>日</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>本隐私政策规定了勤鸟公司（下文简称</text
            >&#8220;勤鸟&#8221;或&#8220;我们&#8221;）如何收集、使用、披露、处理和保护您使用</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>我们在移动设备上提供的应用程序产品或服务时提供给我们的信息。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >我们非常重视您的个人信息和隐私保护，我们将会按照法律规定和业界成熟的安全标准，为您的个人信息提供相应的保护措施。本隐私政策在制定时充分考虑到您的需求，让您全面了解我们的个人信息收集政策，同时确保您最终能控制提供给勤鸟的个人信息。</text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>在本隐私政策中，</text
            >&#8220;个人信息&#8221;指通过信息本身或通过关联其他信息后能够识别特定个人的数据。此类个人信息包括但不限于您主动提供给我们的信息，我们系统指定与您相关的信息，以及根据您所使用的服务，相应收集的信息，可能包括财务信息，社交信息，位置信息，登录日志信息等。</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >如果使用勤鸟产品或服务，表示您已阅读、认可并接受本隐私政策中所有条款，包括我们随时做出的任何变更。你可以删除本应用或联系所在场馆工作人员删除您的个人信息数据。</text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 28.05pt"
          ><text><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 14px">1．</text></text
          ><text
            ><text style="font-weight: bold; font-size: 14px"><text>我们如何收集和使用您的个人信息</text></text></text
          ><text><text style="font-weight: bold; font-size: 14px"></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text>我们只会收集您选择的服务所必需的、特定的、明确及合法目的的信息。</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 255)"
            ><text
              >为了向您提供优质的健身指导服务和定制化的个人体验，基于产品和服务的功能用途要求，我们须收集和使用您的以下信息：</text
            ></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 255)"></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 255)"></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1.1您提供的信息，包含但不限于以下信息：</text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text name="_Hlk68990276"></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.1.1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >您在使用我们的服务时向我们或我们的合作方所提供的信息，如您的姓名、电子邮箱、联系方式、地址等；</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.1.2</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >您在使用我们的服务设备时录入的生物特征信息，如您的指静脉识别信息、指纹、人脸识别信息等；</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.1.3</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text>您在使用我们的服务时，基于系统功能收集的信息，如系统日志信息、位置信息、设备信息等；</text></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.1.4</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >合作方分享的您的信息，即我们的合作方基于为您提供优质服务，通过使用勤鸟产品或服务时所收集的有关您的信息。</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1.2我们会出于以下目的，收集和使用您的以下个人信息：</text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.2.1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >为帮助您完成用户注册及登录，我们需要您提供的基本注册信息，如您的手机号码、电子邮件地址等；如您采取其他已有账号进行登录，我们需要收集和使用您的其他账号信息（昵称、手机号码、头像、性别、年龄、身高、体重等），除必要信息外，您可以选择填写；</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.2.2</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text>为了帮助您进行身份认证，我们可能会需要您提供真实的身份信息（如姓名、</text
              ><text name="_Hlk68078948"><text>身份证或护照、面部特征、指纹、指静脉</text></text
              ><text
                >及其他身份信息）以完成身份认证。如果您不提供上述信息，我们将无法向您提供依照相关法律法规必须完成身份认证的功能及服务；</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.2.3</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >为保障您正常使用我们的服务，维护我们服务的正常运行，改进及优化我们的服务体验以及保障您的账号安全，我们可能会收集您的操作系统、登陆</text
              >IP地址、操作日志、服务日志信息（如您的搜索、查看操作的记录、服务故障信息）等日志信息，这类信息是为提供相应服务必须收集的基础信息；</text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.2.4</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >为了帮助您在系统内完成下单、支付等交易，当您在系统内下单交易时，我们需要收集您提供的订单信息，如收货人信息等；在完成订单支付交易时，我们需要基于您对交易方式的选择，收集您的交易账号、订单信息、交易、支付、物流等信息；</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.2.5</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >为了向您提供服务或实现系统功能，我们可能会收集您提供的个人健康信息、健身数据信息、体检信息等，用以与您的健身计划进行匹配和跟踪服务；</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >1</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              >.2.6</text
            ></text
          ><text
            ><text style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
              ><text
                >为了向您提供商品或服务的信息展示和推送，我们可能会收集和使用您的健身方案、身体数据信息、运动频率、运动项目等信息，向您提供个性化的商品或服务产品信息推荐。</text
              ></text
            ></text
          ><text
            ><text
              style="color: rgb(51, 51, 51); font-weight: bold; font-size: 12px; background: rgb(255, 255, 255)"
            ></text></text></view
        ><view style="text-indent: 28.05pt"
          ><text><text style="font-weight: bold; font-size: 14px">2</text></text
          ><text><text style="font-weight: bold; font-size: 14px">.</text></text
          ><text
            ><text style="font-weight: bold; font-size: 14px"><text>个人信息的管理</text></text></text
          ><text><text style="color: rgb(255, 0, 0); font-weight: bold; font-size: 14px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px">2.1个人资料管理</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>为了管理您的个人资料数据，并提升运</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>动数据的准确性，我们需要收集您的个人资料，包括</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>姓名、身份证或护照、面部特征、指纹、指静脉、</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>性别、出生日期、身</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>高</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>、体重、兴趣爱好、运动设置数据（比如运动目标、心率区间等）。您</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>可以前往</text></text
          ><text style="line-height: 150%; font-size: 12px">&#8220;我的&#8221;</text
          ><text style="line-height: 150%; font-size: 12px">&#62;</text
          ><text style="line-height: 150%; font-size: 12px">&#8220;个人资料&#8221;或联系所在场馆工作人员进行</text
          ><text style="line-height: 150%; font-size: 12px"><text>填写或修改。若您不提供这类信息，</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>将影响相关服务的使用</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px">2.2运动数据管理</text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px"><text>为记录、备份和管理您的运动数据，我</text></text
          ><text style="line-height: 150%; font-size: 12px"
            ><text
              >们需要收集您的位置信息、运动类型、运动时长、步数、距离、热量、运动心率以及其他运动数据。运动类型包括走路、跑步、骑车等。收集此类信息的目的是记录并统计您的日常运动数据，为您提供丰富的运动服务。</text
            ></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px">2.4用户体验改进服务</text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px"><text>为改进产品质量并提升使用体验，我们</text></text
          ><text style="line-height: 150%; font-size: 12px"
            ><text
              >需要收集您在应用中打开的页面和点击的按钮、应用的功能运行情况，用来分析统计应用的使用情况，改进服务和用户使用体验。以上数据只进行整体分析，不会</text
            ></text
          ><text style="line-height: 150%; font-size: 12px"><text>涉及</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>您的</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>任何</text></text
          ><text style="line-height: 150%; font-size: 12px"
            ><text>个人信息，在使用完成后会删除所有相关数据。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 28.05pt"
          ><text><text style="font-weight: bold; font-size: 14px">3.设备权限调用</text></text
          ><text><text style="font-weight: bold; font-size: 14px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>相机权限：当您</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>需要</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>通过扫描二维码的方式</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>连接设备</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>时</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>，我们需要向您申请并获取该权限，以便完成图片拍</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>摄。如您不需要此类服务，可以</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>选择不再启用</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>该权限，这不会影响其他服务的使用。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>位置权限：当您使用签到</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>、</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>营销活</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>动、</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >蓝牙配对或其他与位置相关的搜索等功能时，您可以选择开启该权限，用于确定您的当前位置信息。如您不需要此类服务，可以随时关闭该权限，这不会影响其他服务的使用。</text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>您可以随时访问系统</text>&#8220;</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>设置</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px">&#8221;</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>来管理您的系统权限。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt"
          ><text><text style="font-weight: bold; font-size: 15px">4</text></text
          ><text><text style="font-weight: bold; font-size: 15px">.</text></text
          ><text
            ><text style="font-weight: bold; font-size: 15px"><text>对未成年人的保护</text></text></text
          ><text><text style="font-weight: bold; font-size: 15px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px"><text>如果您是未成年人，需要您的监护人同</text></text
          ><text style="line-height: 150%; font-size: 12px"
            ><text
              >意您使用本应用并同意相关应用的服务条款。父母和监护人也应釆取适当的预防措施来保护未成年人，包括监督其对本应用的使用</text
            ></text
          ><text style="line-height: 150%; font-size: 12px"><text>。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt"
          ><text><text style="font-weight: bold; font-size: 15px">5</text></text
          ><text><text style="font-weight: bold; font-size: 15px">.</text></text
          ><text
            ><text style="font-weight: bold; font-size: 15px"><text>删除个人信息</text></text></text
          ><text><text style="font-weight: bold; font-size: 15px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>在</text></text
          ><text style="line-height: 150%; font-size: 12px">&#8220;我的&#8221;</text
          ><text style="line-height: 150%; font-size: 12px">&#62;</text
          ><text style="line-height: 150%; font-size: 12px">&#8220;隐私管理&#8221;</text
          ><text style="line-height: 150%; font-size: 12px"
            ><text>中可以随时删除运动数据，数据删除后不可找回。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px"
            ><text>您可以通过注销勤鸟</text>&#8220;运动生活管家&#8221;账户或联系所在场馆工作人员</text
          ><text style="line-height: 150%; font-size: 12px"><text>清除您</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>提供</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>的所有个人数据。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt; line-height: 150%"
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">6</text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">.更新个人信息</text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; font-size: 12px"><text>您可以在</text>&#8220;</text
          ><text style="line-height: 150%; font-size: 12px"><text>我的</text>&#8221;</text
          ><text style="line-height: 150%; font-size: 12px">&#62;</text
          ><text style="line-height: 150%; font-size: 12px">&#8220;个人资料&#8221;</text
          ><text style="line-height: 150%; font-size: 12px"><text>页面中修改更新您的昵称、头像、性别、</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>出生日期、</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>身高</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>、体重，其中从手机或</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>其他</text></text
          ><text style="line-height: 150%; font-size: 12px"
            ><text>设备上自动获取的数据，不提供编辑更新功能。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt; line-height: 150%"
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">7</text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">.撤回</text></text
          ><text
            ><text style="line-height: 150%; font-weight: bold; font-size: 15px"><text>授权</text></text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text></view
        ><view
          style="
            text-indent: 24px;
            punctuation-wrap: simple;
            text-autospace: none;
            line-height: 150%;
            background: rgb(255, 255, 255);
          "
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>授权是指经您允许，我方使用您的个人信息及您的个人硬件为您提供服务的过程。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view
          style="
            text-indent: 24px;
            punctuation-wrap: simple;
            text-autospace: none;
            line-height: 150%;
            background: rgb(255, 255, 255);
          "
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>本服务的</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>硬件使用</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>授权</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>无需撤销，每次使用均需要您独立授权，整体授权的撤</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>销及更改见微信（</text>WECHAT）的设置或参照本声明第3条&#8220;设备权限调用&#8221;</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view
          style="
            text-indent: 24px;
            punctuation-wrap: simple;
            text-autospace: none;
            line-height: 150%;
            background: rgb(255, 255, 255);
          "
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>本服务的个人信息授权，</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>您可向所在场馆联系工作人员申请撤回授权</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>，</text></text
          ><text style="line-height: 150%; font-size: 12px"
            ><text>撤回该授权后，会影响部分功能的正常使用。如您使用相关功能受阻时，可重新授权。</text></text
          ><text style="line-height: 150%; font-size: 12px"></text></view
        ><view
          style="
            text-indent: 24px;
            punctuation-wrap: simple;
            text-autospace: none;
            line-height: 150%;
            background: rgb(255, 255, 255);
          "
          ><text style="line-height: 150%; font-size: 12px"
            ><text>关于您使用本服务过程中生成、提交的个人信息，您可以通过</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>本声明中</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px">&#8220;联系我们&#8221;</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>所述方式与我们取得联系，并行使您的相关权利。</text></text
          ><text style="font-family: 等线; font-size: 10.5pt"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>如您对您的数据主体权利有进一步要求</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>或存在任何疑问、意见或建议，可通过</text><text>本声明中</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px">&#8220;联系我们&#8221;</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>所述方式与我们取得联系，并行使您的相关权利。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt; line-height: 150%"
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">8</text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">.</text></text
          ><text
            ><text style="line-height: 150%; font-weight: bold; font-size: 15px"
              ><text>个人信息的委托处理</text></text
            ></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text style="color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 255)"
            ><text>公司产品和服务中的某些模块或功能由</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"><text>第三方服务供应商</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 255)"
            ><text>提供。</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >为了向您提供产品或服务的全部功能，我们可能不时向我们的第三方服务供应商（包括但不限于数据中心、数据存储设施、客户服务供应商）</text
            ></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text>披露您的个人信息。此类第三方服务供应商可能代表</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"><text>勤鸟</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text>或出于上述的一项或多项目的处理您的个人信息。</text></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt; line-height: 150%"
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">9.个人信息的</text></text
          ><text
            ><text style="line-height: 150%; font-weight: bold; font-size: 15px"><text>共享</text></text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >我们不会主动共享或转让您的个人信息至第三方，如存在其他共享或转让您的个人信息或您需要我们将您的个人信息共享或转让至第三方情形时，我们会直接征得或确认第三方征得您对上述行为的明示同意。</text
            ></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >我们不会对外公开披露收集的个人信息，如必须公开披露时，我们会向您告知此次公开披露的目的、披露信息的类型及可能涉及的敏感信息，并征得您的明示同意。</text
            ></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view
          class="p"
          align="justify"
          style="text-indent: 24px; text-align: justify; text-justify: inter-ideograph; line-height: 150%"
          ><text style="color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >随着我们业务的持续发展，我们有可能进行合并、收购、资产转让等交易，我们将告知您相关情形，按照法律法规及不低于本指引所要求的标准继续保护或要求新的控制者继续保护您的个人信息。</text
            ></text
          ><text style="color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt; line-height: 150%"
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">10.数据存储地点及期限</text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>上述信息将会保存在中华人民共</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>和国境内的服务器。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>我们仅在实现本声明所述目的所必需的</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>时间内保留您的个人信息，并在超出法律</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>规定的</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>保留时间后删除或匿名化处理您的个人信息。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>为了您的数据安全，您的数据会和账号</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>绑定，您的数据会</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>通过算法</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>加密传输并保</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>存在</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>重庆勤鸟圈科技有限公司使用的</text></text
          ><text style="line-height: 150%; font-size: 12px"><text>云服务器，直到您</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>联系所在场馆工作人员</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>删除云</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>端</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>数据。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>如果您需要删除您保留在本公司的全部个人原始信息，</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>可通过本声明中</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px">&#8220;联系我们&#8221;</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>所述方式与我们取得联系，并行使您的相关权利。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>此外，当我们的产品或服务发生停止运</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >营的情形时，我们将以推送通知、公告等形式通知您，并在合理的期限内删除您的个人信息或进行匿名化</text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>脱敏</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>处理。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 30.05pt; line-height: 150%"
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px">11.联系我们</text></text
          ><text><text style="line-height: 150%; font-weight: bold; font-size: 15px"></text></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>如您对本隐私政策或个人信息保护有任</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>何疑问或需要进行投诉，您可以通过</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>公司客服电话或电子邮箱</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>与</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>我们</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>取得联系，我们会尽快回复</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>并处理</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>。</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>如果您对我们的回复不满意，特别是当</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text
              >我们的个人信息处理行为损害了您的合法权益时，您还可以通过向有管辖权的人民法院提起诉讼或政府相关管理机构投诉等外部途径进行解决。</text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px; background: rgb(255, 255, 255)"
            ><text>重庆勤鸟圈科技有限公司</text></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>办公地址：重庆市渝北区财富大道</text>1号9-5</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
            ><text>客服电话：</text>400-160-7266</text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"><text>官方网址：</text></text
          ><text
            ><text href="https://www.rocketbird.cn"
              ><text
                class="17"
                style="
                  line-height: 150%;
                  color: rgb(0, 0, 255);
                  text-decoration: underline;
                  text-underline: single;
                  font-size: 12px;
                "
                >https://www.rocketbird.cn</text
              ></text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
        ><view style="text-indent: 24px; line-height: 150%"
          ><text
            ><text href="mailto:公司邮箱：<EMAIL>"
              ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"
                ><text>公司邮箱：</text><EMAIL></text
              ></text
            ></text
          ><text style="line-height: 150%; color: rgb(51, 51, 51); font-size: 12px"></text></view
      ></view>
    </scroll-view>
    <view v-if="showAgree" class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <button class="normal-btn finished" @tap="updateAgreeStatus(false)">关闭</button>
        <button class="normal-btn" :disabled="!isScrollBottom" @tap="updateAgreeStatus('1')">同意</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const instanceEventChannel = ref(null)
const isScrollBottom = ref(false)
const showAgree = ref(false)
onLoad((option) => {
  instanceEventChannel.value = getCurrentInstance().proxy.getOpenerEventChannel()
  showAgree.value = option.showAgree || false
})
function scrollBottom() {
  isScrollBottom.value = true
}
function updateAgreeStatus(status) {
  instanceEventChannel.value.emit('acceptAgreementStatus', { isAgreeStatus: status })
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.agreenpage-wrap {
  width: 100%;
  height: 100%;
}
.agreen-wrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 10rpx;
  background: #fff;
  color: #000;
}
.agree-pb {
  padding-bottom: 150rpx;
}
</style>
