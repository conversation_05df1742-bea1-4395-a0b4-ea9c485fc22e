<template>
  <view class="record-wrapper">
    <view class="title-wrap">
      <view class="time">时间</view>
      <view class="action">动作</view>
      <view class="cabinet">柜号</view>
    </view>
    <view class="record-content">
      <view v-for="item in recordList" :key="item.device_id" class="record-item">
        <view class="time">{{ item.create_time }}</view>
        <view class="action">
          <!-- 1存，2取，3退 ，4清 -->
          <text
            class="action-tag"
            :class="{ red: item.action === 4, orange: item.action === 3, green: item.action === 2 }"
          >
            {{ item.action_name }}
          </text>
        </view>
        <view class="cabinet">{{ item.cabinet_name }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin } = useLogin()

const postData = reactive({
  bus_id: '',
  user_id: '',
  page_no: 1,
  page_size: 20,
})
const recordList = ref<Record<string, any>[]>([])
onShow(async () => {
  checkLogin().then((res) => {
    postData.bus_id = res.bus_id
    postData.user_id = res.user_id
    getList()
  })
})
function getList() {
  http.post('Cabinets/getCabinetRecordList', postData).then((res) => {
    recordList.value = res.data.list
  })
}
</script>

<style lang="scss" scoped>
.record-wrapper {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0 15rpx;
  text-align: center;
  .time {
    width: 300rpx;
  }
  .action,
  .cabinet {
    flex: 1;
  }
}
.title-wrap {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-weight: bold;
}
.record-item {
  width: 100%;
  height: 100rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: #fff;
  margin-bottom: 10rpx;
}
.action-tag {
  display: block;
  margin: 0 auto;
  width: 92rpx;
  height: 50rpx;
  line-height: 50rpx;
  background: rgba(6, 190, 252, 0.2);
  border-radius: 4rpx;
  border: 1rpx solid #06befc;
  &.green {
    background: rgba(161, 234, 43, 0.2);
    border-color: #a1ea2b;
  }
  &.orange {
    background: rgba(255, 116, 39, 0.3);
    border-color: #ff7427;
  }
  &.red {
    background: rgba(247, 44, 108, 0.3);
    border-color: #f72c6c;
  }
}
</style>
