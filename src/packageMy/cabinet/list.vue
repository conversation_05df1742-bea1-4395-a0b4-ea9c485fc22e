<template>
  <view class="page-wrap theme-bg">
    <view class="locker-wrap">
      <view class="locker-top">
        <view class="tit">
          {{ deviceName }}
        </view>
        <view class="top-tips">
          <view><image class="locker-tips" :src="`${imageCdn}cabinet-bg1.png`" />空闲</view>
          <view><image class="locker-tips" :src="`${imageCdn}cabinet-bg2.png`" />已用</view>
          <view><image class="locker-tips" :src="`${imageCdn}cabinet-bg3.png`" />已选</view>
        </view>
      </view>
      <view class="locker-list">
        <block v-for="(item, index) in lockerList" :key="index">
          <view
            class="locker"
            :class="[
              item.cabinet_id === selectedItem.cabinet_id ? 'bg3' : item.status === 1 ? 'bg2' : 'bg1',
              (index + 1) % 3 !== 0 || (index + 1) % 6 === 0 ? 'mr' : '',
            ]"
            @tap="selectItem(item)"
          >
            <text class="num">{{ item.cabinet_id }}</text>
            <image
              :src="`${imageCdn}cabinet-${
                item.cabinet_id === selectedItem.cabinet_id ? 'bg3' : item.status === 1 ? 'bg2' : 'bg1'
              }.png`"
            />
          </view>
          <view v-if="(index + 1) % 3 === 0 && (index + 1) % 6 !== 0" class="locker-mid"
            >{{ lockerList[index - 2].cabinet_id }}-{{
              index + 3 >= lockerList.length
                ? lockerList[lockerList.length - 1].cabinet_id
                : lockerList[index + 3].cabinet_id
            }}</view
          >
        </block>
      </view>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <view class="foot-label"
          >已选：<text v-if="selectedItem.cabinet_id" class="num">{{ selectedItem.cabinet_id }}</text></view
        >
      </view>
      <button class="normal-btn" @tap="handleSubmitClick">提交</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
const { checkLogin, getParam } = useLogin()
const imageCdn = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/'
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
const deviceName = ref('')
const timestamp = ref('')
const deviceId = ref('')
const action = ref('')
const lockerList = ref([])
const selectedItem = reactive({
  cabinet_id: '',
  device_id: '',
})
const curOption = ref<Record<string, any>>({})
onLoad((options) => {
  curOption.value = options || {}
})
onShow(async () => {
  const curParams = await getParam(curOption.value.scene || '')
  deviceId.value = curParams.device_id || curOption.value.device_id
  timestamp.value = curParams.timestamp || curOption.value.timestamp
  action.value = curParams.action || curOption.value.action
  uni.setNavigationBarTitle({
    title: action.value === 'qrcabinet' ? '扫码选柜号' : '自主选柜',
  })
  checkLogin(true, curParams.bus_id || curOption.value.bus_id).then((info) => {
    Object.assign(loginUserInfo, info)
    validateLocker()
    getLocker()
  })
})
function validateLocker() {
  const url = action.value === 'qrcabinet' ? 'Qrcabinet/getUserRentInfo' : 'Cabinets/validateUserCabinet'
  http
    .post(url, {
      ...loginUserInfo,
      device_id: deviceId.value,
      timestamp: timestamp.value || '',
      showToast: false,
    })
    .then((res) => {
      if (action.value === 'qrcabinet' && res.data.length > 0) {
        uni.redirectTo({ url: `/packageMy/cabinet/info?from=list&device_id=${deviceId.value}` })
      } else if (res.status === 1 && action.value !== 'qrcabinet') {
        uni.redirectTo({
          url: '/pages/my/ticket?from=index',
        })
      }
    })
}
function getLocker() {
  const url = action.value === 'qrcabinet' ? 'Qrcabinet/getAllCabinets' : 'Cabinets/getAllCabinets'
  http
    .get(url, {
      ...loginUserInfo,
      timestamp: timestamp.value || '',
      device_id: deviceId.value,
    })
    .then((res) => {
      lockerList.value = res.data.list
      deviceName.value = res.data.device_name
    })
}
function selectItem(item) {
  if (selectedItem.cabinet_id === item.cabinet_id) {
    selectedItem.cabinet_id = ''
  } else if (item.status !== 1) {
    Object.assign(selectedItem, item)
  }
}

function handleSubmitClick() {
  if (!selectedItem.cabinet_id) {
    uni.showToast({ title: '请先选柜', icon: 'none' })
  }
  if (action.value === 'qrcabinet') {
    uni.showModal({
      title: `确认使用${selectedItem.cabinet_id}号储物柜吗?`,
      confirmColor: '#CA2E53',
      cancelColor: '#A0A0A0',
      success: (res) => {
        if (res.confirm) {
          rentRequest()
        }
      },
    })
  } else {
    rentRequest()
  }
}

function rentRequest(params: type) {
  const url = action.value === 'qrcabinet' ? 'Qrcabinet/rent' : 'Cabinets/saveUserCabinet'
  http
    .get(url, {
      ...loginUserInfo,
      device_id: selectedItem.device_id,
      cabinet_no: selectedItem.cabinet_id,
      cabinet_id: selectedItem.cabinet_id,
    })
    .then((res) => {
      if (action.value === 'qrcabinet') {
        if (selectedItem.device_id) {
          uni.redirectTo({
            url: `/packageMy/cabinet/info?from=list&bus_id=${loginUserInfo.bus_id}&device_id=${selectedItem.device_id}`,
          })
        } else {
          uni.redirectTo({ url: `/packageMy/cabinet/info?from=list&bus_id=${loginUserInfo.bus_id}` })
        }
      } else {
        uni.redirectTo({
          url: '/pages/my/ticket?from=index',
        })
      }
    })
}
</script>

<style lang="scss" scoped>
.page-wrap {
  width: 100%;
  height: 100%;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  overflow: hidden;
  flex: 1;
  display: flex;
}
.locker-wrap {
  margin: 20rpx 20rpx;
  border-radius: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.locker-tips {
  display: inline-block;
  vertical-align: middle;
  width: 24rpx;
  height: 26rpx;
}
.locker-top {
  padding: 30rpx 34rpx;
  font-size: 30rpx;

  .tit {
    font-weight: bold;
    margin-bottom: 28rpx;
  }
  .top-tips {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 20rpx;
    view {
      margin-right: 30rpx;
    }
    image {
      margin-right: 16rpx;
    }
  }
}
.locker-list {
  background: #e1e3e5;
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  flex-wrap: wrap;
  padding: 36rpx 0 8rpx 36rpx;
  flex: 1;
  overflow-y: scroll;
  .locker {
    width: 68rpx;
    height: 75rpx;
    position: relative;
    margin-bottom: 24rpx;
    color: #7d7d7d;

    &.bg2,
    &.bg3 {
      color: #fff;
    }

    &.mr {
      margin-right: 28rpx;
    }

    .num {
      position: absolute;
      left: 50%;
      top: 6rpx;
      font-size: 30rpx;
      transform: translateX(-50%);
    }

    image {
      width: 100%;
      height: 100%;
    }
  }
  .locker-mid {
    font-size: 22rpx;
    color: #707070;
    width: 118rpx;
    height: 75rpx;
    line-height: 75rpx;
    text-align: center;
  }
}

.foot-label {
  height: 100%;
  margin-left: 45rpx;
  line-height: 100rpx;
  .num {
    font-weight: bold;
  }
}
</style>
