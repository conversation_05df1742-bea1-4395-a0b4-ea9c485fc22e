<template>
  <view class="shower-wrapper theme-bg">

    <scroll-view class="cabinet-box" scroll-y="true" enable-flex>
      <!-- Display when there are rental items -->
      <view v-if="rentalList.length === 1" style="height: 130rpx"></view>
      <view v-if="rentalList.length > 0">
        <view v-for="item in rentalList" :key="item.device_id" class="rental-item">
          <BaseInfo :bath-num="item.cabinet_no" type="储物柜" />
          <view class="rental-info-content">
            <!-- Show usage time for each rental item -->
            <view class="usage-info">
              <text class="usage-time">{{ calculateUsageTime(item.rent_time) }}</text>
              <text class="rent-info">{{ item.rent_time }} 租用</text>
            </view>
          </view>
        </view>
      </view>

      <!-- Display when no rental items -->
      <view v-else>
        <image
          class="cabinet-nodata"
          src="https://imagecdn.rocketbird.cn/minprogram/uni-member/cabinet-nodata.png"
        />
        <view class="bath-info-content">
          <text class="single-price">{{ infoTop }}</text>
          <text class="brand-limit">{{ infoBottom }}</text>
        </view>
      </view>
    </scroll-view>

    <BathTips v-if="rentalList.length > 0" :tips-info="tipsInfo" />
    <view v-if="rentalList.length > 0" class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <button v-if="from === 'list'" class="normal-btn outer-org mgr" @tap="rentBack">退柜离场</button>
        <button v-if="from === 'list'" class="normal-btn" hover-class="none" @tap="openCabinet">开启柜门</button>
        <button v-if="from !== 'list'" class="normal-btn outer-org" @tap="allRentBack">退柜离场</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import BaseInfo from './components/BaseInfo'
import BathTips from './components/BathTips'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin, getParam } = useLogin()
const from = ref('')
// const bathNum = ref('')
const tipsInfo = ref('请在离场前点击“退柜离场”释放储物柜使用权')
const infoTop = ref('')
const timeInt = ref('')
const infoBottom = ref('')
const rentalList = ref<Array<{cabinet_no: string, device_id: string, rent_time: string}>>([]) // Store the full rental list from API
const postData = reactive({
  bus_id: '',
  user_id: '',
})
const curOption = ref<any>(null)
onLoad((options) => {
  curOption.value = options
  from.value = options.from || ''
})
onShow(async () => {
  const curParams = await getParam(curOption.value.scene || '')
  checkLogin(true, curParams.bus_id || curOption.value.bus_id).then((res) => {
    postData.bus_id = res.bus_id
    postData.user_id = res.user_id
    validateLocker()
  })
})
function validateLocker() {
  http
    .post('Qrcabinet/getUserRentInfo', {
      user_id: postData.user_id,
      from: from.value,
      bus_id: postData.bus_id,
      device_id: curOption.value?.device_id || '',
      showToast: false,
    })
    .then((res) => {
      rentalList.value = res?.data|| []

      if (Array.isArray(rentalList.value) && rentalList.value.length === 0) {
        setNoCabinetStatus()
      }
    })
}
function setNoCabinetStatus() {
  rentalList.value = [] // Clear the rental list
  // bathNum.value = '--'
  infoTop.value = '未查询到租柜信息'
  infoBottom.value = '未发现有使用中的储物柜，请联系管理员协助'
}

// Calculate usage time for individual rental items
function calculateUsageTime(rentTime: string) {
  try {
    const dateBegin = new Date(rentTime)
    const dateEnd = new Date()
    const dateDiff = dateEnd.getTime() - dateBegin.getTime()
    const dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)) // 天数
    const leave1 = dateDiff % (24 * 3600 * 1000) // 计算天数后剩余的毫秒数
    const hours = Math.floor(leave1 / (3600 * 1000))
    const leave2 = leave1 % (3600 * 1000)
    const minutes = Math.floor(leave2 / (60 * 1000))

    return `已使用${dayDiff && dayDiff > 0 ? dayDiff + '天' : ''}${hours && hours > 0 ? hours + '小时' : ''}${
      minutes && minutes > 0 ? minutes + '分钟' : ''
    }`
  } catch (error) {
    return '计算使用时间失败'
  }
}
function openCabinet() {
  const { device_id, cabinet_no } = rentalList.value[0]
  http
    .post('Qrcabinet/open', {
      ...postData,
      from: from.value,
      device_id,
      cabinet_no,
    })
    .then((res) => {
      uni.showToast({
        title: `【${cabinet_no}】储物柜已打开`,
        icon: 'none',
      })
    })
}
function rentBack() {
  const { device_id, cabinet_no } = rentalList.value[0]
  uni.showModal({
    title: '离场退柜后将释放储物柜使用权。',
    confirmColor: '#CA2E53',
    cancelColor: '#A0A0A0',
    success: (res) => {
      if (res.confirm) {
        http
          .post('Qrcabinet/rentBack', {
            ...postData,
            from: from.value,
            device_id,
            cabinet_no,
          })
          .then((res) => {
            setNoCabinetStatus()
            validateLocker()
            uni.showToast({
              icon: 'none',
              title: '储物柜已退，期待您再次光临！',
            })
          })
      }
    },
  })
}
const allRentBack = () => {
  uni.showModal({
    title: '离场退柜后将释放储物柜使用权。',
    confirmColor: '#CA2E53',
    cancelColor: '#A0A0A0',
    success: (res) => {
      if (res.confirm) {
        const cabinet_list = rentalList.value.map((item) => {
          return {
            device_id: item.device_id,
            cabinet_no: item.cabinet_no,
          }
        })
        http
          .post('/Qrcabinet/rentBackBatch', {
            ...postData,
            from: from.value,
            cabinet_list: JSON.stringify(cabinet_list),
          })
          .then((res) => {
            setNoCabinetStatus()
            validateLocker()
            uni.showToast({
              icon: 'none',
              title: '储物柜已退，期待您再次光临！',
            })
          })
      }
    },
  })
}
function timeFn(time) {
  if (timeInt.value) {
    clearInterval(timeInt.value)
  }
  tiemSet(time)
  timeInt.value = setInterval(() => {
    tiemSet(time)
  }, 60 * 1000)
}
function tiemSet(time) {
  const dateBegin = new Date(time)
  const dateEnd = new Date()
  const dateDiff = dateEnd.getTime() - dateBegin.getTime()
  const dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)) //天数
  const leave1 = dateDiff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
  const hours = Math.floor(leave1 / (3600 * 1000))
  const leave2 = leave1 % (3600 * 1000)
  const minutes = Math.floor(leave2 / (60 * 1000))
  const leave3 = leave2 % (60 * 1000)
  const seconds = Math.round(leave3 / 1000)
  infoTop.value = `已使用${dayDiff && dayDiff > 0 ? dayDiff + '天' : ''}${hours && hours > 0 ? hours + '小时' : ''}${
    minutes && minutes > 0 ? minutes + '分钟' : ''
  }`
}
</script>

<style lang="scss" scoped>
.cabinet-box {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 306rpx;
}

.cabinet-nodata {
  display: block;
  width: 162rpx;
  height: 248rpx;
  margin: 100rpx auto 0;
}
.shower-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .rental-item {
    margin-bottom: 40rpx;
    padding-bottom: 40rpx;
    border: 1rpx solid #f0f0f0;
    background-color: #f2f2f2;
    margin: 20rpx 40rpx;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  .rental-info-content {
    width: 100%;
    margin-top: 30rpx;
    // padding: 0 40rpx;
  }

  .usage-info {
    text-align: center;

    .usage-time {
      display: block;
      font-size: 36rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .rent-info {
      display: block;
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 400;
      color: #666;
    }
  }

  .bath-info-content {
    width: 100%;
    text-align: center;
    margin-top: 50rpx;
    .single-price {
      display: block;
      height: 40rpx;
      font-size: 36rpx;
      font-family: PingFang SC;
      font-weight: bold;
    }
    .brand-limit {
      display: block;
      height: 30rpx;
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 400;
      margin-top: 20rpx;
      line-height: 37px;
    }
    .occupied {
      color: rgba(202, 46, 83, 1);
    }
  }
  .consume-amount {
    margin-left: 50%;
    transform: translateX(-50%);
    width: 431rpx;
    margin-top: 70rpx;
    background-color: rgba(245, 247, 249, 1);
    border-radius: 10rpx;
    line-height: 116rpx;
    .item-border {
      border-bottom: 2rpx solid rgba(229, 229, 229, 1);
      box-sizing: border-box;
    }
    > view {
      font-family: PingFang SC;
      display: block;
      width: 100%;
      font-size: 32rpx;
      color: rgba(27, 27, 27, 1);
      font-weight: 400;
      .consume-item {
        width: 50%;
        font-size: 34rpx;
        display: inline-block;
        text-align: center;
      }
      .consume-detail {
        text-align: center;
        display: inline-block;
        color: rgba(202, 46, 83, 1);
        font-weight: bold;
        font-size: 43rpx;
      }
    }
  }
}
page {
  background-color: #ffffff;
}
</style>
