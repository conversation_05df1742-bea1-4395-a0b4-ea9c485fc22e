<template>
  <view :class="['card-wrapper', { 'selected-card': isSelected }]" @tap="onCardSelected">
    <view>
      <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/bath-card-3.png" />
      <text>{{ cardName }}</text>
      <text>¥{{ leftValue }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  cardName: {
    type: String,
    default: '会员卡',
  },
  leftValue: {
    type: String,
    default: '--',
  },
  isSelected: {
    type: Boolean,
  },
  detailInfo: {
    type: Object,
  },
})
const emits = defineEmits(['on-selected'])
function onCardSelected() {
  emits('on-selected', props.detailInfo && props.detailInfo.card_user_id)
}
</script>

<style lang="scss" scoped>
.card-wrapper {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  > view {
    width: 325rpx;
    height: 211rpx;
    position: relative;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    > image {
      display: block;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
    }
    > text {
      position: absolute;
    }
    > text:nth-of-type(1) {
      top: 24rpx;
      left: 32rpx;
      font-size: 25rpx;
      font-family: PingFang SC;
      font-weight: 400;
      color: rgba(49, 49, 49, 1);
    }
    > text:nth-of-type(2) {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      -webkit-transform: translate(-50%, -50%);
      font-size: 51rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: rgba(49, 49, 49, 1);
    }
  }
}
.selected-card {
  box-shadow: 0prx 5rpx 10rpx 0rpx rgba(65, 92, 145, 0.15);
  border-radius: 10rpx;
  border: 2rpx solid rgba(81, 222, 211, 1);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
</style>
