<template>
  <view class="brand-wrapper" :class="size === 'small' ? 'bath-no-small' : ''">
    <view class="brand-container">
      <image class="banner-red" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/brand.png" />
      <text :class="size === 'small' ? 'brand-no-small' : 'brand-no'">{{ bathNum }}</text>
      <text :class="size === 'small' ? 'brand-title-small' : 'brand-title'">{{ type }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">

const props = defineProps({
  bathNum: {
    type: [String, Number],
    default: '--',
  },
  type: {
    type: String,
    default: '淋浴间',
  },
  size: {
    type: String,
    default: 'normal',
  },
})
</script>

<style lang="scss" scoped>
.brand-wrapper {
  width: 269rpx;
  height: 266rpx;
}
.bath-no-small {
  width: 182rpx;
  height: 182rpx;
}
.brand-container {
  width: 100%;
  height: 100%;
  position: relative;
  .banner-red {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    display: block;  
  }
  .brand-no {
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 55rpx;
    font-size: 77rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #000;
  }
  .brand-no-small {
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 40rpx;
    font-size: 62rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #000;
  }
  .brand-title-small {
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 120rpx;
    font-size: 29rpx;
    font-family: PingFang SC;
    font-weight: 400;
    color: #000;
  }
  .brand-title {
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 163rpx;
    font-size: 36rpx;
    font-family: PingFang SC;
    font-weight: 400;
    color: #000;
  }
}
</style>
