<template>
   <view class="shower-bg">
    <BathroomNo class="center-center" :bathNum="bathNum" :type="type" />
  </view>
</template>

<script setup lang="ts">
import BathroomNo from "./BathroomNo";

const props = defineProps({
  bathNum: {
    type: [String, Number],
    default: '--',
  },
  type: {
    type: String,
    default: '淋浴间',
  }
})
</script>

<style lang="scss" scoped>
.shower-bg {
  width: 100%;
  height: 345rpx;
  position: relative;
  .center-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 60rpx;
  }
}
</style>
