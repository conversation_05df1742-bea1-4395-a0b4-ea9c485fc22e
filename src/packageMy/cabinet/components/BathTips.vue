<template>
 <view class="bath-tips-bottom">
    <view class="warm-tips">
      <uni-icons type="info-filled" size="20" color="#ff7427"></uni-icons>
      <view>温馨提示：</view>
    </view>
    <text class="rent-back">{{ tipsInfo }}</text>
  </view>
</template>

<script setup lang="ts">

const props = defineProps({
  tipsInfo: {
    type: String,
    default: '使用完淋浴间记得退租哦，退租后将进行费用结算和停止供水。'
  }
})
</script>

<style lang="scss" scoped>
.bath-tips-bottom {
  position: fixed;
  bottom: 190rpx;
  padding: 0 48rpx;
  .warm-tips {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #ff7427;
  }
  .rent-back {
    display: block;
    margin-top: 16rpx;
    line-height: 1.5;
  }
}
</style>
