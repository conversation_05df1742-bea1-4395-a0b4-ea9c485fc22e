import env from '@/config/env'
import Auth from '@/utils/auth'
import { useUserStore } from '@/store/user'
let loadingCount = 0
const commonParams = {
  loading: true,
}
function showLoading(loading) {
  if (loading) {
    uni.showLoading({
      title: '加载中',
      mask: true,
    })
    loadingCount = loadingCount + 1
  }
}

function hideLoading() {
  loadingCount = loadingCount - 1
  if (loadingCount <= 0) {
    uni.hideLoading()
    loadingCount = 0
  }
}

function errTips(err: { errorcode: number; msg?: String; errormsg: String }) {
  const { errormsg = '网络错误，稍候片刻！', msg, errorcode = -1 } = err
  uni.showToast({
    noConflict: true,
    duration: 3000,
    title: msg || errormsg,
    icon: 'none',
  })
}
function getGatewayParams() {
  const userStore = useUserStore()
  const accountInfo = uni.getAccountInfoSync()
  const appid = accountInfo.miniProgram.appId
  return {
    v: '1.0.10', // 版本号
    appId: appid,
    openid: userStore.userInfo.openid,
    Client: 'wechat', // 固定字符串【支付宝：alipay】【微信：wechat】
    apiKey: appid, // 勤鸟网关使用
  }
}
function getUrl(url, envUrlType) {
  const apiBaseUrl = env[envUrlType || 'apiBaseUrl']
  const isLineStart = /^\/.*/.test(url)
  const apiIsLineEnd = /.*\/$/.test(apiBaseUrl)
  const isUrl = /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/.test(url)
  if (isUrl) {
    return url
  } else if (apiIsLineEnd && isLineStart) {
    return apiBaseUrl + url.slice(1)
  } else if (!apiIsLineEnd && !isLineStart) {
    return apiBaseUrl + '/' + url
  }
  return apiBaseUrl + url
}

function getTokenHeader(params) {
  const token = Auth.getToken()
  if(!token) return {}
  const timestamp = Auth.getMillisecond()
  const sign = Auth.signature(token, params, timestamp)
  return {
    token,
    timestamp,
    sign,
  }
}

function baseRequest(method, url: string, data, envUrlType?) {
  return new Promise((resolve, reject) => {
    showLoading(data.loading)
    const showToast = typeof data.showToast !== 'undefined' ? data.showToast : true
    delete data.loading
    delete data.showToast
    uni.request({
      url: getUrl(url, envUrlType),
      method,
      timeout: 30000,
      header: {
        ...getGatewayParams(),
        ...getTokenHeader(data),
        'content-type': 'application/x-www-form-urlencoded',
      },
      data,
      success: (res: any) => {
        hideLoading()
        if (res.statusCode >= 200 && res.statusCode < 400) {
          if (res.data.errorcode === 0 || (res.data.status === 0 && envUrlType === 'veinUrl')) {
            resolve(res.data)
          } else {
            showToast && errTips(res.data)
            //errorcode=60059表示refreshToken需要重新登录 errorcode=60060表示accessToken过期需要重新获取
            if (res.data.errorcode === 60059) {
              Auth.logout()
            } else if (res.data.errorcode === 60060) {
              Auth.postToRefreshToken()
            }
            reject(res.data)
          }
        } else {
          const errObj = {
            errorcode: -1,
            errormsg: '网络异常，稍候片刻！',
          }
          showToast && errTips(errObj)
          reject(errObj)
        }
      },
      fail: (err) => {
        hideLoading()
        const tipsObj = {
          errorcode: -1,
          errormsg: err.errMsg || '网络不给力，请检查你的网络设置~',
        }
        showToast && errTips(tipsObj)
        reject(tipsObj)
      },
    })
  })
}

const http = {
  getGatewayParams,
  get: <T>(api, params, envUrlType?) =>
    baseRequest('GET', api, { ...commonParams, ...params }, envUrlType) as Http.Response,
  post: <T>(api, params, envUrlType?) =>
    baseRequest('POST', api, { ...commonParams, ...params }, envUrlType) as Http.Response,
}

export default http
