import CryptoJS from 'crypto-js'
import { useUserStore } from '@/store/user'
import http from '@/utils/request'

export default class Auth {
  private static isRefreshing = false
  private static isLogoutTriggered = false

  /**
   * 生成签名
   * @param {string} token - 令牌
   * @param {Object} params - 请求参数对象
   * @param {number} timestamp - 时间戳
   * @return {string} - 生成的签名
   */
  static signature(token, params, timestamp) {
    try {
      // 第一步：对请求参数进行排序，并拼接参数名和参数值
      const data = Auth.ascSort(params)
      // 第二步：拼接字符串 "token + data + token + timestamp"
      const str = token + data + token + timestamp
      const sign = Auth.md5(str).toUpperCase()
      return sign
    } catch (error) {
      console.error('签名生成失败：', error)
      return false
    }
  }

  /**
   * 对对象参数按照键名 ASCII 码排序，并拼接参数名和参数值
   * @param {Object} params - 请求参数对象
   * @return {string} - 排序后拼接的字符串
   */
  static ascSort(params) {
    if (params && typeof params === 'object') {
      const sortedKeys = Object.keys(params).sort()
      let str = ''
      for (const key of sortedKeys) {
        str += key + params[key]
      }
      return str
    }
    return ''
  }

  /**
   * 获取毫秒级时间戳
   * @return {number} - 当前时间戳（毫秒）
   */
  static getMillisecond() {
    return new Date().getTime()
  }

  /**
   * 生成 MD5 摘要
   * @param {string} str - 需要生成 MD5 的字符串
   * @return {string} - 生成的 MD5 值
   */
  static md5(str) {
    // 使用 CryptoJS 生成 MD5
    return CryptoJS.MD5(str).toString()
  }

  static getToken() {
    const tokenData = wx.getStorageSync('token_data')
    if (!tokenData) {
      return ''
    }
    if (tokenData && tokenData.token && tokenData.expiresAt > Auth.getMillisecond()) {
      return tokenData.token
    } else if (tokenData.token) {
      this.postToRefreshToken()
      return tokenData.token
    }
    return ''
  }

  static setToken(token) {
    const newExpiresAt = Auth.getMillisecond() + 3600 * 1000 // 设token 有效期为 1 小时（后端两个小时的时候过期）
    const tokenData = {
      token,
      expiresAt: token?newExpiresAt:'',
    }
    return wx.setStorageSync('token_data', tokenData)
  }

  static clearToken() {
    return wx.removeStorageSync('token_data')
  }

  static getRefreshToken() {
    return wx.getStorageSync('refresh_token') || ''
  }
  static setRefreshToken(value) {
    return wx.setStorageSync('refresh_token', value || '')
  }
  static clearRefreshToken() {
    return wx.removeStorageSync('refresh_token')
  }

  static postToRefreshToken() {
    const refreshToken = Auth.getRefreshToken()
    if (!refreshToken || Auth.isRefreshing) {
      return false
    }
    Auth.isRefreshing = true
    const userStore = useUserStore()
    return http
      .post('Auth/refreshAccessToken', {
        user_id: userStore.userInfoUserId,
        refresh_token: refreshToken,
      })
      .then((res) => {
        Auth.isRefreshing = false
        Auth.setToken(res.data || '')
        return true
      })
      .catch((err) => {
        Auth.isRefreshing = false
        return false
      })
  }
  static logout() {
    if (!this.isLogoutTriggered) {
      this.isLogoutTriggered = true
      this.clearToken()
      this.clearRefreshToken()
      const userStore = useUserStore()
      userStore.setUserInfo({
        user_id: '',
      })
      wx.reLaunch({
        url: '/pages/login',
        complete: () => {
          // 退出一次后，延迟10秒，再置为false，防止一个页面请求多个接口的重复执行
          setTimeout(() => {
            this.isLogoutTriggered = false
          }, 10 * 1000)
        },
      })
    }
  }
}
