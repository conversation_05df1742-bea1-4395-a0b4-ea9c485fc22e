/* 
  保存图片分享相关
 */

// 将图片地址保存到用户相册
export function savePhotosAlbum(filePath, callBack) {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath: unref(filePath),
      success() {
        uni.hideLoading()
        uni.showModal({
          title: '提示',
          content: '保存到相册成功,去分享给好友吧！',
          showCancel: false,
          success: function (res) {
            if (typeof callBack === 'function') {
              callBack()
            }
          },
        })
        resolve()
      },
      fail(err) {
        uni.hideLoading()
        reject(err)
      },
    })
  })
}

// 将canvas转化为图片保存到用户相册，方便分享
export function shareCanvasInPhoto(canvasInfoRef) {
  const canvasInfo = unref(canvasInfoRef)
  uni.canvasToTempFilePath({
    x: 0,
    y: 0,
    width: canvasInfo.width,
    height: canvasInfo.height,
    destWidth: canvasInfo.width,
    destHeight: canvasInfo.height,
    canvasId: canvasInfo.id,
    success: async function (res) {
      const filePath = res.tempFilePath
      try {
        await savePhotosAlbum(filePath, canvasInfo.callBack)
      } catch (error) {
        uni.hideLoading()
        canvasInfo.callBack()
        uni.navigateTo({
          url: '/pages/infoAuthor',
        })
      }
    },
    fail: function (res) {
      uni.hideLoading()
    },
  })
}
