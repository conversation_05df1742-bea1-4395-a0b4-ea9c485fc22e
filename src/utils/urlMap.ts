import pagesJson from '../pages.json'
import { parseUrl } from './shared'
import { useLogin } from '@/hooks/useLogin'
import { useUserStore } from '@/store/user'
import { useMerchant } from '@/store/merchant'
import { useThemeStore } from '@/store/theme'
// 获取当前页面路径
export function getCurrentPagePath(): string {
  const pages = getCurrentPages()
  const page = pages[pages.length - 1]
  if (!page) {
    console.error('Current page is undefined, please use this function in the page context.')
    return ''
  }
  return page.route // 返回当前页面的路径
}
export function isTabBarPage(path?: string) {
  if (!path) {
    path = getCurrentPagePath()
  }
  const tabBarPages = pagesJson.tabBar.list.map((i) => i.pagePath)
  if (path.indexOf('/') === 0) {
    path = path.substr(1)
  }
  return tabBarPages.indexOf(path) !== -1
}
// 检查bus_id和本地bus_id是否匹配，并切换到对应bus_id
export async function checkBusIdAndSwitch(busId: string | undefined) {
  const userStore = useUserStore()
  const useMerchantStore = useMerchant()
  if (busId && busId !== userStore.userInfoBusId) {
    userStore.setUserId(await useMerchantStore.getUserIdByBus(busId))
    userStore.setBusId(busId)
    if (useMerchantStore.userInfo.bus_name) {
      userStore.setBusName(useMerchantStore.userInfo.bus_name)
    }
  }
}
/**
 * Navigate to the given URL. If the URL contains a colon, navigate to another mini-program.
 * 注意：商家模式下，场馆会不断切换，防止切换后进行操作需要拿bus_id和user_id不正确的情况，当url参数中含有bus_id时，我们需要预先进行全局bus_id切换再跳转页面，这样在跳转页面时就能通过checkLogin拿到正确的bus_id和user_id
 * @param url - The URL to navigate to.
 * @returns void
 */
export async function goUrlPage(url: string, params: Record<string, string | boolean> = {}): void {
  url = decodeURIComponent(url)
  // 以:分割的url代表跳转到另一个小程序
  if (url.indexOf(':') !== -1) {
    const urlArr: string[] = url.split(':')
    const { checkLogin } = useLogin()
    checkLogin().then((info) => {
      const userQuery = `bus_id=${info.bus_id}&user_id=${info.user_id}`
      wx.navigateToMiniProgram({
        appId: urlArr[0],
        path: urlArr[1].indexOf('?') !== -1 ? `${urlArr[1]}&${userQuery}` : `${urlArr[1]}?${userQuery}`,
        envVersion: 'release', //要打开的小程序版本。仅在当前小程序为开发版或体验版时此参数有效。如果当前小程序是正式版，则打开的小程序必定是正式版
        fail(err) {
          if (err.errMsg.indexOf('navigateToMiniProgram:fail invalid appid') !== -1) {
            uni.showModal({
              title: '提示',
              content: '小程序appid无效',
              showCancel: false,
            })
          } else if (err.errMsg.indexOf('navigateToMiniProgram:fail cancel') !== -1) {
            console.log('取消跳转')
          } else if (err.errMsg.indexOf('navigateToMiniProgram:fail') !== -1) {
            uni.showModal({
              title: '提示',
              content: '跳转失败',
              showCancel: false,
            })
          }
        }
      })
    })
    return
  }
  const { path, query, fullPath } = parseUrl(url)
  Object.assign(query, params)
  const queryMode = query.isShowMerchantMode === 'true'
  const thmeStore = useThemeStore()
  const isTabBarTag = isTabBarPage(path)
  // 当跳转场馆首页时 可能带有是否显示商家模式的参数
  if (query.isShowMerchantMode !== undefined) {
    thmeStore.changeShowMerchantMode(queryMode)
  }
  try {
    await checkBusIdAndSwitch(query.bus_id)
  } catch (error) {
    console.log(error)
  }
  if (isTabBarTag) {
    const pages = getCurrentPages()
    const page = pages[pages.length - 1]
    const currentPagePath = page.route
    // 在场馆首页做的操作
    if (currentPagePath === 'pages/index/bus') {
      uni.setStorageSync('shouldBackToBusPage', true)
    } else {
      uni.setStorageSync('shouldBackToBusPage', false)
    }
    // tabbar页面switchTab方法参数无法带过去 需要预先处理
    if (path === '/pages/class/class') {
      uni.setStorageSync('switchClassPageQuery', query)
    }
    if (queryMode && path === '/pages/index/index') {
      uni.reLaunch({ url: fullPath })
    } else {
      uni.switchTab({ url: path })
    }
  } else if (params.isRedirectTo) {
    uni.redirectTo({ url: fullPath })
  } else {
    uni.navigateTo({ url: fullPath })
  }
}
