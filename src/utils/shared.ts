/**
 * 格式化时间
 * @param  {Date} source 时间对象
 * @param  {string} format 格式
 * @return {string}        格式化过后的时间
 */
export function formatDate(source: Date, format: string): string {
  const o = {
    'M+': source.getMonth() + 1, // 月份
    'd+': source.getDate(), // 日
    'H+': source.getHours(), // 24小时
    'h+': source.getHours() % 12 === 0 ? 12 : source.getHours() % 12, // 12小时制
    'm+': source.getMinutes(), // 分
    's+': source.getSeconds(), // 秒
    'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
    'f+': source.getMilliseconds(), // 毫秒
    't+': source.getHours() < 12 ? 'am' : 'pm',
    'T+': source.getHours() < 12 ? 'AM' : 'PM',
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (source.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return format
}

/**
 * Returns a new date after adding the specified number of days to the given date.
 * @param  {string | Date} date - The base date to add days to.
 * @param  {number} days - The number of days to add to the base date.
 * @returns {string} - The new date as a string in the format "YYYY-MM-DD".
 */
export function addDays(date: string | Date, days: number): string {
  const getDate = new Date(date)
  const nd = new Date(getDate.getTime() + days * 24 * 60 * 60 * 1000)
  const y = nd.getFullYear()
  let m = nd.getMonth() + 1
  let d = nd.getDate()
  if (m <= 9) m = '0' + m
  if (d <= 9) d = '0' + d
  const cdate = `${y}-${m}-${d}`
  return cdate
}

/**
 * 两个日期的差值(天)
 * @param {string} d1 - 第一个日期
 * @param {string} d2 - 第二个日期
 * @returns {number} 返回天数差值
 */
export function dateDiff(d1: string, d2: string): number {
  const day: number = 24 * 60 * 60 * 1000
  const date1: Date = new Date(d1)
  const date2: Date = new Date(d2)
  if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
    return 0
  }
  const timeDiff: number = date1.getTime() - date2.getTime()
  return Math.floor(timeDiff / day)
}

export function queryParser(search) {
  return JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', function (key, value) {
    return key === '' ? value : decodeURIComponent(value)
  })
}

/**
 * Parses a url and returns its path, name, and query parameters
 * @param fullPath the full url to parse
 * @returns an object containing the path, name, and query parameters
 */
export function parseUrl(fullPath: string): {
  name: string
  path: string
  query: { [key: string]: string }
} {
  // 解析url 如果不包含?但是包含&的情况 需要将第一个&替换为?
  if (!fullPath.includes('?') && fullPath.includes('&')) {
    fullPath = fullPath.replace(/&/, '?')
  }
  const [path, queryStr] = fullPath.split('?')
  const name = path.slice(path.lastIndexOf('/') + 1)
  const query: { [key: string]: string } = {}
  queryStr
    ?.split('&')
    .map((i) => i.split('='))
    .forEach((i) => (query[i[0]] = i[1]))
  return {
    name,
    path,
    fullPath,
    query,
  }
}

// 还原url
export function restoreUrl(path: string, query: Object) {
  let count = 0
  for (const key in query) {
    path += `${count === 0 ? '?' : '&'}${key}=${query[key]}`
    count += 1
  }
  return path
}

export function getCardTypeUnit(card_type_id: number | string, is_pt_time_limit_card: number | string | undefined) {
  if (is_pt_time_limit_card == 1) return '天'
  return card_type_id == 1 ? '天' : card_type_id == 2 ? '次' : card_type_id == 3 ? '元' : '节'
}
export function arrIndexOf(arr, value) {
  if (!Array.isArray(arr)) {
    return false
  }
  if (arr.indexOf(value) < 0) {
    return false
  }
  return true
}

export function unescapeHTML(a) {
  a = '' + a
  return a
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&nbsp;/g, ' ')
    .replace(/<img/g, '<img class="rich-text-img"')
    .replace(/<font/g, '<span')
    .replace(/<\/font>/g, '</span>')
    .replace(/<u>/g, '<ins>')
    .replace(/<u /g, '<ins ')
    .replace(/<\/u>/g, '</ins>')
    .replace(/<s>/g, '<del>')
    .replace(/<s /g, '<del ')
    .replace(/<\/s>/g, '</del>')
}
