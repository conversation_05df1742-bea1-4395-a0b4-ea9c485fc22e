declare namespace User {
  type UserInfo = {
    m_id: string
    merchant_user_id: string
    bus_id: string
    merchant_name: string
    bus_name: string
    user_id: string
    openid: string
    unionid: string
    is_display_self_qr: number
    phone: string
  }
  type locationInfo = {
    longitude: string
    latitude: string
  }

  type sceneInfo = {
    scene: string
    params: null | {
      [propName: string]: string | number
    }
  }
  
}
declare namespace Theme {
  type operationModeType = 1 | 0 | undefined // 后台设置的运营模式 1 综合体育场馆模式 0 健身瑜伽模式
  type ThemeItemArr = {
    temp_type: string | number,
    [propName: string]: string | number
  }[]
  type ThemeState = {
    tabBarBus: string
    isEntranceReady: boolean
    isConfigReady: boolean
    operationMode: operationModeType // 当前运营模式
    isShowMerchantMode: boolean | undefined // 当前页面的显示模式是否为商家模式，只有运营模式为综合体育场馆模式时才应该生效
    //1外观颜色 2首页装修 3预约页面装修 4我的页面装修 5底部导航 6登录页
    theme1: {
      background_color: number
      fashion_color: string
      member_open: 1 | 0 // 场馆过期或者闭店时候禁止使用（适用于闭店、过期或者门店装修期间等异常 让会员无法使用本门店会员端）
    }
    theme2: Record<string, any>
    theme3: ThemeItemArr
    theme4: Record<string, any>
    theme5: Record<string, any>[]
    theme6: Record<string, any>
    theme7: Record<string, any>
    //卡课列表页面装修
    theme8: Record<string, any>
    theme10: {
      bus_id: string
    }
  }
  type rgbInfo = {
    r: Number
    g: Number
    b: Number
  }
}
