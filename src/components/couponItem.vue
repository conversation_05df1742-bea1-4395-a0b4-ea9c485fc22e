<template>
  <view class="coupon-wrap" :class="{ 'style-other': isStyleOther }" @tap.stop="handleCatchTap">
    <view
      class="top-info-row"
      :class="{ 'disabled-bgc': coupon.can_choose === undefined ? coupon.status !== 1 : coupon.can_choose !== 1 }"
    >
      <view class="top-left-money">
        <view class="price">
          <text>¥{{ coupon.discount_amount }}</text>
        </view>
        <view class="condition">满{{ coupon.limit_amount }}元可用</view>
      </view>

      <view class="top-center-basic">
        <view class="title">{{ coupon.name }}</view>
        <view class="useful-life">{{ coupon.begin_date }}~{{ coupon.end_date }}</view>
      </view>

      <view class="top-right-box">
        <slot name="right">
          <view v-if="coupon.can_choose !== 1 && coupon.status !== 1" class="mark-box">
            {{
              coupon.status === 2
                ? '已使用'
                : coupon.status === 3
                ? '已赠送'
                : coupon.status === 4
                ? '已过期'
                : coupon.status
            }}
          </view>
        </slot>
      </view>
    </view>
    <view class="bottom-info-row">
      <view class="left-info" :class="{ 'drop-down': !isStyleOther, 'info-more': isShowInfo }" @tap="handleShowInfo">
        <view v-if="userLimitText" class="show-view warn-color">{{ userLimitText }}</view>
        <view v-if="coupon.limit_card">{{ limitCardText }}</view>
        <view v-if="coupon.limit_space_type === 2 || coupon.limit_space_type === 1">
          支持使用的场地：{{ coupon.limit_space_type === 2 ? coupon.space_type_species : '全部场地' }}
        </view>
        <view v-if="coupon.limit_san_rule === 2 || coupon.limit_san_rule === 1">
          支持使用的散场票：{{ coupon.limit_san_rule === 2 ? coupon.san_rule_species : '全部散场票' }}
        </view>
        <view v-if="coupon.limit_class === 2 || coupon.limit_class === 1">
          支持使用的团课：{{ coupon.limit_class === 2 ? coupon.class_rule_species : '全部团课' }}
        </view>
      </view>
      <view v-if="isShowBtn" class="rig">
        <button
          v-if="coupon.can_presented !== 2"
          class="normal-btn normal-btn-small outer-green"
          open-type="share"
          :data-bus-id="coupon.bus_id || ''"
          :data-coupon-id="coupon.coupon_receive_id"
          :data-coupon-name="coupon.name"
        >
          转赠
        </button>
        <button class="normal-btn normal-btn-small" @tap.stop="handleUseCard">使用</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="couponItem">
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps({
  coupon: {
    type: Object,
    required: true,
  },
  // 是否按照其它样式展示 用于赠送折扣券后的用户领取页面
  isStyleOther: {
    type: Boolean,
    default: false,
  },
  isShowBtn: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['click-item'])
const isShowInfo = ref(false)

const cardTypeList = ['', '除储值卡所有卡种', '仅限购会员卡(除储值卡)', '仅限购教练课', '仅限购指定卡种']

const userLimitText = computed(() => {
  const useLimit = {
    '1': '购卡购课',
    '2': '续卡续课',
    '3': '升级',
    '4': '订场',
    '5': '散场票',
    '6': '团课',
  }
  return `${
    props.coupon.use_limit
      ? props.coupon.use_limit
          .split(',')
          .map((v) => useLimit[v])
          .join('、')
      : useLimit['1']
  } 可用`
})
const limitCardText = computed(() => {
  const { apply_card, specify_card, specify_card_str } = props.coupon
  if (Array.isArray(specify_card)) {
    const limitText = cardTypeList[apply_card]
    let specifyText = ''
    if (apply_card === 4) {
      specifyText = '：' + (specify_card_str || specify_card.map(({ name }) => name).join('，'))
    }
    return limitText + specifyText
  }
  return specify_card
})

const handleCatchTap = () => {
  emits('click-item', props.coupon)
}

function handleUseCard() {
  // ([123]) 表示匹配数字 1 或 2 或 3
  if (/[123]/.test(props.coupon.use_limit)) {
    goUrlPage(`/pages/card/list?bus_id=${props.coupon.bus_id || ''}`)
  } else {
    // 订场-散场票跳场地 团课跳团课
    const type = props.coupon.use_limit.match(/4|5/) ? 3 : 1
    goUrlPage(`/pages/class/class?type=${type}&bus_id=${props.coupon.bus_id || ''}`)
  }
}

function handleShowInfo() {
  isShowInfo.value = !isShowInfo.value
}
</script>

<style lang="scss" scoped>
.drop-down {
  padding-right: 40rpx;
  &::after {
    right: 10rpx;
  }
  &.info-more {
    &::after {
      border-width: 0 8rpx 10rpx;
    }
  }
}
.coupon-wrap {
  overflow: hidden;
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  width: 100%;
  min-height: 200rpx;
  box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
}
.top-info-row {
  display: flex;
  overflow: hidden;
  position: relative;
  align-items: center;
  box-sizing: border-box;
  padding: 0 28rpx;
  height: 140rpx;
  background-color: var(--THEME-COLOR);
  font-size: 24rpx;
  color: #03080e;
  &.disabled-bgc {
    background-color: #d3d3d3;
  }
  &::before {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 50%;
    width: 322rpx;
    height: 322rpx;
    background-color: rgba(255, 255, 255, 0.3);
    content: '';
    transform: translate(-37%, -23.6%);
  }
  &::after {
    position: absolute;
    right: 0;
    top: 0;
    width: 147rpx;
    height: 100%; // 140rpx
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/coupon-item-card-icon.png') center/ cover
      no-repeat;
    content: '';
  }
  .top-left-money {
    z-index: 1;
    margin-right: 10rpx;
    min-width: 190rpx;
    .price {
      margin-bottom: 6rpx;
      font-weight: bold;
      font-size: 36rpx;
    }
    .condition {
      font-size: 22rpx; // 位置略小
    }
  }
  .top-center-basic {
    flex: 1;
    max-width: 394rpx;
    .title {
      margin-bottom: 6rpx;
      font-weight: bold;
      font-size: 30rpx;
    }
  }
  .top-right-box {
    margin-left: auto;
    .mark-box {
      border: 1rpx solid #e60012;
      width: 92rpx;
      height: 34rpx;
      line-height: 32rpx;
      text-align: center;
      color: #e60012;
      transform: rotate(20deg);
    }
  }
}
.bottom-info-row {
  display: flex;
  z-index: 1;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  padding: 20rpx 28rpx;
  min-height: 60rpx;
  background: #fff;
  font-size: 20rpx;
  color: $theme-text-color-grey;
  .left-info {
    color: #7d7d7d;
    view {
      display: none;
    }
    .show-view {
      display: block;
    }
  }
  .info-more {
    display: block;
    view {
      display: block;
    }
  }
  .warn-color {
    color: $theme-text-color-other;
  }
  .rig {
    display: flex;
    button {
      margin-right: 10rpx;
    }
  }
}
.style-other {
  &.coupon-wrap {
    width: 710rpx;
    height: 249rpx;
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/gift-coupon-bg.png') center/ cover no-repeat;
    box-shadow: none;
  }
  .top-info-row,
  .bottom-info-row {
    background: transparent !important;
  }
  .top-left-money {
    width: 200rpx;
    .condition {
      color: $theme-text-color-other;
    }
  }
  .top-info-row {
    height: 100%;
    &::before,
    &::before {
      display: none;
    }
    .top-center-basic {
      padding-bottom: 100rpx;
    }
  }
  .bottom-info-row {
    position: absolute;
    left: 208rpx;
    top: 120rpx;
  }
}
</style>
