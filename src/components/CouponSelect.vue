<template>
  <view class="item coupon-item">
    <view class="label">
      <view class="prefix">券</view>
      <text>折扣券抵扣</text>
    </view>
    <view class="value">
      <text v-if="!hasUse" class="no-coupon">暂无可用折扣券</text>
      <view v-else class="rig-text" @tap="handleToDiscountPage">
        {{ state.coupon_receive_id == 0 ? '不使用折扣券' : '¥ ' + state.coupon_amount }}
      </view>
    </view>
  </view>
  <view v-if="!notShowBill" class="item">
    <view class="label">
      <text>优惠后需支付</text>
    </view>
    <view class="value price"> ¥ {{ bill }} </view>
  </view>
</template>

<script setup lang="ts" name="CouponSelect">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
interface Props {
  useLimit: number //使用场景，4订场地5购散场票 6团课预约
  amount: number //当前支付原金额
  id: number | string //当前id，默认0，当$use_limit==4，此字段为当前场地类型id，当$use_limit==5，此字段为当前散场票id，当$use_limit==6，此字段为当前class_id 依次类推
  notShowBill?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  useLimit: 4,
  amount: 0,
  id: 0,
})
const couList = ref<Record<string, any>[]>([])
const hasUse = ref(false) // 是否有可用折扣券
const userStore = useUserStore()
const state = reactive({
  coupon_receive_id: 0, // 折扣券id
  coupon_amount: 0, // 折扣金额
})
//使用折扣券后的金额
const bill = computed(() => {
  const inAmount = props.amount - state.coupon_amount
  return (inAmount > 0 ? inAmount : 0).toFixed(2)
})
const emit = defineEmits(['on-change'])
watch(
  [() => state, bill],
  ([val, old]) => {
    emit('on-change', { ...val, bill: bill.value })
  },
  { immediate: true }
)
watch(() => props.amount, getList, { immediate: true })

function getList() {
  http
    .get('/Coupon/getCouponListNew', {
      user_id: userStore.userInfo.user_id,
      bus_id: userStore.userInfo.bus_id,
      use_limit: props.useLimit,
      amount: props.amount,
      current_id: props.id,
    })
    .then((res) => {
      const resData = res.data
      state.coupon_receive_id = resData.coupon_receive_id
      hasUse.value = +resData.coupon_receive_id !== 0
      state.coupon_amount = resData.coupon_amount
      couList.value = resData.coupon_list
    })
}
// 跳转到折扣券选择页面 并监听折扣券选择
function handleToDiscountPage() {
  uni.navigateTo({
    url: '/pages/card/couponList',
    events: {
      // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
      'confirm-coupon': ({ couponReceiveId, couponAmount }) => {
        if (couponReceiveId !== undefined) {
          state.coupon_receive_id = couponReceiveId
          state.coupon_amount = couponAmount
        } else {
          state.coupon_receive_id = 0
          state.coupon_amount = 0
        }
      },
    },
    success(res) {
      // 通过eventChannel向被打开页面传送数据
      const { coupon_receive_id, coupon_amount } = state
      res.eventChannel.emit('init-coupon', {
        couList: couList.value,
        couponReceiveId: coupon_receive_id,
        couponAmount: coupon_amount,
        showNotUse: true,
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
.coupon-item {
  .label {
    display: flex;
    align-items: center;
    .prefix {
      margin-right: 12rpx;
      border-radius: 6rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: var(--THEME-COLOR);
      line-height: 32rpx;
      text-align: center;
      font-weight: bold;
      font-size: 21rpx;
      color: #000;
    }
  }
  .value {
    padding: 15rpx;
    padding-right: 0;
    min-width: 50%;
    text-align: right;
    // .no-coupon,
    // .rig-text {
    //   width: 100%;
    //   text-align: right;
    // }
  }
}
</style>
