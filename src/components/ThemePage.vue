<template>
  <view class="page" :class="[dataTheme]">
    <view class="page-main" :data-theme="dataTheme" :style="themeStyleVars">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/store/theme'
const themeStore = useThemeStore()
const themeStyleVars = ref('')
const dataTheme = ref('default')
watchEffect(() => {
  const info = themeStore.theme1
  const rgbInfo = themeStore.getRGB()
  dataTheme.value = info.background_color == 2 ? 'dark' : 'default'
  themeStyleVars.value = `--THEME-COLOR:#${info.fashion_color};--THEME-RGB:${rgbInfo.r},${rgbInfo.g},${rgbInfo.b};`
  uni.setNavigationBarColor({
    frontColor: info.background_color == 2 ? '#ffffff' : '#000000',
    backgroundColor: info.background_color == 2 ? '#000000' : '#ffffff',
  })
})
</script>

<style lang="scss" scoped>
.page {
  &.default {
    background-color: #ffffff;
  }
  &.dark {
    background-color: #000000;
  }
}
</style>
