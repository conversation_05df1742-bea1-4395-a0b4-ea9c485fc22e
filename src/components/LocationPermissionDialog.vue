<template>
  <view v-if="visible" class="location-permission-dialog-container">
    <view class="location-permission-dialog" :style="dialogStyle">
      <view class="dialog-content">
        <view v-show="!small" class="dialog-text">
          {{ isDenied ? '未授权定位' : '开启定位授权服务' }}
          <view class="dialog-subtext">{{ isDenied ? '我们无法根据您的位置推荐附近门店' : '开启后，将为你推荐附近门店' }}</view>
        </view>
        <button class="enable-location-btn" :style="{ background: isDenied ? '#ff5722' : '#07c160' }" @tap="handleEnableLocation">开启定位</button>
        <view class="close-btn" @tap="handleClose">
          <view class="delete-button"></view>
        </view>
      </view>
      <view class="dialog-arrow"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  direction: {
    type: String,
    default: 'none',
    validator: (value: string) => ['up', 'down', 'none'].includes(value)
  },
  distance: {
    type: Number,
    default: 0
  },
  small: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'enable'])

const visible = ref(false)

// Watch for show prop changes to update visibility
watch(
  () => props.show,
  (val) => {
    visible.value = val
  }
)

// Track previous direction and distance to detect changes
const prevDirection = ref(props.direction)
const prevDistance = ref(props.distance)

// Watch for direction or distance changes to apply transition effects
watch(
  [() => props.direction, () => props.distance],
  ([newDirection, newDistance]) => {
    // Only apply transition effect if the dialog is visible
    if (visible.value) {
      // Store previous values for comparison
      prevDirection.value = newDirection
      prevDistance.value = newDistance
    }
  }
)

// Calculate the dialog style including position and transition
const dialogStyle = computed(() => {
  // Base position depends on whether it's at the tabBar or not
  const basePosition = 150
  let bottomValue: number

  // Adjust based on direction and distance
  if (props.direction === 'up') {
    // Moving up means increasing the bottom value
    bottomValue = basePosition + props.distance
  } else if (props.direction === 'down') {
    // Moving down means decreasing the bottom value
    bottomValue = basePosition - props.distance
  } else {
    // Default: no adjustment
    bottomValue = basePosition
  }

  // Add additional transform based on direction for a more dynamic effect
  let transformX = 'translateX(-50%)'
  let transformY = ''
  let scale = 'scale(1)'
  let opacity = 1
  let width = '690rpx'

  if (props.direction === 'up') {
    // Add a slight upward movement for 'up' direction
    const moveDistance = Math.min(props.distance / 2, 20) // Limit the visual movement
    transformY = ` translateY(-${moveDistance}rpx)`
    // Add a subtle scale effect
    scale = ' scale(1.02)'
  } else if (props.direction === 'down') {
    // Add a slight downward movement for 'down' direction
    const moveDistance = Math.min(props.distance / 2, 20) // Limit the visual movement
    transformY = ` translateY(${moveDistance}rpx)`
    // Add a subtle scale effect
    scale = ' scale(0.98)'
  }

  const transform = `${transformX}${transformY}${scale}`

  if (props.small) {
    width = '281rpx'
  }

  return {
    bottom: `${bottomValue}rpx`,
    transform,
    opacity,
    width
  }
})

const isDenied = ref(false)

// Handle enabling location
const handleEnableLocation = async () => {
  // Check if the user has denied location permission
  if (isDenied.value) {
    uni.showModal({
      title: '提示',
      content: '请在设置中开启定位权限',
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          uni.openSetting({
            success: (settingRes) => {
              // After settings are closed, check if location is now authorized
              uni.getLocation({
                type: 'gcj02',
                success: (locationInfo) => {
                  // Location permission granted
                  userStore.setLocationInfo(locationInfo)
                  userStore.setIsDeniedLocation(false)
                  userStore.setShowLocationDialog(false)
                  emit('enable')
                  emit('close')
                  
                  // Reload current page
                  // const page = getCurrentPages().pop()
                  // if (page) {
                  //   uni.redirectTo({
                  //     url: '/' + page.route,
                  //     success: () => {
                  //       // Force component to remount by toggling show prop
                  //       visible.value = false
                  //       nextTick(() => {
                  //         visible.value = props.show
                  //       })
                  //     }
                  //   })
                  // }
                },
                fail: () => {
                  // Still denied, update state
                  userStore.setIsDeniedLocation(true)
                  // Force component refresh
                  visible.value = false
                  nextTick(() => {
                    visible.value = props.show
                  })
                }
              })
            }
          })
        }
      }
    })
    return
  }

  try {
    // Request location permission
    const locationInfo = await uni.getLocation({ type: 'gcj02' })

    if (locationInfo.latitude && locationInfo.longitude) {
      // Location permission granted
      userStore.setLocationInfo(locationInfo)
      userStore.setShowLocationDialog(false)
      userStore.setIsDeniedLocation(false)
      emit('enable')
      emit('close')
    } else {
      // Location permission denied
      uni.showModal({
        title: '提示',
        content: '请允许使用位置信息，以便为您推荐附近门店',
        showCancel: false
      })
    }
  } catch (error: any) {
    if (error.errMsg === 'getLocation:fail auth deny') {
      // User denied location permission
      isDenied.value = true
      userStore.setIsDeniedLocation(true)
    }
  }
}

// Handle closing the dialog
const handleClose = () => {
  userStore.setShowLocationDialog(false)
  emit('close')
}

onShow(() => {
  nextTick(() => {
    // Check if the user has denied location permission
    isDenied.value = userStore.getIsDeniedLocation()
  })
})
</script>

<style lang="scss" scoped>
.location-permission-dialog-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 998;
  pointer-events: none;
}

.location-permission-dialog {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  width: 690rpx;
  transition: all 0.3s ease;
  pointer-events: auto;

  .dialog-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(74, 74, 74, 0.9);
    border-radius: 16rpx;
    padding: 30rpx 40rpx;
    position: relative;
  }

  .dialog-text {
    color: #ffffff;
    font-size: 32rpx;
    flex: 1;

    .dialog-subtext {
      font-size: 24rpx;
      margin-top: 8rpx;
      color: #ffffff;
      opacity: 0.8;
    }
  }

  .enable-location-btn {
    background-color: #07c160;
    color: #ffffff;
    font-size: 28rpx;
    height: 50rpx;
    line-height: 50rpx;
    border-radius: 35rpx;
    padding: 0 30rpx;
    margin: 0;
    min-width: 180rpx;
    text-align: center;
  }

  .close-btn {
    position: absolute;
    top: -20rpx;
    right: -20rpx;
    width: 50rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dialog-arrow {
    width: 0;
    height: 0;
    border-left: 20rpx solid transparent;
    border-right: 20rpx solid transparent;
    border-top: 20rpx solid rgba(74, 74, 74, 0.9);
    margin: 0 auto;
  }
}

.delete-button {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #e6e6e6;
  position: relative;
}

.delete-button::before,
.delete-button::after {
  content: "";
  position: absolute;
  width: 40%;
  height: 4rpx;
  background-color: black;
  top: 50%;
  left: 50%;
  transform-origin: center;
}

.delete-button::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.delete-button::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
</style>
