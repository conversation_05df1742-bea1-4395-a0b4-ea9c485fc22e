<template>
  <view>
    <view class="time-text" @tap="showPopup">
      <image
        class="icon-time"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/time-icon.png"
      />{{ curTimeRange || placeholder }}</view
    >
    <uni-popup ref="timePopup" type="bottom" :is-mask-click="false">
      <view class="time-rang">
        <view class="btn-wrap">
          <view class="cancel" @tap="cancel">取消</view>
          <view class="confirm" @tap="closeHandler">确定</view>
        </view>
        <picker-view
          class="time-rang-picker"
          :value="timerang"
          indicator-style="height:30px;"
          @change="changeTimeRange"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in hoursList"
              :key="index"
              class="time-rang-hours picker-column-block"
              >{{ item }}</view
            >
          </picker-view-column>
          <picker-view-column>
            <view
              v-for="(item, index) in minutes"
              :key="index"
              class="time-rang-minutes picker-column-block"
              >{{ item }}</view
            >
          </picker-view-column>
          <picker-view-column>
            <view class="picker-column-block">-</view>
          </picker-view-column>
          <picker-view-column>
            <view
              v-for="(item, index) in hoursList"
              :key="index"
              class="time-rang-hours picker-column-block"
              >{{ item }}</view
            >
          </picker-view-column>
          <picker-view-column>
            <view
              v-for="(item, index) in minutes"
              :key="index"
              class="time-rang-minutes picker-column-block"
              >{{ item }}</view
            >
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
})
const emits = defineEmits(['close', 'change', 'update:modelValue'])
const curTimeRange = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  },
})
const timerang = ref([9, 0, 0, 18, 0]) //默认结束开始时间
const minutes = Array.from(Array(60), (v, k) => (k + '').padStart(2, '0'))
const hoursList = Array.from(Array(24), (v, k) => (k + '').padStart(2, '0'))
watch(
  () => props.modelValue,
  (val, oldVal) => {
    if (val && val.length === 2) {
      timerang.value = handleTime(val)
    }
  },
  { deep: true, immediate: true }
)

function changeTimeRange(e) {
  timerang.value = e.detail.value
}

function handleTime(val) {
  return []
    .concat(val[0].split(':').flatMap((ele) => Number(ele)))
    .concat([])
    .concat(val[1].split(':').flatMap((ele) => Number(ele)))
}

const timePopup = ref()
async function showPopup() {
  if (timePopup.value) {
    timePopup.value.open()
  } else {
    await nextTick()
    timePopup.value.open()
  }
}

function closeHandler() {
  const time1 = `${toString(0).padStart(2, '0') + ':' + toString(1).padStart(2, '0')}`
  const time2 = `${toString(3).padStart(2, '0') + ':' + toString(4).padStart(2, '0')}`
  if (time2 < time1) {
    uni.showToast({
      title: '结束时间不能小于开始时间！',
      icon: 'none',
      duration: 2500,
    })
    return
  }
  const timerang = [time1, time2]
  curTimeRange.value = timerang[0] + ' - ' + timerang[1]
  emits('change', timerang)
  closePopuup()
}
async function closePopuup() {
  if (timePopup.value) {
    timePopup.value.close()
  } else {
    await nextTick()
    timePopup.value.close()
  }
}
function cancel(info) {
  closePopuup()
}

function toString(i) {
  return timerang.value[i].toString()
}
</script>
<style lang="scss" scoped>
.time-text {
  padding-right: 20rpx;
  min-width: 95rpx;
  text-align: right;
}
.time-rang {
  width: 100%;
  margin: 0 auto;
  background-color: #ffffff;
  color: #000;
  height: 450rpx;

  .time-rang-picker {
    width: 100%;
    height: 300rpx;
  }
  .time-rang-hours {
    font-size: 30rpx;
    font-weight: bold;
    color: #000;
  }
  .time-rang-minutes {
    font-size: 30rpx;
    font-weight: bold;
    color: #000;
  }
  .picker-column-block {
    line-height: 30px;
    text-align: center;
  }
}
.btn-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 30rpx;
  padding: 30rpx;
  font-size: 36rpx;
  .confirm {
    color: green;
  }
}
</style>
