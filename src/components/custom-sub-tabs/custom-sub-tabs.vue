<template>
  <view :class="['tab', { 'tab-fixed': fixed }, { 'tab-tag': type === 'tag' }]">
    <scroll-view class="theme-bg" style="background-color: transparent !important;" scroll-x="true" :scroll-into-view="scrollId" scroll-with-animation>
      <view class="tab-bar" :class="{ 'tab-bar-center': center }">
        <view
          v-for="(tab, i) in state.tabs"
          :id="`tab_${i}`"
          :key="i"
          :class="['tab-bar-item', { active: tab.isActive }]"
          @tap="selectTab(i)"
        >
          <text class="txt">{{ tab.label }}</text>
          <text v-if="tab.number" class="txt" style="margin-left: 10rpx">({{ tab.number }})</text>
          <text v-if="tab.isActive" class="active-line"></text>
        </view>
        <view class="item-right">
          <slot name="right"></slot>
        </view>
      </view>
    </scroll-view>
    <view class="tab-cont">
      <slot />
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    // 默认选中项
    type: [Number, String],
    default: 0,
  },
  center: {
    // tabbar是否居中
    type: Boolean,
    default: false,
  },
  fixed: {
    // 该组件是否固定位置
    type: Boolean,
    default: false,
  },
  type: {
    // 该组件展示的样式类型 可选值 tag
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:modelValue', 'change', 'clicked'])

const scrollId = ref('tab_0')
const state = reactive({
  activeTabHash: '',
  lastActiveTabHash: '',
  tabs: [],
})

provide('tabsProvider', state)

provide('addTab', (tab) => {
  state.tabs.push(tab)
  if (!state.lastActiveTabHash) {
    selectTab(props.modelValue || 0)
  }
})

provide('updateTab', (computedId, data) => {
  const findTabIndex = state.tabs.findIndex((tab) => tab.computedId === computedId)
  if (findTabIndex === -1) {
    return
  }
  data.isActive = state.tabs[findTabIndex].isActive
  state.tabs[findTabIndex] = data
})

provide('deleteTab', (computedId) => {
  const findTabIndex = state.tabs.findIndex((tab) => tab.computedId === computedId)
  state.tabs.splice(findTabIndex, 1)
})

function selectTab(selectedIndex) {
  const selectedTabHash = state.tabs[selectedIndex]?.hash
  if (!selectedTabHash) {
    return
  }
  const selectedTab = findTab(selectedTabHash)

  if (!selectedTab) {
    return
  }
  if (state.lastActiveTabHash === selectedTab.hash) {
    emits('clicked', { tab: selectedTab })
    return
  }
  scrollId.value = `tab_${selectedIndex}`
  state.tabs.forEach((tab) => {
    tab.isActive = tab.hash === selectedTab.hash
  })

  emits('change', { value: selectedIndex, ...selectedTab })
  emits('update:modelValue', selectedIndex)
  state.lastActiveTabHash = state.activeTabHash = selectedTab.hash
}

const findTab = (tabHash) => {
  return state.tabs.find((tab) => tab.hash === tabHash)
}
watch(
  () => props.modelValue,
  (val) => {
    selectTab(val)
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.tab {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  height: 100%;

  .tab-bar {
    position: relative;
    height: 72rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    .item-right {
      position: absolute;
      right: 20rpx;
    }
    .tab-bar-item {
      margin-right: 20rpx;
      transition: all 0.3s ease;
      padding: 0 10rpx;
      min-width: 128rpx;
      height: 50rpx;
      background: #E7E7E7;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: bold;
      font-size: 24rpx;
      color: #000000;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      justify-content: center;

      &:last-child {
        margin-right: 0;
      }
    }
    &.tab-bar-center {
      justify-content: center;
      .tab-bar-item {
        flex: 1;
      }
    }
    &-item {
      height: 48rpx;
      line-height: 48rpx;
      font-size: 28rpx;

      &.active {
        background: var(--THEME-COLOR);
        color: black;
        font-weight: bold;
      }

      .active-line {
        display: none;
      }
    }
  }

  &-fixed {
    height: 100vh;

    .tab-bar {
      position: sticky;
      top: 0px;
      z-index: 2022;
    }
  }
  &-tag {
    .tab-bar {
      position: sticky;
      top: 0px;
      z-index: 2022;
    }
    .active-line {
      display: none;
    }
    .tab-bar-item {
      padding: 0 4rpx !important;
      margin-right: 8rpx;
    }
    .txt {
      height: 60rpx;
      line-height: 60rpx;
      border-radius: 30rpx;
      padding: 0 28rpx;
      font-size: 28rpx;
      background: #f5f7f9;
      color: #666;
      display: inline-block;
    }
    .tab-bar-item.active {
      position: relative;
      font-weight: normal;
    }
    .active .txt {
      font-weight: bold;
      background: #7ED321;
      color: white;
    }
  }
  .tab-cont {
    display: flex;
    flex: 1;
    flex-direction: row;
  }
}
</style>
