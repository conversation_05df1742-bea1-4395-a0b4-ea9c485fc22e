<template>
  <view>
    <uni-popup ref="msgPopup" @change="handleStatusChange">
      <view v-if="type === 1" class="invitation-container">
        <view class="invitation-box">
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/face.png"></image>
          <view class="title">{{ title }}</view>
          <view class="desc">邀请有礼！叫上朋友一起来锻炼吧！</view>
        </view>
        <view class="invitation-btn">
          <navigator url="/pages/invitation/detail" class="normal-btn normal-btn-min outer-org">邀请有礼活动</navigator>
          <button open-type="share" class="normal-btn normal-btn-min">邀请好友</button>
        </view>
      </view>
      <view v-if="type === 2" class="invitation-container">
        <view class="invitation-box">
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/face.png"></image>
          <view class="title">您已获得邀请奖励</view>
          <view class="desc">【{{ list && list[active] && list[active].gift ? list[active].gift : '' }}】</view>
        </view>
        <view class="invitation-btn">
          <button class="normal-btn normal-btn-min" @tap="handleCheck">去查看</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const props = defineProps({
  type: {
    type: Number,
    default: 1,
  },
  modelValue: {
    type: String,
  },
  show: {
    type: Boolean,
  },
  title: {
    type: String,
    default: '活动',
  },
})
const emit = defineEmits(['update:modelValue'])

const invitationActivityId = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})
const userStore = useUserStore()
const msgPopup = ref()
const active = ref(0)
const list = ref([])
watch(
  () => props.show,
  (val) => {
    if (val && props.type === 1) {
      showPopup()
    }
    if (!val) {
      closePopup()
    }
    if (!val && props.type === 2) {
      list.value = []
      active.value = 0
    }
  }
)
// watchEffect(() => {
//   handleInit()
// })
onShow(() => {
  handleInit()
})
function handleInit() {
  const busId = userStore.userInfoBusId
  const userId = userStore.userInfoUserId
  if (busId && userId) {
    if (props.type === 1) {
      getInvitationActivity()
    } else if (props.type === 2) {
      getInvitationAward()
    }
  }
}
function handleStatusChange(info) {
  if (!info.show && props.type === 2) {
    list.value = []
    active.value = 0
  }
}
function handleCheck() {
  const item = list.value[active.value]
  if (item) {
    // 奖励类型 1会员卡 2折扣券
    const path = item.type == 1 ? '/pages/my/card' : '/pages/my/coupon'
    const query = 'scrollToId=' + item.gift_id
    uni.navigateTo({
      url: `${path}?${query}`,
    })
    if (++active.value >= list.value.length) {
      closePopup()
    }
  }
}
// 获取是否有邀请奖励
function getInvitationAward() {
  http
    .get('ActivityGuests/checkGiftRead', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      if (Array.isArray(res.data.list) && res.data.list.length) {
        list.value = [...list.value, ...res.data.list]
        showPopup()
      }
    })
}
// 获取是否有邀请活动
function getInvitationActivity() {
  http
    .get('ActivityGuests/getDoingActivity', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      invitationActivityId.value = res.data.id || ''
    })
}
async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}
async function closePopup() {
  if (msgPopup.value) {
    msgPopup.value.close()
  } else {
    await nextTick()
    msgPopup.value.close()
  }
}
</script>

<style lang="scss" scoped>
.invitation-container {
  border-radius: 20rpx;
  background: #fff;
  .invitation-box {
    padding-bottom: 35rpx;
    width: 540rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #1b1b1b;

    image {
      margin-top: 43rpx;
      width: 134rpx;
      height: 134rpx;
    }

    .title {
      margin-top: 18rpx;
      font-size: 36rpx;
      font-weight: bold;
    }

    .desc {
      font-size: 30rpx;
      line-height: 38rpx;
      margin: 36rpx 58rpx 0 58rpx;
    }
  }
  .invitation-btn {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    padding-bottom: 30rpx;
    .normal-btn-min {
      width: 230rpx;
    }
  }
}
</style>
