<template>
  <cover-view v-if="isShowTabBar" class="tab-bar" :style="{ backgroundColor: theme == 2 ? '#000000' : '#ffffff' }">
    <cover-view class="tab-bar-border"></cover-view>
    <template v-for="(item, index) in themeStore.theme5" :key="index">
      <cover-view v-if="item.display" class="tab-bar-item" @tap="switchToTab(item.temp_type)">
        <cover-image
          :src="selected === index ? findIconByTemp(item.temp_type, true) : findIconByTemp(item.temp_type)"
        />
        <cover-view
          :style="{
            color: selected === index ? (theme == 2 ? '#ffffff' : '#0E0E0E') : theme == 2 ? '#7d7d7d' : '#0E0E0E',
          }"
          >{{ item.name }}</cover-view
        >
      </cover-view>
    </template>
  </cover-view>
</template>
<script setup lang="ts">
import { useThemeStore } from '@/store/theme'
import { useUserStore } from '@/store/user'

const themeStore = useThemeStore()
const selected = ref(0)
const tabKeyArr = ['home', 'reserve', 'mall', 'my']
const list = ref([
  {
    pagePath: '/pages/index/index',
    text: '首页',
    temp_type: '1',
    iconPath: '/static/img/home.png',
    selectedIconPath: '/static/img/home-selected-dark.png',
  },
  {
    pagePath: '/pages/class/class',
    text: '约课',
    temp_type: '2',
    display: 1,
    iconPath: '/static/img/reserve.png',
    selectedIconPath: '/static/img/reserve-selected-dark.png',
  },
  {
    pagePath: '/pages/mall/list',
    text: '商城',
    temp_type: '3',
    display: 1,
    iconPath: '/static/img/mall.png',
    selectedIconPath: '/static/img/mall-selected-dark.png',
  },
  {
    pagePath: '/pages/my/index',
    text: '我的',
    temp_type: '4',
    display: 1,
    iconPath: '/static/img/my.png',
    selectedIconPath: '/static/img/my-selected-dark.png',
  },
])

const color = computed(() => {
  return themeStore.theme1.fashion_color || 'a1ea2b'
})
const theme = ref(themeStore.theme1.background_color || 1)
//运营模式为综合体育场馆版本时，只有商家下展示tabBar
const isShowTabBar = computed(() => {
  return (themeStore.operationMode === 1 && themeStore.isShowMerchantMode) || themeStore.operationMode !== 1
})
function findItemByTemp(temp: string) {
  return list.value.find((item) => item.temp_type === temp) || list.value[0]
}
function findIconByTemp(temp: string, isSelect = false) {
  const item = findItemByTemp(temp)
  return isSelect
    ? `https://imagecdn.rocketbird.cn/minprogram/uni-member/theme/tabicons/${color.value}/${
        tabKeyArr[+temp - 1]
      }-selected${theme.value == 2 ? '' : '-dark'}.png`
    : item?.iconPath
}

function setTabIndex() {
  const pages = getCurrentPages()
  const page = pages[pages.length - 1]
  const path = '/' + page.route
  list.value.forEach((item, index) => {
    if (item.pagePath === path) {
      selected.value = index
    }
  })
}
onShow(() => {
  setTabIndex()
})
function switchToTab(temp: string) {
  const url = findItemByTemp(temp).pagePath
  if (url === '/pages/class/class') {
    uni.removeStorageSync('switchClassPageQuery')
  }
  uni.switchTab({ url: url })
}
</script>
<style lang="scss" scoped>
.tab-bar {
  position: fixed;
  z-index: 99;
  bottom: 0;
  left: 0;
  right: 0;
  height: 48px;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tab-bar-item cover-image {
  width: 22px;
  height: 22px;
  margin-bottom: 2px;
}

.tab-bar-item cover-view {
  font-size: 10px;
}
</style>
