<template>
  <uni-countdown
    class="line-count"
    :font-size="size"
    :color="color"
    :splitor-color="color"
    :hour="curHour"
    :minute="curMinute"
    :second="curSecond"
    :day="curDay"
    :show-day="showDay"
    @timeup="timeup"
  ></uni-countdown>
</template>

<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#ff7427',
  },
  size: {
    type: Number,
    default: 14,
  },
  time: {
    type: String,
    default: '',
  },
  day: {
    type: Number,
    default: 0,
  },
  hour: {
    type: Number,
    default: 0,
  },
  minute: {
    type: Number,
    default: 0,
  },
  second: {
    type: Number,
    default: 0,
  },
})
const timeArr = ref([])
const showDay = ref(false)
const curDay = ref(0)
const curHour = ref(0)
const curMinute = ref(0)
const curSecond = ref(0)

watchEffect(() => {
  setTime()
})
const emits = defineEmits(['timeup'])
function timeup() {
  emits('timeup')
}
function setTime() {
  if (props.time) {
    timeArr.value = props.time.split(':').reverse()
    if (timeArr.value.length == 4) {
      showDay.value = true
    }
    curSecond.value = timeArr.value[0] ? +timeArr.value[0] : 0
    curMinute.value = timeArr.value[1] ? +timeArr.value[1] : 0
    curHour.value = timeArr.value[2] ? +timeArr.value[2] : 0
    curDay.value = timeArr.value[3] ? +timeArr.value[3] : 0
  } else {
    curSecond.value = props.second
    curMinute.value = props.minute
    curHour.value = props.hour
    curDay.value = props.day
  }
}
</script>

<style lang="scss" scoped>
.line-count {
  display: inline-block;
  vertical-align: middle;
  color: $theme-text-color-other;
}
</style>
