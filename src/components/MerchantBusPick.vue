<template>
  <picker
    :value="getIndexByBusId(selectedBusId)"
    :disabled="disabled"
    range-key="bus_name"
    :range="busList"
    @change="handleChange"
  >
    <view class="merchant-con" :style="{ height: height + 'rpx', lineHeight: height + 'rpx' }">
      <ThemeIcon v-if="isShowIcon" class="icon-mr" type="t-icon-mendianqiehuan-01" />
      <view
        class="value"
        :class="!isShowSelecteIcon || disabled ? '' : selecteIconColor === 'origin' ? 'dark-sel org-sel' : 'dark-sel'"
        >{{ showBusName }}</view
      >
    </view>
  </picker>
</template>

<script setup lang="ts">
/* 用页面下进行商家门店切换 会与useMerchantStore下面的门店信息关联 方便切换门店后在页面各级组件中调用, 当选中全部门店选项时 在各组件中接口bus_id为空 后端会获取商家下所有门店数据 */
import { useMerchant } from '@/store/merchant'
import { useUserStore } from '@/store/user'
import { checkBusIdAndSwitch } from '@/utils/urlMap'

interface Props {
  isFilterUserIdBus?: boolean // 是否是必须含有用户id的场馆
  isShowIcon?: boolean
  isSelectDefault?: boolean // 是否默认选中本地场馆
  height?: number
  modelValue?: string
  disabled?: boolean
  hasAllBusOption?: boolean // 是否加入全部门店选项
  isShowSelecteIcon?: boolean
  selecteIconColor?: 'origin' | 'dark' // 下拉右侧三角形icon的颜色
}
const props = withDefaults(defineProps<Props>(), {
  isFilterUserIdBus: false,
  isSelectDefault: false,
  modelValue: '',
  disabled: false,
  isShowIcon: true,
  isShowSelecteIcon: true,
  height: 90,
  hasAllBusOption: true,
  selecteIconColor: 'dark',
})
const requestList = ref<any[]>([])
const busList = ref<any[]>([])
const userStore = useUserStore()
const useMerchantStore = useMerchant()
const showBusName = ref(props.hasAllBusOption ? '全部门店' : '请选择')
const busIndex = ref(0)
const emits = defineEmits(['update:modelValue', 'change'])
const nullInfo = computed(() => {
  return {
    bus_name: props.hasAllBusOption ? '全部门店' : '请选择',
    bus_id: '',
    user_id: useMerchantStore.userInfoUserId || userStore.userInfoUserId,
  }
})
const selectedBusId = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  },
})
watch(selectedBusId, (val, oldVaa) => {
  const info = setInfo(val)
  emits('change', info)
})
function pickInit() {
  requestList.value = props.isFilterUserIdBus ? useMerchantStore.getHasUserIdBusList : useMerchantStore.busList
  const allBusOption = props.hasAllBusOption ? [nullInfo.value] : []
  busList.value = allBusOption.concat(requestList.value)
  if (props.isSelectDefault) {
    // 选中默认门店
    selectDefault(busList.value)
  } else {
    // 初始化 防止被其它页面的同组件影响了值 同时触发一次change事件
    changeIndex(0)
    emits('change', setInfo(busList.value[0]))
  }
}

if (!useMerchantStore.busList.length) {
  useMerchantStore.requestBusList().then(() => {
    pickInit()
  })
} else {
  pickInit()
}

function findInfoByBusId(busId: string) {
  return busList.value.find((item) => item.bus_id === busId)
}
function setInfo(value) {
  const info = findInfoByBusId(value)
  unpdateBusName(info || nullInfo.value)
  return info || nullInfo.value
}
function selectDefault(list: any[]) {
  const index = getIndexByBusId(useMerchantStore.userInfoBusId || userStore.userInfoBusId, list)
  if (index >= 0) {
    changeIndex(index)
  }
}

function getIndexByBusId(busId: string, list?: any[]) {
  const index = (list || busList.value).findIndex((item) => item.bus_id === busId)
  return index >= 0 ? index : 0
}
function handleChange(e) {
  const index = e.detail.value
  changeIndex(index)
}
async function changeIndex(index: number) {
  busIndex.value = index
  const busInfo = busList.value[index || 0]
  useMerchantStore.setMerchantUserInfo(busInfo)
  // 场馆id和user_id都不为空时 切换场馆信息和用户信息 方便后续操作
  if (busInfo.bus_id && busInfo.user_id) {
    await checkBusIdAndSwitch(busInfo.bus_id)
  }
  selectedBusId.value = busInfo.bus_id
}
function unpdateBusName(busInfo) {
  const busNameValue = busInfo.bus_name
  const maxLength = 7
  showBusName.value = busNameValue.length > maxLength ? '...' + busNameValue.slice(-maxLength) : busNameValue
}
</script>

<style lang="scss" scoped>
.merchant-con {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  height: 90rpx;
  line-height: 90rpx;
}
.dark-sel {
  position: relative;
  padding-right: 26rpx;
  &::after {
    position: absolute;
    right: 4rpx;
    top: 50%;
    margin-top: -5rpx;
    border: 8rpx solid transparent;
    border-top: 8rpx solid #1b1b1b;
    width: 0;
    height: 0;
    content: ' ';
  }
}
.org-sel {
  &::after {
    border: 8rpx solid transparent;
    border-top: 8rpx solid $theme-text-color-other;
  }
}
</style>
