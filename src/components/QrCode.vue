<template>
  <view class="qr-wrap theme-bg">
    <view v-if="showAvatar" class="user-avatar">
      <image :src="avatar" />
      {{ nickName }}
    </view>
    <view class="qr-code">
      <image :src="qrCode" />
    </view>
    <text v-if="tips" class="tips">{{ tips }}</text>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const avatar = ref('')
const nickName = ref('')
const qrCode = ref('')
const qrRefreshTimer = ref()
const props = defineProps({
  showAvatar: {
    type: Boolean,
    default: true,
  },
  tips: {
    type: String,
    default: '',
  },
})
onMounted(() => {
  getQrcode(0)
})
onShow(() => {
  getQrcode(0)
})
onUnmounted(() => {
  clearTimeout(qrRefreshTimer.value)
})

function refreshQrcode() {
  qrRefreshTimer.value = setTimeout(() => {
    getQrcode(1)
  }, 5 * 1000)
}

function getQrcode(type: number) {
  if (!userStore.userInfoUserId) {
    return
  }
  http
    .get('Sign/getSelfQr', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      loading: false,
      type,
    })
    .then((res) => {
      const userInfo = uni.getStorageSync('userInfo')
      qrCode.value = res.data
      avatar.value = userInfo.avatar
      nickName.value = userInfo.nickname
      refreshQrcode()
    })
}
</script>

<style lang="scss" scoped>
.tips {
  font-size: 26rpx;
  text-align: center;
  display: block;
}
.qr-wrap {
  padding: 30rpx;
  margin: 30rpx;
  box-sizing: border-box;
  min-width: 600rpx;
  border-radius: 8rpx;
  .user-avatar {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    image {
      height: 100rpx;
      width: 100rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }
  }
  .qr-code {
    margin: 10rpx 0;
    width: 100%;
    padding-bottom: 100%;
    position: relative;
    image {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
