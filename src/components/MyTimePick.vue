<template>
  <picker mode="time" :value="time" :start="start" :end="end" @change="bindDateChange">
    <view class="date-con">
      <image
        class="icon-time"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/time-icon.png"
      />
      <text class="date-text">{{ time || placeholder }}</text>
    </view>
  </picker>
</template>

<script setup lang="ts">
const props = defineProps({
  start: {
    type: String,
    default: '',
  },
  end: {
    type: String,
    default: '',
  },
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
})
const emits = defineEmits(['update:modelValue', 'change'])
const time = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
    emits('change', time.value)
  },
})
function bindDateChange(e) {
  time.value = e.detail.value
}
</script>

<style lang="scss" scoped>
.date-con {
  display: inline-flex;
  align-items: center;
  font-size: 26rpx;
}
.date-text {
  flex: 1;
  text-align: right;
}
</style>
