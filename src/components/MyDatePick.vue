<template>
  <picker mode="date" :value="day" :start="start" :end="end" @change="bindDateChange">
    <view class="date-con">
      <image class="icon-time" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/calendar-icon.png" />
      <text class="date-text">{{ day || placeholder }}</text>
    </view>
  </picker>
</template>

<script setup lang="ts">
const props = defineProps({
  start: {
    type: String,
    default: '',
  },
  end: {
    type: String,
    default: '',
  },
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
})
const emits = defineEmits(['update:modelValue', 'change'])
const day = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
    emits('change', value)
  },
})

function bindDateChange(e) {
  day.value = e.detail.value
}
</script>

<style lang="scss" scoped>
.date-con {
  display: inline-flex;
  align-items: center;
  font-size: 26rpx;
}
.date-text {
  flex: 1;
  text-align: right;
}
</style>
