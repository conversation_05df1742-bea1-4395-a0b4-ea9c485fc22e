<template>
  <view v-if="lazyLoad?hasActived:true" v-show="isActive" :id="computedId" ref="tab" class="tab-pane">
    <slot />
  </view>
</template>
<script lang="ts">
export default {
  name: 'CustomTabPane',
  options: {
    virtualHost: true, // 将自定义节点设置成虚拟的，更加接近Vue组件的表现。
  },
}
</script>
<script setup lang="ts">
  const props = defineProps({
    lazyLoad: {
      // 是否懒加载
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: null,
    },
    number: {
      type: Number,
      default: 0,
    },
    label: {
      type: String,
      required: true,
    },
  })

const emits = defineEmits(['actived'])
const isActive = ref(false)
const hasActived = ref(false)

const tabsProvider = inject('tabsProvider')
const addTab = inject('addTab')
const updateTab = inject('updateTab')
const deleteTab = inject('deleteTab')

const computedId = props.id ? props.id : props.label.toLowerCase().replace(/ /g, '-')
const hash = '#' + computedId

watch(
  () => tabsProvider.activeTabHash,
  () => {
    isActive.value = hash === tabsProvider.activeTabHash
    if(isActive.value) {
      hasActived.value = true
      emits('actived', {
        label: props.label,
        number: props.number,
        hash: hash,
        index: tabsProvider.tabs.length,
        computedId: computedId,
      })
    }
  }
)

watch(
  () => Object.assign({}, props),
  () => {
    updateTab(computedId, {
      label: props.label,
      number: props.number,
      hash: hash,
      index: tabsProvider.tabs.length,
      computedId: computedId,
    })
  }
)

onBeforeMount(() => {
  addTab({
    label: props.label,
    number: props.number,
    hash: hash,
    index: tabsProvider.tabs.length,
    computedId: computedId,
  })
})

onBeforeUnmount(() => {
  deleteTab(computedId)
})
</script>
<style lang="scss" scoped>
.tab-pane {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
}
</style>
