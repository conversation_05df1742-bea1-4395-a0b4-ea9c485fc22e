<template>
  <view
    v-if="hasRedBag"
    :class="{
      'red-bag-box': isBox,
      'to-button': isToButton && isBox,
      'red-bag-button': !isBox,
      'to-box': !isToButton && !isBox,
    }"
    @click="goToRedBag"
    :style="props.styles"
  >
    <view v-if="isBox" class="gg-close-o" @click.stop="handleToggle"></view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()
const userStore = useUserStore()
const isShowMerchantMode = computed(() => themeStore.isShowMerchantMode)

const props = defineProps({
  styles: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['hasRedBag'])

const isBox = ref(true)
const isToButton = ref(false)
const handleToggle = () => {
  isToButton.value = !isToButton.value
  setTimeout(() => {
    isBox.value = !isBox.value

    emit('hasRedBag', {
      hasRedBag: isBox.value,
      small: !isBox.value,
    })
  }, 400)
}

const goToRedBag = () => {
  // 门店跳转到红包详情, 商家跳转到红包列表
  if (isShowMerchantMode.value) {
    uni.navigateTo({
      url: '/packageMy/redBag/list',
    })
  } else {
    uni.navigateTo({
      url: '/packageMy/redBag/open?bonusId=' + bonusId.value,
    })
  }
}

const hasRedBag = ref(false)
const bonusId = ref('')
const getRedBag = () => {
  let bus_id = ''
  if (!isShowMerchantMode.value) {
    bus_id = userStore.userInfoBusId
  }
  http.get('/Bonus/checkNewBonus', { bus_id }).then((res) => {
    if (res.errorcode === 0) {
      bonusId.value = res.data.bonus_id
      hasRedBag.value = !!res.data.bonus_id

      emit('hasRedBag', {
        hasRedBag: hasRedBag.value,
        small: false,
      })
    }
  })
}

onReady(() => {
  getRedBag()
})
</script>

<style lang="scss">
$box-height: 180rpx;
$box-width: 710rpx;
$button-height: 164rpx;
$button-width: 188rpx;

@mixin red-bag {
  position: fixed;
  right: 20rpx;
  bottom: 160rpx;
  color: white;
  border-radius: 20rpx;
  /* box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.5); */
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  z-index: 996;
}

.red-bag-box {
  font-size: 36rpx;
  font-weight: 500;
  width: $box-width;
  height: $box-height;
  @include red-bag();
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/red-bag-banner.png') center center / 100% 100%
    no-repeat;
}

.red-bag-button {
  font-size: 24rpx;
  width: $button-width;
  height: $button-height;
  @include red-bag();
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/red-bag-button.png') center center / 100% 100%
    no-repeat;
}

.red-bag-box.to-button {
  animation: toggleAnimation 0.4s ease-in normal forwards;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/red-bag-button.png') center center / 100% 100%
    no-repeat;
}

.red-bag-button.to-box {
  animation: toggleAnimation 0.4s ease-out reverse forwards;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/red-bag-banner.png') center center / 100% 100%
    no-repeat;
}

@keyframes toggleAnimation {
  0% {
    width: $box-width;
    height: $box-height;
    font-size: 36rpx;
    transform: scale(1);
  }
  50% {
    transform: scale(0.6);
  }
  100% {
    width: $button-width;
    height: $button-height;
    font-size: 24rpx;
    transform: scale(1);
  }
}

.gg-close-o {
  box-sizing: border-box;
  position: absolute;
  top: 14rpx;
  right: 14rpx;
  display: block;
  width: 32rpx;
  height: 32rpx;
  background: #b4b4b4ff;
  border: 1rpx solid #f3fcffff;
  border-radius: 50%;
}
.gg-close-o::after,
.gg-close-o::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 16rpx;
  height: 4rpx;
  background: #f3fcffff;
  transform: rotate(45deg);
  border-radius: 5rpx;
  top: 13rpx;
  left: 7rpx;
}
.gg-close-o::after {
  transform: rotate(-45deg);
}
</style>
