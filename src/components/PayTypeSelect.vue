<template>
  <view v-if="newPayTypeList.length" class="form-items theme-bg payment">
    <view class="payment-tit">支付方式</view>
    <view>
      <view>
        <view
          v-for="(item, index) in newPayTypeList"
          :key="item.pay_type"
          class="item"
          @click="handlePayMethodChange($event, String(index))"
        >
          <view class="label">
            <image :src="item.icon" style="width: 32rpx; height: 32rpx; margin-right: 10rpx" />
            <text>{{ item.pay_name }}</text>
          </view>
          <uni-icons
            v-show="payTypeIndex === String(index)"
            type="checkbox-filled"
            size="24"
            color="var(--THEME-COLOR)"
          />
          <uni-icons v-show="payTypeIndex !== String(index)" type="circle" size="24" color="#d1d1d1" />
        </view>
      </view>

      <view v-if="newPayTypeList[payTypeIndex].pay_type === 8" class="item">
        <picker
          v-if="cardList.length"
          mode="selector"
          class="rig-sel"
          :value="cardIndex"
          :range="cardList"
          range-key="name"
          style="width: 100%"
          @change="handleCardChange"
        >
          <view class="value-card">
            <text class="card-name">{{ cardList[cardIndex] && cardList[cardIndex].name }}</text>
            <text class="card-balance">{{ valueCardText }}</text>
            <text v-if="cardList[cardIndex]?.self_recharge == '1'" class="recharge-link" @click.stop="handleToRecharge"
              >去充值</text
            >
          </view>
        </picker>
        <view v-else class="value-card">
          <text class="card-name">暂无可用储值卡</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="PayTypeSelect">
import { PropType } from 'vue'

interface PayTypeItem {
  pay_name: string
  pay_type: number
  icon?: string
}

interface CardItem {
  name: string
  card_user_id: string | number
  [propName: string]: any
}

const props = defineProps({
  payTypeList: {
    // 支付方式列表
    type: Array as PropType<PayTypeItem[]>,
    default: () => [],
  },
  payTypeIndex: {
    type: String, // radio value 类型目前需要为string
    default: '0',
  },
  cardList: {
    // 储值卡列表
    type: Array as PropType<CardItem[]>,
    default: () => [],
  },
  cardIndex: {
    type: Number,
    default: 0,
  },
  valueCardText: {
    // 储值余额提示文本
    type: String,
    default: '',
  },
})
const emits = defineEmits(['change-type', 'change-card', 'refresh-page'])

// 支付方式列表 增加了icon字段
const newPayTypeList = computed(() => {
  const stadiumCdn = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/'
  const suffix = {
    1: 'wx.png', // 微信图标
    8: 'vip.png', // 储值卡
  }
  return props.payTypeList.map((v) => {
    const icon = v.icon || `${stadiumCdn}${suffix[v.pay_type]}`
    return {
      ...v,
      icon,
    }
  })
})

// 切换支付方式
const handlePayMethodChange = (e, index) => {
  if (index === props.payTypeIndex) {
    return
  }

  const payType = props.payTypeList[index].pay_type
  if (payType === 8 && !props.cardList.length) {
    uni.showToast({
      title: '暂无可用储值卡',
      icon: 'none',
    })
    return
  }

  const data = {
    index,
    payType,
    choseCard: props.cardList[props.cardIndex],
  }
  emits('change-type', data)
}

// 切换储值卡
const handleCardChange = (e) => {
  if (e.detail.value === props.cardIndex) {
    return
  }

  const data = {
    index: +e.detail.value,
    choseCard: props.cardList[e.detail.value],
  }
  emits('change-card', data)
}

// 跳转到对应储值卡充值页
function handleToRecharge() {
  const card_user_id = props.cardList[props.cardIndex].card_user_id
  uni.navigateTo({
    url: '/packageMy/recharge/detail?card_user_id=' + card_user_id,
    events: {
      'refresh-page': () => {
        emits('refresh-page')
      },
    },
  })
}
</script>

<style lang="scss" scoped>
.payment-tit {
  font-size: 26rpx;
  font-weight: bold;
  line-height: 80rpx;
}

.payment {
  padding-bottom: 20px;
  .item {
    .label {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 26rpx;
      line-height: 30rpx;
    }

    // .value {
    //   font-size: 26rpx;
    //   color: #313131;
    //   line-height: 30rpx;
    //   margin: 0 20rpx;
    // }

    .value-card {
      font-weight: bold;
      font-size: 24rpx;
      // color: #888888;
      // line-height: 90rpx;
      min-height: 90rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 0 30rpx 0 40rpx;
      .card-balance {
        margin-left: 12rpx;
        font-weight: 400;
        color: $theme-text-color-other;
        white-space: nowrap;
      }
      .recharge-link {
        padding: 20rpx 12rpx;
        text-decoration: underline;
        color: $theme-text-color-other;
        white-space: nowrap;
      }
    }
  }
}
</style>
