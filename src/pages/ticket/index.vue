<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
// @ts-ignore
import { useLogin } from '@/hooks/useLogin.ts'

const userStore = useUserStore()
const { checkLogin } = useLogin()

// tabs
const tabIndex = ref(0)
const handleTabClick = (tab) => {
  tabIndex.value = tab
  getList(1, 10)
}

// print information
const printer = ref('')
const handlePrint = () => {
  checkPrinter()
  printShow.value = true
}
const setPrinterStatus = (status) => {
  // 0 表示离线 1 表示在线正常 2 表示在线异常
  if (status == 0) {
    printSuccess.value = false
    printMessage.value = '设备不在线'
  } else if (status == 1) {
    printSuccess.value = true
    printMessage.value = ''
    uni.setKeepScreenOn({ keepScreenOn: true })
    printNext()
  } else if (status == 2) {
    printSuccess.value = false
    printMessage.value = '设备在线异常'
    handleCancelPrint()
  } else if (status == 5) {
    printSuccess.value = false
    printMessage.value = '设备被占用'
  }
}
const checkPrinter = () => {
  http
    .get('/Cloudprinter/getPrintStatus', {
      user_id: userStore.userInfoUserId,
      device_sn: printer.value,
      loading: false,
    })
    .then((res) => {
      const status = res.data
      setPrinterStatus(status)
    })
    .catch((err) => {
      printSuccess.value = false
      printMessage.value = err.errormsg
    })
}

// list
const list0 = ref([]) as any
const list1 = ref([]) as any
const ref0 = ref()
const ref1 = ref()
const getList = (pageNo, pageSize) => {
  http
    .get('/Santicket/getPrintVoucherList', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      print_status: tabIndex.value,
      // print_status: 1,
      page_no: pageNo,
      page_size: pageSize,
      device_sn: printer.value,
      loading: false,
    })
    .then((res) => {
      const list = res.data.list.map((item) => {
        return {
          ...item,
          selected: false,
          printed: false,
          showMore: false,
        }
      })
      if (tabIndex.value === 0) {
        ref0.value.complete(list)
      } else if (tabIndex.value === 1) {
        ref1.value.complete(list)
      }
    })
    .catch(() => {
      if (tabIndex.value === 0) {
        ref0.value.complete(false)
      } else if (tabIndex.value === 1) {
        ref1.value.complete(false)
      }
    })
}

// print dialog
const printShow = ref(false)
const printSuccess = ref(true)
const printMessage = ref('')
const printTotal = computed(() => list0.value.filter((item) => item.selected).length)
const printed = computed(() => list0.value.filter((item) => item.printed).length)
const order = ref({}) as any
const checkOrder = () => {
  if (!order.value.order_id) {
    return
  }
  return http
    .get('/Cloudprinter/queryMachine', {
      user_id: userStore.userInfoUserId,
      device_sn: printer.value,
      order_id: order.value.order_id,
      san_log_id: order.value.san_log_id,
      loading: false,
    })
    .then((res) => {
      // 0 表示离线 1 表示在线正常 2 表示在线异常 3 表示打印完成 4 表示打印中 5 表示打印机被占用
      const flag = res.data
      if (flag == 3) {
        order.value.printed = true
        printNext()
      } else if (flag == 4) {
        setTimeout(() => {
          checkOrder()
        }, 2000)
      } else {
        setPrinterStatus(flag)
      }
    })
    .catch((err) => {
      printSuccess.value = false
      printMessage.value = err.errormsg
    })
}
const pushOrder = () => {
  const san_log_ids = list0.value.filter((item) => item.selected && !item.printed).map((item) => item.san_log_id)
  if (Array.isArray(san_log_ids) && san_log_ids.length) {
    return http
      .post('/Cloudprinter/createPrintOrder', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        device_sn: printer.value,
        san_log_id: order.value.san_log_id,
        san_log_ids,
        loading: false,
      })
      .then((res) => {
        order.value.order_id = res.data
        setTimeout(() => {
          checkOrder()
        }, 2000)
      })
      .catch((err) => {
        printSuccess.value = false
        printMessage.value = err.errormsg
      })
  }
}
const printNext = () => {
  order.value = list0.value.find((item) => item.selected && !item.printed)
  if (order.value) {
    pushOrder()
  } else {
    handleClosePrint()
    uni.showToast({ title: '打印完成', icon: 'success', duration: 3000 })
  }
}
const handleCancelPrint = () => {
  return http.post('/Cloudprinter/clearPrintList', {
    user_id: userStore.userInfoUserId,
    san_log_id: order.value.san_log_id,
    device_sn: printer.value,
  })
}
const handleClosePrint = () => {
  printShow.value = false
  printSuccess.value = true
  printMessage.value = ''
  uni.setKeepScreenOn({ keepScreenOn: false })
  getList(1, 10)
}

// life hook
const scene = ref('')
onLoad((options) => {
  scene.value = options.scene as string
})
onShow(() => {
  if (scene.value) {
    http.get('/Cloudprinter/getSceneToParam', { scene: scene.value, loading: false }).then((res) => {
      printer.value = res.data.device_sn
      const busId = res.data.bus_id
      checkLogin(true, busId).then((info) => {
        getList(1, 10)
      })
    })
  }
})
onHide(() => {
  handleCancelPrint()
})
</script>

<template>
  <div class="box">
    <div class="tab-box">
      <div class="tab" :class="{ active: tabIndex === 0 }" @click="handleTabClick(0)">
        <div class="label">待打印</div>
        <div class="line"></div>
      </div>
      <div class="tab" :class="{ active: tabIndex === 1 }" @click="handleTabClick(1)">
        <div class="label">已完成</div>
        <div class="line"></div>
      </div>
    </div>
    <div class="tip">打印机编号 {{ printer }}</div>
    <!-- <scroll-view class="list" v-if="tabIndex === 0" scroll-y> -->
    <z-paging
      v-if="tabIndex === 0"
      ref="ref0"
      v-model="list0"
      class="list"
      :show-loading-more-no-more-view="list0.length > 10 ? true : false"
      empty-view-text="暂无数据"
      :fixed="false"
      :auto="false"
      @query="getList"
    >
      <div v-for="(item, index0) in list0" :key="index0" class="card" @click="item.selected = !item.selected">
        <div class="checkbox">
          <div class="nike-circle" :class="{ 'selected-circle': item.selected }">
            <img class="nike" :class="{ selected: item.selected }" src="/static/img/checkbox.png" />
          </div>
        </div>
        <div class="info">
          <div class="title">{{ item.name }}</div>
          <div class="tag-box">
            <div :class="{ 'multiline-ellipsis': !item.showMore }">
              <template v-if="item.space_name.length > 0">
                <template v-for="(tag, idx0) in item.space_name" :key="idx0">
                  <div v-if="tag" class="tag light">{{ tag }}</div>
                </template>
              </template>
              <div v-else class="tag light">入场</div>
            </div>
            <div class="tag">{{ item.base_duration }} {{ item.duration_unit == 1 ? '小时' : '分钟' }}</div>
          </div>
          <div class="license">
            <div class="code">凭证 {{ item.consume_sn }}</div>
            <div class="name">{{ item.bus_name }}</div>
          </div>
        </div>
      </div>
    </z-paging>
    <!-- </scroll-view> -->
    <!-- <scroll-view class="list" v-if="tabIndex === 1" scroll-y> -->
    <z-paging
      v-if="tabIndex === 1"
      ref="ref1"
      v-model="list1"
      class="list"
      style="bottom: 0px"
      :show-loading-more-no-more-view="list1.length > 10 ? true : false"
      empty-view-text="暂无数据"
      :fixed="false"
      :auto="false"
      @query="getList"
    >
      <div v-for="(item, index1) in list1" :key="index1" class="card" style="border-color: #c3c3c3">
        <div class="info">
          <div class="title">{{ item.name }}</div>
          <div class="tag-box" :class="{ 'multiline-ellipsis': !item.showMore }">
            <div v-if="item.space_name.length > 0" :class="{ 'multiline-ellipsis': !item.showMore }">
              <template>
                <template v-for="(tag, idx1) in item.space_name" :key="idx1">
                  <div v-if="tag" class="tag light">{{ tag }}</div>
                </template>
              </template>
            </div>
            <div v-else class="tag light">入场</div>
            <div class="tag">{{ item.base_duration }} {{ item.duration_unit == 1 ? '小时' : '分钟' }}</div>
          </div>
          <div class="license">
            <div class="code" style="color: #c3c3c3">凭证 {{ item.consume_sn }}</div>
            <div class="name">{{ item.bus_name }}</div>
          </div>
        </div>
      </div>
      <!-- </scroll-view> -->
    </z-paging>
    <view v-if="tabIndex === 0" class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" :disabled="printTotal === 0" @click="handlePrint">打印</button>
    </view>
    <div v-if="printShow" class="dialog-mask"></div>
    <div v-if="printShow" class="dialog">
      <template v-if="printSuccess">
        <div class="dialog-icon">
          <img class="icon" src="/static/img/print.png" />
        </div>
        <div class="dialog-tip">
          打印中，请稍后
          <img class="bean" src="../../static/img/bean.svg" />
        </div>
        <div class="dialog-desc">
          共 <span class="value">{{ printTotal }}</span> 票 ，已打印 <span class="value">{{ printed }}</span> 张
        </div>
        <div
          v-if="printed < printTotal"
          class="dialog-btn"
          @click="
            () => {
              handleClosePrint()
              handleCancelPrint()
            }
          "
        >
          取消打印
        </div>
        <div v-else class="dialog-btn" @click="handleClosePrint">关闭</div>
      </template>
      <template v-else>
        <div class="dialog-icon">
          <img class="icon" src="/static/img/print-error.png" />
        </div>
        <div class="dialog-tip">打印机异常，请联系管理员</div>
        <div class="dialog-desc">异常原因：{{ printMessage }}</div>
        <div class="dialog-btn" @click="handleClosePrint">关闭</div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.bean {
  width: 44rpx;
  height: 44rpx;
  margin-left: 20rpx;
}

.multiline-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1; /* start showing ellipsis when 3rd line is reached */
  white-space: pre-wrap; /* let the text wrap preserving spaces */
}

@keyframes switch-nike {
  0% {
    scale: 0.4;
  }
  50% {
    scale: 1.5;
  }
  100% {
    scale: 1;
  }
}

.box {
  .tab-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;

    .tab {
      font-size: 30rpx;
      font-weight: 400;
      color: #000000;

      .label {
        padding: 14rpx 27rpx;
      }
    }

    .active {
      font-size: 36rpx;
      font-weight: bold;
      color: #03080e;
      @include center;
      flex-direction: column;

      .line {
        width: 38rpx;
        height: 6rpx;
        background: $uni-color-success;
        border-radius: 3rpx;
      }
    }
  }

  .tip {
    font-size: 26rpx;
    font-weight: bold;
    color: #03080e;
    margin: 36rpx 0 30rpx 30rpx;
  }

  .list {
    position: absolute;
    top: 180rpx;
    left: 0;
    right: 0;
    bottom: 190rpx;

    .card {
      width: 650rpx;
      min-height: 160rpx;
      background: #ffffff;
      border: 1px solid $theme-text-color-other;
      border-radius: 20rpx;
      margin: 0 auto 20rpx auto;
      padding: 20rpx;
      display: flex;
      flex-direction: row;

      .checkbox {
        /* @include center; */
        /* margin: 68rpx 40rpx 0 7rpx; */
        display: flex;
        align-items: center;
        width: 72rpx;
        min-height: 72rpx;

        .nike-circle {
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          border: 2rpx solid #e8e8e8;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .nike {
            width: 28rpx;
            height: 28rpx;
            border-radius: 50%;
          }
        }
      }

      .selected-circle {
        background-color: $theme-text-color-other;
      }

      .selected {
        animation-name: switch-nike;
        animation-duration: 0.1s;
        animation-iteration-count: 1;
        animation-timing-function: ease-out;
        animation-fill-mode: forwards;
      }

      .info {
        width: 100%;

        .title {
          font-size: 32rpx;
          font-weight: bold;
          color: #313131;
          margin-top: 6rpx;
        }

        .tag-box {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;

          .tag {
            display: inline-block;
            font-size: 24rpx;
            font-weight: 400;
            color: #313131;
            margin-top: 20rpx;
            margin-right: 20rpx;
          }

          .light {
            background-color: rgba(255, 116, 39, 0.1);
            padding: 2rpx 6rpx;
          }
        }

        .license {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;
          margin-top: 20rpx;

          .code {
            font-size: 24rpx;
            font-weight: bold;
            color: $theme-text-color-other;
          }

          .name {
            font-size: 24rpx;
            font-weight: 400;
            color: #313131;
          }
        }
      }
    }
  }

  .dialog-mask {
    width: 100%;
    height: 100vh;
    background: #000000;
    opacity: 0.6;
    z-index: 998;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .dialog {
    width: 540rpx;
    height: 540rpx;
    background: #ffffff;
    border-radius: 20rpx;
    z-index: 999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    .dialog-icon {
      width: 360rpx;
      height: 200rpx;
      margin-top: 50rpx;

      .icon {
        width: 360rpx;
        height: 200rpx;
      }
    }

    .dialog-tip {
      width: 100%;
      font-size: 36rpx;
      font-weight: bold;
      color: #1b1b1b;
      margin-top: 30rpx;
      @include center;
    }

    .dialog-desc {
      width: 100%;
      font-size: 30rpx;
      font-weight: 400;
      color: #1b1b1b;
      text-align: center;
      margin-top: 30rpx;

      .value {
        color: $theme-text-color-other;
      }
    }

    .dialog-btn {
      width: 261rpx;
      height: 71rpx;
      background: #ffffff;
      border: 1px solid $theme-text-color-other;
      border-radius: 35rpx;
      @include center;
      font-size: 26rpx;
      font-weight: bold;
      color: $theme-text-color-other;
      margin-top: 40rpx;
    }
  }
}
</style>
