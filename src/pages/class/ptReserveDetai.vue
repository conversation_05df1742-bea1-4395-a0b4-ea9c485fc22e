<template>
  <view class="pt-reserve-box theme-bg footer-hasfixed">
    <view class="coach">
      <image class="avatar" :src="privateInfo.avatar" />
      <view class="name">{{ privateInfo.coach_name }}</view>
      <view class="des">{{ privateInfo.position }}</view>
    </view>
    <view class="class-info theme-bg">
      <view class="status-text" :class="privateInfo.status == 1 ? 'theme-text' : ''">
        {{
          privateInfo.status == 1
            ? '已约'
            : privateInfo.status == 2
            ? '已上'
            : privateInfo.status == 3
            ? '爽约'
            : privateInfo.status == 4
            ? '完成'
            : '取消'
        }}
      </view>
      <image
        class="class-thum"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/class.png"
        mode="aspectFill"
      />
      <view class="rig">
        <view class="name">{{ privateInfo.class_name }}</view>
        <view class="item">
          <text class="class-time"
            >时长:<text class="theme-color-other">{{ privateInfo.class_duration }}</text
            >分钟</text
          >
          <text v-if="privateInfo.user_no > 1" style="margin-left: 10rpx"> | 1对{{ privateInfo.user_no }}</text>
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" />
          {{ privateInfo.date_time }}&nbsp;<text class="time"
            >{{ privateInfo.beg_time }}-{{ privateInfo.end_time }}</text
          >
        </view>
      </view>
    </view>
    <view class="form-items theme-bg">
      <view class="box-tit">预约信息</view>
      <view class="item">
        <text class="key">创建时间</text>
        <text class="value">{{ privateInfo.create_time }}</text>
      </view>
      <view class="item">
        <text class="key">预约方式</text>
        <text class="value"
          >{{ privateInfo.create_way == 1 ? '前台代约' : privateInfo.create_way == 2 ? '教练代约' : '自主预约-'
          }}{{
            privateInfo.create_way == 1 || privateInfo.create_way == 2
              ? ''
              : privateInfo.create_way == 4
              ? '支付宝小程序'
              : '微信小程序'
          }}</text
        >
      </view>
      <view v-if="privateInfo.cancel_time && privateInfo.status == 5" class="item">
        <text class="key">取消时间</text>
        <text class="value">{{ privateInfo.cancel_time }}</text>
      </view>
      <view v-if="privateInfo.cancel_type && privateInfo.status == 5" class="item">
        <text class="key">取消方式</text>
        <text class="value">
          {{ privateInfo.cancel_type == 1 ? '前台取消' : privateInfo.cancel_type == 4 ? '教练取消' : '自主取消-' }}
          {{ privateInfo.cancel_type == 2 || privateInfo.cancel_type == 3 ? '微信小程序' : '' }}
        </text>
      </view>
    </view>
    <view v-if="privateInfo.status == 1" class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <button class="normal-btn outer-org" @tap="cancelPtReserve(privateInfo.id, false, privateInfo.appt_type, privateInfo.refund_rate)">取消预约</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import RoomSeats from './components/RoomSeats'
import http from '@/utils/request'
import { usePtClass } from '@/hooks/usePtClass.ts'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const { cancelPtReserve } = usePtClass()
const formData = reactive({
  pt_id: '',
})
const privateInfo = reactive({
  avatar: '',
  coach_name: '',
})
onLoad((options) => {
  formData.pt_id = options.pt_id
})
onShow(() => {
  getInfo()
})
const getInfo = () => {
  http
    .get('/classMark/ptMarkDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      ...formData,
    })
    .then((res) => {
      Object.assign(privateInfo, res.data)
    })
}
</script>

<style lang="scss">
.pt-reserve-box {
  box-sizing: border-box;
  min-height: 100%;

  .coach {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 50rpx;
    .avatar {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      box-shadow: 0 3rpx 8rpx 0 rgba(65, 92, 145, 0.15);
    }
    .name {
      margin-top: 20rpx;
      font-size: 30rpx;
    }
    .des {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: $theme-text-color-grey;
    }
  }
}
.class-info {
  position: relative;
  overflow: hidden;
  margin: 30rpx 20rpx;
  padding: 30rpx;
  display: flex;
  font-size: 24rpx;
  align-items: flex-start;
  border: 1px solid #e8e8e8;
  border-radius: 20rpx;
  .class-thum {
    width: 161rpx;
    height: 94rpx;
    margin-right: 30rpx;
  }
  .name {
    font-size: 30rpx;
    font-weight: bold;
  }
  .class-time {
    color: $theme-text-color-grey;
  }
  .item {
    display: flex;
    align-items: center;
    margin-top: 15rpx;
  }
}
</style>
