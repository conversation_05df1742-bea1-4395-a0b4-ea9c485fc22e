<template>
  <view class="comment-detail footer-hasfixed">
    <ChargeClassCard
      v-model="selCard.card_user_id"
      :card-list="classInfo.cards"
      :mark-object="classInfo.mark_object"
      :class-price="classInfo.class_price"
      :type="1"
      @confirm="cardChanged"
    />
  </view>
</template>

<script setup lang="ts" name="protocol">
import ChargeClassCard from './components/ChargeClassCard.vue'
const instanceEventChannel = ref()
const classInfo = reactive({
  class_id: '',
  mark_object: 2,
  class_name: '',
  cards: [],
  class_price: '',
})
const selCard = reactive({
  card_user_id: '',
  card_id: '',
  pay_type: 1, // 1卡内余额支付 2微信支付
  todeduct: '',
  maxresv_num: '',
  card_name: '',
  card_type_id: null,
})
function cardChanged(info) {
  Object.assign(selCard, { ...info, maxresv_num: info.maxresv_num || Infinity })
  instanceEventChannel.value.emit('confirm-select', {
    selectedCard: selCard,
  })
  uni.navigateBack()
}
onLoad((option) => {
  instanceEventChannel.value = getCurrentInstance().proxy.getOpenerEventChannel()
  // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
  instanceEventChannel.value.once('init-charge-list', function ({ reserveInfo, selectedCard }) {
    Object.assign(classInfo, reserveInfo)
    Object.assign(selCard, { ...selectedCard, maxresv_num: selectedCard.maxresv_num || Infinity })
  })
})
</script>
<style lang="scss" scoped></style>
