<template>
  <view class="footer-hasfixed">
    <OpenClassInfoMore :only-class-info="formData.only_class_info" :class-info="classInfo" />

    <view class="des-wrap theme-bg">
      <view class="box-tit">课程描述</view>
      <view class="info-des rich-text">
        <rich-text :nodes="classInfo.description"></rich-text>
      </view>
    </view>

    <!-- <view class="des-wrap theme-bg">
      <view class="box-tit">上课流程</view>
      <view class="process">
        <view class="item">
          <view class="circle">1</view>
          <text>预付款成功</text>
        </view>
        <view class="item">
          <view class="circle">2</view>
          <text>获取短信通知</text>
        </view>
        <view class="item">
          <view class="circle">3</view>
          <text>用微信扫签到二维码</text>
        </view>
        <view class="item">
          <view class="circle">4</view>
          <text>验证成功入场上课</text>
        </view>
      </view>
    </view> -->

    <view class="des-wrap theme-bg">
      <view class="box-tit">预约须知</view>
      <view class="item-notice">
        <view class="circle-8" />
        <text>请您根据自身情况，选择适合强度的课程</text>
      </view>
      <view v-if="classInfo.stop_mark" class="item-notice">
        <view class="circle-8" />
        <text>上课前{{ classInfo.stop_mark }}分钟停止预约，{{ classInfo.stop_result_mark }}分钟停止取消</text>
      </view>
      <!-- <view
        v-if="classInfo.business_miss_setting_data && classInfo.business_miss_setting_data.miss_turn_on"
        class="item-notice"
      >
        <view class="circle-8" />
        <text>
          门店已开启爽约惩罚，{{ classInfo.business_miss_setting_data.miss_cycle }}天内爽约1次禁止约课{{
            classInfo.business_miss_setting_data.one
          }}天</text
        >
      </view> -->
      <blockquote v-if="classInfo.business_miss_setting_data && classInfo.business_miss_setting_data.miss_turn_on">
        <view class="item-notice">
          <view class="circle-8" />
          <text>门店已开启爽约惩罚</text>
        </view>
        <view class="item-notice sub-item" v-for="(item, index) in subList">
          <view class="circle-8" />
          <text>{{ classInfo.business_miss_setting_data.miss_cycle }}天内爽约{{ index + 1 }}次禁止约课{{ item }}天</text>
        </view>
      </blockquote>
    </view>
  </view>
  <view class="fixed-bottom-wrap theme-bg">
    <view
      v-if="classInfo.waitting_enable_status != 1 && classInfo.checkmark != 6 && classInfo.checkmark != 7"
      class="buttons"
    >
      <button
        v-if="formData.only_class_info !== 1 && classInfo.checkmark == 0 || classInfo.checkmark == 1 || classInfo.class_category == 1"
        open-type="share"
        class="normal-btn lef-flex friend mgr"
      >
        约朋友
      </button>

      <button
        v-if="classInfo.class_category == 0"
        class="normal-btn rig-flex"
        :class="RESERVE_CLASS[classInfo.checkmark] || 'disabled'"
        :disabled="classInfo.can_mark === 0"
        @tap="handleReserve"
      >
        {{ RESERVE_TEXT[classInfo.checkmark] || RESERVE_TEXT[0] }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts" name="class">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useOpenClass } from '@/hooks/useOpenClass'
import OpenClassInfoMore from './components/OpenClassInfoMore.vue'
import { formatDate, unescapeHTML } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin'

const { checkLogin } = useLogin()
//5 爱动 6 候补中 7 待确认 8已开始 9已结束
const RESERVE_TEXT = ['立即预约', '已预约', '已完成', '已满', '已过期', '预约中', '', '', '已开始', '已结束']
const RESERVE_CLASS = ['reserve', 'outer-green', 'finished', 'disabled', 'disabled', 'disabled']

const userStore = useUserStore()
const { goReservePage, cancelReserve } = useOpenClass()

const subList = ref<any>([]);
const formData = reactive<Record<string, any>>({
  course_schedule_id: '',
  day: '',
  coach_id: '',
  only_class_info: 0,
  class_id: '',
})
const classInfo = reactive<Record<string, any>>({
  class_name: '',
  checkmark: '',
})
const selectedCard = reactive<Record<string, any>>({
  card_user_id: '',
})
onLoad((options) => {
  formData.course_schedule_id = options.id || ''
  formData.day = options.day || formatDate(new Date(), 'yyyy-MM-dd')
  formData.class_id = options.class_id || ''
  formData.only_class_info = options.only_class_info ? 1 : 0
  formData.coach_id = options.coach_id || ''
})

onShow(() => {
  checkLogin(false).then(() => {
    getInfo()
  })
})
function setRoom(ids) {
  formData.seats = ids
}
const getInfo = () => {
  http
    .get('/Course/getCourseDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      ...formData,
    })
    .then((res) => {
      Object.assign(classInfo, {
        ...res.data,
        description: unescapeHTML(res.data.description),
      })

      const miss =  classInfo.business_miss_setting_data
      subList.value = [miss.one, miss.two, miss.three, miss.four, miss.five].filter(item => item) || []
    })
}
function handleReserve() {
  if (classInfo.checkmark == 1) {
    cancelReserve(classInfo.mark_id)
  } else {
    goReservePage(formData)
  }
}
onShareAppMessage((options) => {
  return {
    path: `/pages/class/openClassDetail?bus_id=${userStore.userInfoBusId}&id=${formData.course_schedule_id}&coach_id=${formData.coach_id}&class_id=${formData.class_id}&from=share`,
  }
})
</script>
<style lang="scss">
[data-theme='dark'] {
  .item-notice {
    color: #fff;
  }
}
.tips {
  margin-top: 26rpx;
  padding-bottom: 30rpx;
  line-height: 1.75;
  .title {
    font-weight: bold;
    color: #050505;
  }
}
.des-wrap {
  padding: 0 20rpx 20rpx;
}
.item-notice {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  color: #03080e;
  .circle-8 {
    margin: 0rpx 18rpx;
    width: 8rpx;
    height: 8rpx;
    background: $theme-text-color-grey;
    border-radius: 50%;
  }
}

.sub-item {
  margin-left: 40rpx;
}

.info-des {
  padding-top: 0;
  font-size: 24rpx;
  line-height: 1.7;
}
.process {
  height: 358rpx;
  padding: 10rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;

  .line {
    position: absolute;
    height: 300rpx;
    width: 2rpx;
    background-color: #e7e7e7;
    left: 53rpx;
    top: 48rpx;
    z-index: 1;
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 62rpx;
    position: relative;
    z-index: 2;

    .circle {
      font-size: 24rpx;
      width: 44rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      box-sizing: border-box;
      border: 1rpx solid #e7e7e7;
      background-color: #f5f7f9;
      border-radius: 50%;
      color: #898989;
    }
    text {
      color: #313131;
      width: 524rpx;
      background-color: #f5f7f9;
      height: 62rpx;
      line-height: 62rpx;
      text-align: center;

      font-size: 22rpx;
      position: relative;

      &::before {
        content: '';
        width: 0;
        height: 0;
        display: block;
        position: absolute;
        left: -21rpx;
        top: 24rpx;
        border: solid transparent;
        border-width: 7rpx 12rpx;
        border-right-color: #f5f7f9;
      }
    }
  }
}
</style>
