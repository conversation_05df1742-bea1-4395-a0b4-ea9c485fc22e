<template>
  <view class="comment-detail footer-hasfixed">
    <view class="top-info theme-bg">
      <PtInfo v-if="is_pt == 1" :info="resInfo" />
      <OpenClassInfo v-else :class-info="resInfo" />
    </view>
    <CommentDetail
      v-model="commentInfo"
      :is-pt="is_pt == 1"
      :is-comment="resInfo.is_comment"
      :config="resInfo"
    />
    <view v-if="resInfo.is_comment || resInfo.is_add_comment" class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="handleComment">
        {{ resInfo.is_comment ? '提交评价内容' : '发布追评' }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts" name="protocol">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import OpenClassInfo from './components/OpenClassInfo'
import PtInfo from './components/PtInfo'
import CommentDetail from './components/CommentDetail'
const userStore = useUserStore()
const class_mark_id = ref()
const sign_log_id = ref()
const is_pt = ref()
const resInfo = reactive({
  class_name: '',
  is_comment: false,
  comment_setting: Object,
})
const commentInfo = reactive({
  set_type_json: [],
  tag_json: [],
  advice: '',
  add_advice: '',
})
onLoad((options) => {
  class_mark_id.value = options.class_mark_id || ''
  sign_log_id.value = options.sign_log_id || ''
  is_pt.value = options.is_pt
  getInfo()
})
function getInfo() {
  const url =
    is_pt.value == 1 ? 'Coursecomment/getDetailBySignLogId' : 'Coursecomment/getDetailByClassMarkId'
  http
    .get(url, {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      class_mark_id: class_mark_id.value,
      sign_log_id: sign_log_id.value,
    })
    .then((res) => {
      const resData = res.data
      Object.assign(resInfo, resData)
      if (resData.comment_detail) {
        commentInfo.set_type_json = resData.comment_detail.set_type_json
        commentInfo.tag_json = resData.comment_detail.tag_json
        commentInfo.advice = resData.comment_detail.advice
      } else {
        const setList =
          is_pt.value == 1
            ? res.data.comment_setting?.COACH_RATING_TYPE_LIST
            : res.data.comment_setting?.GROUP_CLASS_GRADING_TYPE_LIST
        commentInfo.set_type_json = setList.map((item) => {
          return {
            ...item,
            star: 0,
          }
        })
      }
    })
}
function handleComment() {
  if (resInfo.is_comment) {
    handleAdd()
  } else {
    handleAddMore()
  }
}
function handleAdd() {
  let isAllHaveStar = true
  for (const item of commentInfo.set_type_json) {
    if (item.star === 0) {
      isAllHaveStar = false
      break
    }
  }
  if (!isAllHaveStar) {
    uni.showToast({
      title: '所有维度评价必须打星',
      icon: 'none',
    })
    return
  }
  const url =
    is_pt.value == 1 ? 'Coursecomment/addFirstCommentByPrivate' : 'Coursecomment/addFirstComment'
  http
    .post(url, {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      mark_id: class_mark_id.value,
      sign_log_id: sign_log_id.value,
      set_type_json: JSON.stringify(commentInfo.set_type_json),
      tag_json: JSON.stringify(commentInfo.tag_json),
      advice: commentInfo.advice,
    })
    .then((res) => {
      uni.showToast({
        title: '评价成功',
        image: '/static/img/success.png',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    })
}
function handleAddMore() {
  if (!commentInfo.add_advice) {
    uni.showToast({
      title: '请填写您的追评',
      icon: 'none',
    })
    return
  }
  const url =
    is_pt.value == 1 ? 'Coursecomment/addPrivateSecondComment' : 'Coursecomment/addSecondComment'
  http
    .post(url, {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      mark_id: class_mark_id.value,
      sign_log_id: sign_log_id.value,
      add_advice: commentInfo.add_advice,
    })
    .then((res) => {
      uni.showToast({
        title: '追评成功',
        image: '/static/img/success.png',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    })
}
</script>
<style lang="scss" scoped>
.comment-detail {
  padding: 20rpx;
}
.top-info {
  padding-bottom: 15rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
</style>
