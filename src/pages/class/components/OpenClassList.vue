<template>
  <view class="pt-list-wrap">
    <view class="date-switch theme-bg">
      <view
        v-for="item in dateList"
        :key="item.day"
        class="date"
        :class="today == item.date ? 'inset-shadow active' : ''"
        @tap="setDay(item, getClassInfo)"
      >
        <text class="week">{{ item.week }}</text>
        <text class="day">{{ item.day }}</text>
      </view>
    </view>
    <view class="private-list" :style="{ height: 'calc(100% - 166rpx)' }">
      <z-paging
        ref="openPaging"
        v-model="privateList"
        :show-loading-more-no-more-view="privateList.length > 10 ? true : false"
        :fixed="false"
        :refresher-enabled="false"
        empty-view-text="暂无相关团课数据"
        :empty-view-img-style="{ width: '103rpx', height: '144rpx' }"
        empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png"
      >
        <view v-for="item in privateList" :key="item.id">
          <OpenClassListItem :bus-id="busId" :day="today" :config="config" :item="item" @on-callback="getClassInfo" />
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts" name="OpenClassList">
import http from '@/utils/request'
import { useNextDays } from '@/hooks/useNextDays'
import { useUserStore } from '@/store/user'
import OpenClassListItem from './OpenClassListItem.vue'
import { useThemeStore } from '@/store/theme'
import { useMerchant } from '@/store/merchant'

const thmeStore = useThemeStore()
const useMerchantStore = useMerchant()
const props = defineProps<{
  config: Record<string, any>
}>()
const openPaging = ref()
const { today, dateList, setDay, setDateList } = useNextDays()
setDateList()

const userStore = useUserStore()
const privateList = ref<Record<string, any>>([])
const busId = props.config?.bus_id || userStore.userInfoBusId
const getClassInfo = async () => {
  const user_id = props.config?.bus_id
    ? await useMerchantStore.getUserIdByBus(props.config?.bus_id)
    : userStore.userInfoUserId
  if (privateList.value.length) {
    openPaging.value.clear()
  }
  http
    .get('/Schedule/getCourseScheduleList', {
      bus_id: busId,
      user_id,
      merchant_type_get: thmeStore.isShowMerchantMode,
      day: today.value,
    })
    .then(async (res) => {
      if (!openPaging.value) {
        await nextTick()
      }
      openPaging.value.setLocalPaging(res.data.list)
    })
    .catch(async (res) => {
      if (!openPaging.value) {
        await nextTick()
      }
      openPaging.value.setLocalPaging([], false)
    })
}
getClassInfo()
</script>
<style lang="scss"></style>
