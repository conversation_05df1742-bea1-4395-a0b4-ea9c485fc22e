<template>
  <view class="pt-info">
    <view class="left">
      <image class="pic" :src="info.avatar" />
      <view class="item-info">
        <view class="name">{{ info.class_name }}</view>
        <view class="item">
          <view class="des-lef">
            {{ info.coach_name }}
          </view>
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" />
          {{ info.class_time }}
        </view>
      </view>
    </view>
    <view class="reserve"> </view>
  </view>
</template>

<script setup lang="ts">
import { usePtClass } from '@/hooks/usePtClass.ts'
const { cancelPtReserve } = usePtClass()
const props = defineProps({
  info: {
    type: Object,
  },
})
function goReserveDetail() {
  uni.navigateTo({
    url: `/pages/class/ptReserveDetai?pt_id=${props.info.id}`,
  })
}
function refreshPage() {
  uni.$emit('refresh-class-record')
}
</script>
<style lang="scss" scoped>
.pt-info {
  position: relative;
  box-sizing: border-box;
  width: 690rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin: 25rpx auto 0;
  overflow: hidden;
  font-size: 24rpx;
  .left {
    display: flex;
    align-items: flex-start;
  }
  .pic {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: 26rpx;
  }
  .pic-thum {
    width: 161rpx;
    height: 94rpx;
    border-radius: 6rpx;
    margin-right: 14rpx;
  }
  .item-info {
    .name {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      > text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.1;
      }
    }
    .des {
      margin-top: 20rpx;
      display: flex;
      justify-items: center;
    }
    .des-lef {
      font-weight: bold;
      margin-right: 8rpx;
      max-width: 400rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .des-num {
      color: #ff7427;
    }
    .item {
      display: flex;
      align-items: center;
      font-size: 23rpx;
      margin-top: 15rpx;
    }
  }
}
</style>
