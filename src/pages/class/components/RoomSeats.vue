<template>
  <view class="room-box-wrap theme-bg" :class="{ mt20: !canSelect }">
    <view class="box-tit room-top">
      <view>
        {{ canSelect ? '选座' : '座位号:' + seatsNum }}
      </view>
      <view class="rig">
        <template v-if="!canSelect"> <view class="rig-tag is-self"></view> 自己 </template>
        <template v-if="canSelect"> <view class="rig-tag not-sit"></view> 可选 </template>
        <view class="rig-tag is-sit"></view> {{ canSelect ? '已选' : '他人' }}
      </view>
    </view>
    <view class="room-box">
      <view class="room-btn-wrap" :style="{ width: rowArrMax * 76 + 'rpx' }">
        <view class="room-btn">讲台</view>
      </view>
      <view class="room-box-con" :style="{ height: rowArr.length * 76 + 'rpx', width: rowArrMax * 76 + 'rpx' }">
        <view v-for="x in rowArr.length" :key="x" class="seat-row" :style="{ width: rowArr[x - 1] * 76 + 'rpx' }">
          <view
            v-for="y in rowArr[x - 1]"
            :key="y"
            class="seat-num"
            :class="{
              'is-choose': seatsListInfo[x + '_' + y]?.is_self,
              'is-used': !seatsListInfo[x + '_' + y]?.is_self && seatsListInfo[x + '_' + y]?.is_used,
            }"
            @tap="chooseNum(x, y)"
          >
            <span>
              {{ seatsListInfo[x + '_' + y]?.seat_number }}
            </span>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  seatsList: Array,
  canSelect: Boolean,
  reserveNum: [Number, String],
})
const rowArr = ref([])
const chooseIds = ref([])
const rowArrMax = ref(0)
const seatsListInfo = ref([])
const seatsNum = ref('')
watchEffect(() => {
  if (props.seatsList) {
    getRowArr(props.seatsList)
  }
})
function getRowArr(list) {
  const seatsNumArr = []
  list.forEach((item) => {
    seatsListInfo.value[+item.position_x + '_' + +item.position_y] = item
    if (item.is_self) {
      seatsNumArr.push(item.seat_number)
    }
  })
  seatsNum.value = seatsNumArr.join('、')
  let arr = []
  if (list && list.length) {
    let arrIndex = 0
    list.forEach((item, i) => {
      if (i > 0 && list[i - 1].position_x !== item.position_x) {
        arr.push(i - arrIndex)
        arrIndex = i
      }
    })
    // 最后一排或者只有一排的时候
    arr.push(list.length - arrIndex)
  } else {
    arr = ['']
  }
  rowArrMax.value = arr[0] || 0
  arr.forEach((num, index) => {
    if (num > rowArrMax.value) {
      rowArrMax.value = num
    }
  })
  rowArr.value = arr
}
const emit = defineEmits(['on-select'])
function chooseNum(x, y) {
  if (!props.canSelect) {
    return
  }
  const curId = seatsListInfo.value[x + '_' + y].id
  const isUsed = seatsListInfo.value[x + '_' + y].is_used
  if (isUsed) {
    return
  }
  if (chooseIds.value.indexOf(curId) !== -1) {
    seatsListInfo.value[x + '_' + y].is_self = false
    chooseIds.value.splice(chooseIds.value.indexOf(curId), 1)
    emit('on-select', chooseIds)
    return
  }
  if (props.reserveNum && chooseIds.value.length >= props.reserveNum) {
    uni.showToast({ title: '超过预约人数！', icon: 'error' })
    return
  }
  seatsListInfo.value[x + '_' + y].is_self = true
  chooseIds.value.push(curId)
  emit('on-select', chooseIds)
}
</script>
<style lang="scss">
.room-box-wrap {
  overflow: hidden;
  padding: 0 30rpx 30rpx;
  margin: 0 20rpx 20rpx;
  .room-box {
    overflow-x: scroll;
    background: #fff;
    width: 100%;
    text-align: center;
  }
  .room-box-con {
    min-height: 160rpx;
    margin: 0 auto;
    icon {
      display: inline-block;
      width: 30rpx;
      height: 30rpx;
    }
    .seat-row {
      height: 76rpx;
      text-align: center;
      margin: 0 auto;
    }
    .seat-num {
      display: inline-block;
      min-width: 60rpx;
      height: 60rpx;
      line-height: 60rpx;
      margin: 0 16rpx 16rpx 0;
      border-radius: 10rpx;
      border: 1rpx solid #d6d6d6;
      box-sizing: border-box;
      &.is-choose {
        font-size: 28rpx;
        line-height: 60rpx;
        background: var(--THEME-COLOR);
        border-color: var(--THEME-COLOR);
      }
      &.is-used {
        line-height: 60rpx;
        font-size: 28rpx;
        background: $theme-text-color-other;
        border-color: $theme-text-color-other;
        color: #fff;
      }
    }
  }
  .room-top {
    font-size: 28rpx;
    margin-bottom: 10rpx;
  }
  .rig {
    display: flex;
    justify-items: center;
    align-items: center;
    font-size: 24rpx;
    .rig-tag {
      width: 40rpx;
      height: 40rpx;
      line-height: 40rpx;
      text-align: center;
      border-radius: 5rpx;
      box-sizing: border-box;
      margin-right: 20rpx;
    }
    .not-sit {
      border: 1rpx solid #d6d6d6;
    }
    .is-sit {
      margin-left: 30rpx;
      background: $theme-text-color-other;
      border: 1rpx solid $theme-text-color-other;
    }
    .is-self {
      background: var(--THEME-COLOR);
      border-color: var(--THEME-COLOR);
    }
  }
  .room-btn-wrap {
    margin: 0 auto 30rpx;
    text-align: center;
    min-width: 289rpx;
  }
  .room-btn {
    display: inline-block;
    border: 1rpx solid rgb(187, 187, 187);
    border-radius: 5rpx;
    background-color: rgb(238, 238, 238);
    width: 289rpx;
    height: 65rpx;
    line-height: 65rpx;
    font-size: 26rpx;
    color: #333;
  }
}
</style>
