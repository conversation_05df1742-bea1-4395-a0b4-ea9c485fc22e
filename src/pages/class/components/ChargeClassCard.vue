<template>
  <view>
    <view class="modal-card-list">
      <view
        v-for="(item, index) in allChooseList"
        :key="index"
        class="charge-item"
        :class="{ checked: checkedVal === item.card_user_id }"
        @tap="handleChooseCard(item)"
      >
        <view class="checkbox-con">
          <uni-icons
            v-if="checkedVal === item.card_user_id"
            type="checkbox-filled"
            size="24"
            color="#FF7427"
          ></uni-icons>
          <view v-else class="checkbox-circle"></view>
        </view>
        <view>
          <view class="charge-name">{{ item.card_name }}</view>
          <view v-if="item.maxresv_num" class="charge-des"> 最多预约{{ item.maxresv_num }}人 </view>
          <view v-if="!(item.pay_type === 1 && item.card_type_id === 1)" class="charge-des">
            {{ item.pay_type === 1 ? '卡余额扣除' : '支付' }}
            <text>{{ getPriceDes(item) }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn friend" @tap="handleConfirm">确认</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const props = defineProps({
  classPrice: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [Number, String],
  },
  cardList: {
    type: Array,
    default: () => [],
  },
  // 约课对象 1代表支持非会员 0仅会员
  markObject: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])
const checkedVal = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  },
})

const allChooseList = computed(() => {
  const preArr =
    props.markObject === 1
      ? [
          {
            card_user_id: '',
            card_type_id: null,
            card_id: '',
            pay_type: 2,
            todeduct: props.classPrice,
            maxresv_num: Infinity,
            card_name: '无/不使用会员卡',
          },
        ]
      : []
  return preArr.concat(props.cardList)
})
function handleConfirm() {
  const info = allChooseList.value.find((item) => item.card_user_id === checkedVal.value)
  if (info) {
    emit('confirm', info)
  } else {
    uni.showToast({
      title: '请选择',
      icon: 'none',
    })
  }
}

function getPriceDes(info) {
  // pay_type 1卡内余额支付 2微信支付
  const { card_type_id, pay_type, todeduct } = info
  const typeName = card_type_id === 1 ? '天' : card_type_id === 2 ? '次' : card_type_id === 3 ? '元' : '节'
  return `${todeduct}${pay_type === 1 ? typeName : '元'}/人`
}
function handleChooseCard(card) {
  checkedVal.value = card.card_user_id
}
</script>

<style lang="scss" scoped>
.modal-card-list {
  padding: 0 20rpx;
}
.charge-item {
  position: relative;
  margin-top: 30rpx;
  padding: 28rpx;
  background: #fff2eb;
  border: 1rpx solid #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: $theme-text-color-grey;
  overflow: hidden;
  &.checked {
    background: #fff2eb;
    border: 1rpx solid #ff7427;
  }
  .charge-name {
    color: $theme-text-color;
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    justify-items: center;
  }
  .charge-des {
    margin-bottom: 20rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .checkbox-con {
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
  }
  .checkbox-circle {
    box-sizing: border-box;
    width: 40rpx;
    height: 40rpx;
    background: #ffffff;
    border: 4rpx solid #e8e8e8;
    border-radius: 50%;
  }
}
</style>
