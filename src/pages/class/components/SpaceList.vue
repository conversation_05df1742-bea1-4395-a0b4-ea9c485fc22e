<template>
  <view class="space-list" :class="inviteType ? 'full-screen' : ''" :style="{ height: '100%' }">
    <z-paging
      ref="paging"
      v-model="privateList"
      :show-loading-more-no-more-view="privateList.length > 10 ? true : false"
      :fixed="false"
      :refresher-enabled="false"
      :empty-view-img-style="{ width: '103rpx', height: '144rpx' }"
      empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png"
      empty-view-text="暂无相关数据"
    >
      <view v-for="item in privateList" :key="item.coach_id">
        <SpaceItem :info="item" :bus-id="busId || ''" :invite-type="inviteType" />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts" name="SpaceList">
import http from '@/utils/request'
import SpaceItem from '@/pages/stadium/components/SpaceItem.vue'
import { useThemeStore } from '@/store/theme'
import { useMerchant } from '@/store/merchant'

const thmeStore = useThemeStore()
const useMerchantStore = useMerchant()
import { useUserStore } from '@/store/user'
const props = withDefaults(
  defineProps<{
    config?: Record<string, any>
    inviteType?: number
  }>(),
  {
    inviteType: 0,
  }
)

const userStore = useUserStore()
const privateList = ref<Record<string, any>[]>([])
const paging = ref()
const busId = props.config?.bus_id || userStore.userInfoBusId
const getInfo = async () => {
  if (privateList.value.length && paging.value) {
    paging.value.clear()
  }
  http
    .get('/Booking/getSpaceTypeList', {
      bus_id: busId,
      user_id: props.config?.bus_id
        ? await useMerchantStore.getUserIdByBus(props.config?.bus_id)
        : userStore.userInfoUserId,
      merchant_type_get: thmeStore.isShowMerchantMode,
      type: props.inviteType ? props.inviteType : '',
    })
    .then(async (res) => {
      if (!paging.value) {
        await nextTick()
      }
      paging.value.setLocalPaging(res.data.list)
    })
    .catch((res) => {
      paging.value && paging.value.setLocalPaging([], false)
    })
}

getInfo()
</script>
<style lang="scss">
.space-list {
  padding: 22rpx 30rpx;
  height: calc(100% - 90rpx);
  box-sizing: border-box;
  overflow-y: auto;
}
.full-screen {
  height: 100%;
}
</style>
