<template>
  <view class="course theme-bg">
    <view class="left" @tap="goCoachDetail(item)">
      <image class="light-shadow coach-avatar" :src="item.avatar" mode="aspectFill" />
      <view class="coach-info">
        <view class="name">{{ item.coach_name }}</view>
        <view class="coach-classes">
          <view v-for="(name, index) in item.permitted_class" :key="index" class="class">{{ name }}</view>
        </view>
      </view>
      <view v-if="arrIndexOf(config.show_content, '1')" class="surplus-num">余{{ item.surplus }}席</view>
    </view>
    <view class="reserve">
      <view v-if="item.surplus > 0" class="reserve-btn" data-info="{{item}}" @tap="goDetail(item)">预约</view>
      <view v-else class="reserve-btn disabled">已满</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="PtClassListItem">
import { arrIndexOf } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin'
import { goUrlPage } from '@/utils'
const { checkLogin } = useLogin()

const props = defineProps<{
  config?: Record<string, any>
  item: Record<string, any>
  day: string
  busId?: string
}>()
function goDetail(info) {
  checkLogin().then(() => {
    goUrlPage(`/pages/class/ptReserve?coach_id=${info.coach_id}&day=${props.day}&bus_id=${props.busId || ''}`)
  })
}
function goCoachDetail(info) {
  goUrlPage(`/pages/coach/detail?coach_id=${info.coach_id}&bus_id=${props.busId || ''}`)
}
</script>
<style lang="scss"></style>
