<template>
  <view
    :key="item.id"
    class="course theme-bg"
    :style="{
      backgroundImage: config.background_type == 2 ? `url(${config.background})` : '',
      backgroundSize: 'cover',
    }"
  >
    <view class="lef-tag"> 限班级成员参加 </view>
    <view class="left">
      <image class="coach-avatar" :src="item.coach_avatar" mode="aspectFill" />
      <view class="coach-info">
        <view class="name">
          {{ item.class_name }}
        </view>
        <view class="item coach">
          <text class="time-bg">{{ item.s_time }}-{{ item.e_time }}</text>
          <text v-if="arrIndexOf(config.show_content, '1')" class="coach-name">{{ item.coach_name }}</text>
        </view>
        <view class="item">
          <ThemeIcon v-if="arrIndexOf(config.show_content, '2')" class="icon-mr" type="t-icon-jiaoshi1" />
          <text v-if="arrIndexOf(config.show_content, '2')" class="room-name">{{ item.classroom_name || '无' }}</text>
          <text v-if="arrIndexOf(config.show_content, '2') && arrIndexOf(config.show_content, '3')" class="cut-up"
            >|</text
          >
          <text v-if="arrIndexOf(config.show_content, '3')"
            >共<text class="theme-color-other">{{ item.student }}</text
            >名学员</text
          >
        </view>
      </view>
    </view>
    <view class="theme-color-other"> {{ item.status }} </view>
  </view>
</template>

<script setup lang="ts" name="TeamClassListItem">
import { arrIndexOf } from '@/utils/shared'
const props = defineProps<{
  config: Record<string, any>
  item: Record<string, any>
  busId?: string
  day: string
}>()
</script>
<style lang="scss" scoped>
.room-name {
  max-width: 180rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
