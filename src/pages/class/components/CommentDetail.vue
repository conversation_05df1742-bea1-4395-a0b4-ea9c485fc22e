<template>
  <view>
    <view class="comment-wrap theme-bg" :class="{ 'is-comment': isComment }">
      <view v-if="!isComment" class="comment-tit"
        >历史评价<text>({{ config.comment_detail?.create_time_copy }})</text></view
      >
      <view v-for="(item, index) in info.set_type_json" :key="index">
        <view class="star-item">
          <view class="label">{{ item.value }}</view>
          <uni-rate
            v-model="info.set_type_json[index].star"
            :readonly="!isComment"
            :max="
              config.comment_detail?.base_max_score
                ? config.comment_detail.base_max_score
                : config.comment_setting?.[
                    isPt ? 'COACH_MAXIMUM_SCORE_VALUE' : 'GROUP_CLASS_MAXIMUM_SCORE_VALUE'
                  ]?.value
            "
          />
          <view class="score">{{ info.set_type_json[index].star }}分</view>
        </view>
      </view>
      <view class="tags">
        <view
          v-for="(item, index) in config.comment_setting?.[
            isPt ? 'COACH_APPRAISE_TAGS_LIST' : 'GROUP_CLASS_LABEL_SETTINGS_LIST'
          ]"
          :key="index"
          :class="['tag', selectedIds.indexOf(item.id) !== -1 ? 'checked' : '']"
          @tap="handleTag(item)"
          >{{ item.value }}</view
        >
      </view>
      <view v-if="!isComment" class="advice-readonly">
        <view class="comment-tit">意见反馈</view>
        <view class="comment-con">{{ info.advice }}</view>
        <view v-if="config.comment_detail?.reply" class="merchant-replay">
          <view class="comment-tit"
            >商家回复<text>({{ config.comment_detail.reply_time_copy }})</text></view
          >
          <view class="comment-con">{{ config.comment_detail.reply }}</view>
        </view>
      </view>
      <textarea
        v-else-if="
          config.comment_setting?.[isPt ? 'COACH_FEEDBACK_SWITCH' : 'GROUP_CLASS_FEEDBACK_SWITCH']
            ?.value === '1'
        "
        v-model="info.advice"
        class="text-advice"
        maxlength="500"
        placeholder="还想说点什么吗？"
      />
    </view>
    <view
      v-if="config.is_add_comment || config.comment_detail?.add_advice"
      class="comment-wrap theme-bg"
    >
      <view v-if="config.comment_detail.add_advice" class="advice-readonly">
        <view class="comment-tit"
          >追评结果 <text>({{ config.comment_detail.add_advice_time_copy }})</text></view
        >
        <view class="comment-con">{{ config.comment_detail.add_advice }}</view>
        <view v-if="config.comment_detail?.add_reply" class="merchant-replay">
          <view class="comment-tit"
            >商家回复<text>({{ config.comment_detail.add_reply_time_copy }})</text></view
          >
          <view class="comment-con">{{ config.comment_detail.add_reply }}</view>
        </view>
      </view>
      <template v-if="config.is_add_comment">
        <view class="comment-tit">填写追评</view>
        <textarea
          v-model="info.add_advice"
          class="text-advice"
          maxlength="500"
          placeholder="请在此处填写您的追评"
        />
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
// interface infoObj {
//   set_type_json: Array<any>
//   tag_json: Array<any>
//   advice: string
//   [propName: string]: any
// }
const selectedTags = ref([])
const props = defineProps({
  config: Object as any,
  isComment: {
    type: Boolean,
    default: true,
  },
  isPt: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: Object,
  },
})
const emit = defineEmits(['update:modelValue'])
const info = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})
function handleTag(item) {
  if (!props.isComment) {
    return
  }
  if (selectedTags.value.findIndex((label) => label.id == item.id) === -1) {
    selectedTags.value.push(item)
  } else {
    selectedTags.value.splice(
      selectedTags.value.findIndex((label) => label.id == item.id),
      1
    )
  }
  info.value.tag_json = selectedTags.value
}
const selectedIds = computed(() => {
  const tags = info.value.tag_json
  return tags.map((item) => item.id)
})
</script>
<style lang="scss" scoped>
.comment-wrap {
  font-size: 26rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  .star-item {
    display: flex;
    padding: 25rpx 0;
    align-items: center;
    border-bottom: 1rpx solid #f5f7f9;
    &:first-child {
      border-top: 1rpx solid #f5f7f9;
    }
  }
  .label {
    margin-right: 20rpx;
  }
  .score {
    font-weight: bold;
    font-size: 24rpx;
    margin-left: 15rpx;
  }
}
.tags {
  display: flex;
  flex-wrap: wrap;
  padding-top: 25rpx;
  border-bottom: 1rpx solid #f5f7f9;
  .tag {
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 50rpx;
    line-height: 50rpx;
    background: #ffffff;
    border: 1px solid var(--THEME-COLOR);
    border-radius: 4rpx;
    margin: 0 17rpx 25rpx 0;
  }
  .checked {
    background: var(--THEME-COLOR);
    font-weight: bold;
  }
}
.text-advice {
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx;
  height: 364rpx;
  border: 1px solid #e7e7e7;
  border-radius: 6rpx;
}
.comment-tit {
  font-weight: bold;
  line-height: 30rpx;
  padding: 25rpx 0;
  text {
    font-weight: normal;
  }
}
.comment-con {
  margin-bottom: 25rpx;
}
.merchant-replay {
  padding: 0 20rpx;
  overflow: hidden;
  background: #f6f6f8;
}
.is-comment {
  .star-item {
    &:first-child {
      border-top: none;
      padding-top: 15rpx;
    }
  }
}
</style>
