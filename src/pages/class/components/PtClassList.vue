<template>
  <view class="pt-list-wrap">
    <view class="date-switch theme-bg">
      <view
        v-for="item in dateList"
        :key="item.day"
        class="date"
        :class="today == item.date ? 'inset-shadow active' : ''"
        @tap="setDay(item, getInfo)"
      >
        <text class="week">{{ item.week }}</text>
        <text class="day">{{ item.day }}</text>
      </view>
    </view>
    <view class="private-list" :style="{ height: 'calc(100% - 166rpx)' }">
      <z-paging
        ref="paging"
        v-model="privateList"
        :show-loading-more-no-more-view="privateList.length > 10 ? true : false"
        :fixed="false"
        :refresher-enabled="false"
        :empty-view-img-style="{ width: '103rpx', height: '144rpx' }"
        empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png"
        :empty-view-text="`暂无相关${isSwim ? '泳教' : '私教'}数据`"
      >
        <view v-for="item in privateList" :key="item.coach_id">
          <PtClassListItem :bus-id="busId" :day="today" :config="config" :item="item" />
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts" name="PtClassList">
import http from '@/utils/request'
import { useNextDays } from '@/hooks/useNextDays'
import { useUserStore } from '@/store/user'
import PtClassListItem from './PtClassListItem.vue'
import { useThemeStore } from '@/store/theme'
import { useMerchant } from '@/store/merchant'

const thmeStore = useThemeStore()
const useMerchantStore = useMerchant()
const props = withDefaults(
  defineProps<{
    config?: Record<string, any>
    isSwim?: boolean
  }>(),
  {
    isSwim: false,
  }
)
const { today, dateList, setDay, setDateList } = useNextDays()
setDateList()

const userStore = useUserStore()
const privateList = ref<Record<string, any>[]>([])
const paging = ref()

const busId = props.config?.bus_id || userStore.userInfoBusId
const getInfo = async () => {
  const user_id = props.config?.bus_id
    ? await useMerchantStore.getUserIdByBus(props.config?.bus_id)
    : userStore.userInfoUserId
  if (privateList.value.length && paging.value) {
    paging.value.clear()
  }
  http
    .get('/Schedule/getPtScheduleList', {
      bus_id: busId,
      user_id,
      merchant_type_get: thmeStore.isShowMerchantMode,
      day: today.value,
      type: props.isSwim ? 1 : 0,
    })
    .then(async (res) => {
      if (!paging.value) {
        await nextTick()
      }
      paging.value.setLocalPaging(res.data)
    })
    .catch((res) => {
      paging.value && paging.value.setLocalPaging([], false)
    })
}

getInfo()
</script>
<style lang="scss"></style>
