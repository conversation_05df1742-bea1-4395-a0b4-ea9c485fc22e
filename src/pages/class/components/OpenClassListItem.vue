<template>
  <view
    :key="item.id"
    class="course theme-bg"
    :class="config.type == 2 ? 'style2' : ''"
    :style="{
      backgroundImage: config.background_type == 2 ? `url(${config.background})` : '',
      backgroundSize: 'cover',
    }"
    @tap="goDetail(item)"
  >
    <view v-if="item.seats_num" class="lef-tag right"> 座位 {{ item.seats_num }}</view>
    <view v-else-if="item.teamclass_type == 1" class="lef-tag right"> 限班级成员参加 </view>
    <view class="left">
      <image v-if="config.type == 2" class="coach-avatar" :src="item.avatar" mode="aspectFill" />
      <view v-if="config.type == 1" class="time">
        <text>{{ item.begin_time }}</text>
        <view class="time-line"></view>
        <text>{{ item.end_time }}</text>
      </view>
      <view class="coach-info">
        <view class="name">
          <image
            :src="`https://imagecdn.rocketbird.cn/minprogram/uni-member/${
              item.class_category == 1 ? 'cao' : 'tuan'
            }.png`"
            class="icon icon-mr icon-text"
          />
          <text>{{ item.class_name }}</text>
          <view v-if="item.class_level && arrIndexOf(config.show_content, '5')" class="star-wrap">
            <uni-rate class="rate" :readonly="true" :value="item.class_level" :max="item.class_level" :size="12" />
          </view>
        </view>
        <view v-if="arrIndexOf(config.show_content, '1')" class="item coach">
          <text v-if="config.type == 2" class="time-bg">{{ item.begin_time }}-{{ item.end_time }}</text>
          <ThemeIcon v-if="config.type == 1" class="icon-mr" type="t-icon-jiaolianrenwu" />
          <text class="coach-name">{{ item.coach_name }}</text>
        </view>
        <view class="item">
          <ThemeIcon
            v-if="item.classroom_name && arrIndexOf(config.show_content, '2')"
            class="icon-mr"
            type="t-icon-jiaoshi1"
          />
          <text v-if="item.classroom_name && arrIndexOf(config.show_content, '2')" class="room-name">{{
            item.classroom_name
          }}</text>
          <text
            v-if="
              item.classroom_name &&
              arrIndexOf(config.show_content, '2') &&
              ((item.class_category == 1 && item.min_number && arrIndexOf(config.show_content, '4')) ||
                (item.class_category == 0 && arrIndexOf(config.show_content, '3')))
            "
            class="cut-up"
            >|</text
          >
          <text v-if="item.class_category == 1 && item.min_number && arrIndexOf(config.show_content, '4')"
            >满<text class="theme-color-other">{{ item.min_number }}</text
            >人开课</text
          >
          <text v-if="item.class_category == 0 && arrIndexOf(config.show_content, '3') && config.remaining_seats === 1"
            >空余<text class="theme-color-other">{{ item.surplus }}</text>位</text>
          <text v-if="item.class_category == 0 && arrIndexOf(config.show_content, '3') && config.remaining_seats === 2"
            >当前预约 <text class="theme-color-other">{{item.total_num}}</text>/{{item.reserve_number}}</text>
        </view>
      </view>
    </view>
    <view v-if="item.class_category == 0" class="reserve">
      <view v-if="item.checkmark == 0 && item.can_mark_time" class="time-limit theme-color-other">
        {{ item.can_mark_time }}可约
      </view>
      <view v-if="item.checkmark == 0 && !item.can_mark_time" class="reserve-btn" @tap.stop="goReservePage(item, busId)"
        >预约</view
      >
      <view
        v-if="item.checkmark == 1"
        class="reserve-btn waiting-circle"
        @tap.stop="cancelReserve(item.mark_id, getInfo, busId)"
        >已约</view
      >
      <view v-if="item.checkmark == 2" class="reserve-btn disabled">已完成</view>
      <view v-if="item.checkmark == 3 && item.waitting_enable_status != 1" class="reserve-btn disabled">已满</view>
      <view v-if="item.checkmark == 5 || item.checkmark == 8 || item.checkmark == 9" class="reserve-btn disabled">{{
        item.checkmark == 8 ? '已开始' : item.checkmark == 9 ? '已结束' : '预约中'
      }}</view>
      <view v-if="item.checkmark == 10" class="reserve-btn circle" @tap.stop="goSubscribeNotice(item, getInfo)">
        <view>空位</view>
        <view>订阅</view>
      </view>
      <view v-if="item.checkmark == 11" class="reserve-btn waiting" @tap.stop="handleSubscribeNotice()">等位中</view>
    </view>
    <view v-else class="theme-color-other"> 无需预约 </view>
  </view>
</template>

<script setup lang="ts" name="OpenClassList">
import { useOpenClass } from '@/hooks/useOpenClass'
import { goUrlPage } from '@/utils'
import { arrIndexOf } from '@/utils/shared'

const props = defineProps<{
  config: Record<string, any>
  item: Record<string, any>
  busId: string
  day: string
}>()
const { goReservePage, cancelReserve, cancelWait, goSubscribeNotice } = useOpenClass()
const emits = defineEmits(['on-callback'])
function getInfo() {
  emits('on-callback')
}
function handleSubscribeNotice() {
  uni.showToast({
    title: '已订阅空位，请等待通知',
    icon: 'none',
  })
}
function goDetail(info) {
  const { course_schedule_id, coach_id, class_id } = info
  const url = `/pages/class/openClassDetail?id=${course_schedule_id}&coach_id=${coach_id}&class_id=${class_id}&day=${
    props.day
  }&bus_id=${props.busId || ''}`
  goUrlPage(url)
}
</script>
<style lang="scss" scoped>
.room-name {
  max-width: 180rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
