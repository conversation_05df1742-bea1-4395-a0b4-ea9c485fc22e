<template>
  <view>
    <view class="class-info theme-bg">
      <view class="left">
        <image class="class-img" :src="classInfo.thumb" mode="aspectFill" />
      </view>
      <view class="coach-info">
        <view class="name">{{ classInfo.class_name }}</view>
        <view class="item">
          <text class="coach-name">{{ classInfo.coach_name }}</text> <text class="cut-up">|</text>
          <text class="class-time"
            >时长<text class="theme-color-other">{{
              classInfo.class_duration || classInfo.class_hour
            }}</text
            >分钟</text
          >
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" /> {{ classInfo.date }}
          <text class="time">{{ classInfo.beg_time }}-{{ classInfo.end_time }}</text>
        </view>
        <view v-if="classInfo.classroom_name || classInfo.surplus" class="item">
          <ThemeIcon class="icon-mr" type="t-icon-jiaoshi1" />
          <text>{{ classInfo.classroom_name || '无' }}</text>
          <text v-if="arrIndexOf(themeConfig.show_content, '3')" class="cut-up">|</text>
          <text v-if="arrIndexOf(themeConfig.show_content, '3')" class="class-time">
            <text v-if="themeConfig.remaining_seats === 1">
              空余<text class="theme-color-other">{{ classInfo.surplus }}</text>位
            </text>
            <text v-if="themeConfig.remaining_seats === 2">
              当前预约 <text class="theme-color-other">{{classInfo.total_num}}</text>/{{classInfo.reserve_number}}
            </text>
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/store/theme'
import { arrIndexOf } from '@/utils/shared'

const themeStore = useThemeStore()

const props = defineProps({
  classInfo: Object,
})

const themeConfig =  ref<Record<string, any>>()
function getThemeConfig3() {
  themeStore.getConfig({ type: 3 }).then(() => {
    themeStore.theme3.map((item, index) => {
      if (item.temp_type === '1') {
        themeConfig.value = item
      }
    })
  })
}
getThemeConfig3()
</script>
<style lang="scss">
.class-info {
  display: flex;
  align-items: flex-start;
  padding: 40rpx 30rpx 10rpx;
  margin-bottom: 20rpx;
  .class-img {
    width: 161rpx;
    height: 94rpx;
    border-radius: 6rpx;
    margin-right: 34rpx;
  }
  .name {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 15rpx;
  }
  .item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    margin-bottom: 10rpx;
  }
  .coach-name {
    font-weight: bold;
  }
  .class-time {
    color: $theme-text-color-grey;
  }

  .time {
    margin-left: 10rpx;
    padding: 0 20rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    color: var(--THEME-COLOR);
    border-radius: 15rpx;
    font-weight: bold;
  }
}
</style>
