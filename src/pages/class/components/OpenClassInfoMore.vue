<template>
  <view class="class-info-more theme-bg">
    <image class="class-img" :src="classInfo.thumb" mode="aspectFill" />
    <view class="name">{{ classInfo.class_name }}</view>

    <view v-if="classInfo.class_waitting_id && (classInfo.status == 1 || classInfo.status == 2)" class="class-info-top">
      <view v-if="classInfo.status == 1"
        >您当前排在第<text class="theme-color-other">{{ classInfo.waitting_ranking || 0 }}</text
        >位</view
      >
      <view v-if="classInfo.status == 2">
        请在<CountDown
          v-if="classInfo.confirmed_end_time"
          :size="12"
          :time="classInfo.confirmed_end_time"
        />内确认候补，超时未确认则自动取消
      </view>
      <view class="theme-color-other">{{ classInfo.status == 1 ? '候补中' : '待确认' }}</view>
    </view>
    <view v-if="classInfo.begin_mark" class="class-info-top">
      {{ classInfo.begin_mark }}
    </view>
    <view v-if="onlyClassInfo" class="class-info">
      <view class="item">
        <view v-if="classInfo.class_level" class="rate-info">
          强度
          <uni-rate
            class="rate"
            :readonly="true"
            :value="classInfo.class_level"
            :max="classInfo.class_level"
            :size="12"
          />
          <text class="cut-up">|</text>
        </view>
        <text class="class-time"
          >时长<text class="theme-color-other">{{ classInfo.class_hour }}</text
          >分钟</text
        >
      </view>
      <view class="rig">今日暂无排课信息</view>
    </view>
    <view v-else class="class-info">
      <view v-if="classInfo.status == 1 && !classInfo.class_waitting_id" class="status-text theme-text"> 已约 </view>
      <view class="left">
        <image class="coach-img" :src="classInfo.coach?.avatar" mode="aspectFill" />
        <view>
          <view class="coach-name">{{ classInfo.coach?.coach_name }}</view>
          <view class="coach-pos">{{ classInfo.coach?.position?.join('、') }}</view>
        </view>
      </view>
      <view class="rig">
        <view class="item">
          <view v-if="classInfo.class_level" class="rate-info">
            强度
            <uni-rate
              class="rate"
              :readonly="true"
              :value="classInfo.class_level"
              :max="classInfo.class_level"
              :size="12"
            />
            <text class="cut-up">|</text>
          </view>
          <text class="class-time"
            >时长<text class="theme-color-other">{{ classInfo.class_hour }}</text
            >分钟</text>
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" />
          {{ classInfo.date_time }}
          <text class="time">{{ classInfo.beg_time }}-{{ classInfo.end_time }}</text>
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-jiaoshi1" />
          <text>{{ classInfo.classroom_name || '无' }}</text>
          <text class="class-time" v-if="arrIndexOf(themeConfig.show_content, '3')">
            <text class="cut-up">|</text>
            <text v-if="themeConfig.remaining_seats === 1">
              空余<text class="theme-color-other">{{ classInfo.surplus }}</text>位
            </text>
            <text v-if="themeConfig.remaining_seats === 2">
              当前预约 <text class="theme-color-other">{{classInfo.total_num}}</text>/{{classInfo.reserve_number}}
            </text>
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CountDown from '@/components/CountDown.vue'
import { useThemeStore } from '@/store/theme'
import { arrIndexOf } from '@/utils/shared'

const themeStore = useThemeStore()

const props = defineProps<{
  classInfo: Record<string, any>
  onlyClassInfo: boolean
}>()

const themeConfig =  ref<Record<string, any>>({})
function getThemeConfig3() {
  themeStore.getConfig({ type: 3 }).then(() => {
    themeStore.theme3.map((item, index) => {
      if (item.temp_type === '1') {
        themeConfig.value = item
      }
    })
  })
}
getThemeConfig3()
</script>
<style lang="scss" scoped>
.class-info-top {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: $theme-text-color-grey;
  margin-bottom: 10rpx;
}
.class-info-more {
  padding: 30rpx 20rpx;
  .class-img {
    width: 691rpx;
    height: 400rpx;
    border-radius: 6rpx;
    margin-bottom: 30rpx;
  }
  .name {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
  }
}
.class-info {
  position: relative;
  overflow: hidden;
  padding: 28rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid var(--THEME-COLOR);
  border-radius: 20rpx;
  .left {
    display: flex;
    align-items: center;
  }
  .coach-img {
    width: 120rpx;
    height: 120rpx;
    margin-right: 20rpx;
  }
  .item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    margin-bottom: 10rpx;
  }
  .rate-info {
    display: flex;
    align-items: center;
    margin-right: 10rpx;
  }
  .rate {
    margin: 0 10rpx;
  }
  .coach-name,
  .coach-pos {
    max-width: 120rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .coach-name {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 15rpx;
  }
  .coach-pos {
    color: $theme-text-color-grey;
  }
  .class-time {
    color: $theme-text-color-grey;
  }

  .time {
    margin-left: 10rpx;
    padding: 0 20rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    color: var(--THEME-COLOR);
    border-radius: 15rpx;
    font-weight: bold;
  }
}
</style>
