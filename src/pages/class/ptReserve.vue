<template>
  <view class="reserve-box theme-bg">
    <view class="coach">
      <image class="avatar" :src="privateInfo.avatar" />
      <view class="name">{{ privateInfo.coach_name }}</view>
      <view class="des">{{ privateInfo.position }}</view>
    </view>
    <view>
      <view class="course">
        <uni-segmented-control 
          :current="current" 
          :values="items" 
          style-type="button" 
          active-color="var(--THEME-COLOR)" 
          @clickItem="onClickItem" />
      </view>
      <view class="course">
        <view class="title"> 预约课程 </view>
        <view class="private-card">
          <view
            v-for="item in privateInfo.class_list"
            v-if="privateInfo.class_list && privateInfo.class_list.length"
            :key="item.card_user_id"
            class="private-card-item"
            :class="current === 0
              ? curClass.card_user_id == item.card_user_id ? 'selected' : ''
              : curClass.card_id == item.card_id ? 'selected' : ''"
            @tap="selectClass(item)"
          >
            <view class="top">{{ item.name }}</view>
            <view class="des">
              <view class="tit">时长</view>
              <view class="con">{{ item.class_duration }}分钟</view>
            </view>
            <view class="des">
              <view class="tit">方式</view>
              <view class="con">1对{{ item.user_no }}</view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="hasError" class="nodata">{{ current == 0 ? hasErrorMsg : hasOneTimePayErrorMsg }}</view>
      <view v-if="!hasError" :class="current === 0 ? 'time-wrap' : 'course'">
        <view class="title">
          上课时间
          <view on-tap="datePicker" class="date-wrap">
            <picker mode="date" :value="day" :start="startDate" @change="bindDateChange">
              <view class="uni-input">
                {{ day }}
                <ThemeIcon class="icon-mr" type="t-icon-shijian" />
              </view>
            </picker>
          </view>
        </view>
        <view class="time-tags">
          <view
            v-for="item in times"
            :key="item.value"
            class="tag"
            :class="{ active: curTime === item.time, disabled: item.check === 1 }"
            @tap="selectTime(item)"
          >
            <view>{{ item.time }}</view>
            <view v-if="item.all_num > 1" class="min">已约{{ item.class_mark_num }}/{{ item.all_num }}</view>
          </view>
        </view>
      </view>
      <template v-if="current === 1 && !hasError">
        <view class="course">
          <view class="line-item">
            <view class="label">标准价格</view>
            <view class="value">{{ price?.original_amount }}</view>
          </view>
          <view class="line-item" v-if="price?.card_name">
            <view class="label">持卡优惠</view>
            <view class="value">
              <text class="type">{{ price?.card_name }}</text>
              <text class="amount">{{ price?.discount_amount }}</text>
            </view>
          </view>
        </view>
        <PayTypeSelect
          :pay-type-list="payMethodList"
          :pay-type-index="payMethodIdx"
          :card-list="cardList"
          :card-index="cardIdx"
          :value-card-text="`(余额${selectedCard?.balance}${ needPrice > 0 ? '，还需' + needPrice.toFixed(2) : ''})`"
          @change-type="handlePayMethodChange"
          @change-card="handleCardChange"
        />
        <view class="time-wrap" style="padding-top: 0">
          <view class="title" style="padding-top: 0; margin: 0"> 退款规则 </view>
          <view class="tip">
            <rich-text :nodes="tagFirst" type="text"></rich-text>
          </view>
          <view class="tip" v-for="(item, index) of tagList" :key="index">
            <rich-text :nodes="item" type="text"></rich-text>
          </view>
        </view>
      </template>
    </view>
    <view v-if="!hasError && current == 0" class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="addReserve">提交</button>
    </view>
    <view v-if="!hasError && current == 1" class="fixed-bottom-wrap theme-bg">
      <view class="price">
        <text style="font-size: 28rpx">¥{{ killBill }}</text>
        <text v-if="killValueCard" style="margin-left: 20rpx">储值卡已抵扣 {{ killValueCard }} 元</text>
      </view>
      <button class="normal-btn" style="margin-left: 20rpx;width: 300rpx" @tap="addReserve">立即支付</button>
    </view>
    <view>
    </view>
  </view>
</template>

<script setup lang="ts" name="ptReserve">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import { onLoad } from '@dcloudio/uni-app'
import { formatDate } from '@/utils/shared'
import PayTypeSelect from '@/components/PayTypeSelect.vue'

const { checkLogin } = useLogin()
const coach_id = ref('')
const day = ref('')
const hasError = ref(false)
const hasErrorMsg = ref('您暂无对应课程，无法预约')
const hasOneTimePayErrorMsg = ref('此教练不支持单节付费课方案, 无法预约!')
const startDate = formatDate(new Date(), 'yyyy-MM-dd')
onLoad((options) => {
  coach_id.value = options.coach_id
  day.value = options.day || formatDate(new Date(), 'yyyy-MM-dd')
})

onShow(() => {
  checkLogin(false).then(() => {
    getInfo()
  })
})

const userStore = useUserStore()
const privateInfo = reactive({
  avatar: '',
  coach_name: '',
  position: '',
  class_list: [] as any[],
})
const curClass = ref<any>('')
const curTime = ref('')
const curTimeInfo = ref<Record<string, string | number>>({})
const getInfo = () => {
  http
    .get('/Schedule/getPtScheduleDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      coach_id: coach_id.value,
      day: day.value,
      appt_type: current.value,
    })
    .then((res) => {
      const resInfo = res.data.info
      Object.assign(privateInfo, resInfo)
      if (resInfo.class_list?.length) {
        curClass.value = resInfo.class_list[0]
        hasError.value = false
      } else {
        hasError.value = true
        curTime.value = ''
        curTimeInfo.value = {}
      }
      if (curClass.value) {
        getPtScheduleTime()

        if (current.value === 1) {
          getPayInfo()
        }
      }

      if (current.value === 1) {
        const refundLadderList = resInfo.cancel_pt_refund_ladder;

        if (Array.isArray(refundLadderList) && refundLadderList.length) {
          tagFirst.value = `<span>开课前 <strong style="color: #FF2351;font-size: 28rpx">${refundLadderList[0].start}</strong> 分钟内不允许取消</span>`;

          tagList.value = [];
          refundLadderList.forEach((item: any) => {
            tagList.value.push(
              `<span>开课前 <strong style="color: #FF2351;font-size: 28rpx">${item.start}</strong> 分钟 ~ ${!item.end ? '<strong style="color: #FF2351;font-size: 28rpx">无限制</strong>' : `<strong style="color: #FF2351;font-size: 28rpx">${item.end}</strong> 分钟`}，退款 <strong style="color: #FF2351;font-size: 28rpx">${(item.rate * 100).toFixed(2)}%</strong></span>`
            );
          });
        }
      }
    })
    .catch((err) => {
      hasError.value = true
      hasErrorMsg.value = err.errormsg
      hasOneTimePayErrorMsg.value = err.errormsg
    })
}
const times = ref<Record<string, any>[]>([])
const getPtScheduleTime = () => {
  http
    .get('/Schedule/getPtScheduleTime', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      coach_id: coach_id.value,
      day: day.value,
      card_id: curClass.value.card_id,
      appt_type: current.value,
    })
    .then((res) => {
      times.value = res.data.list
      curTime.value = ''
      curTimeInfo.value = {}
    })
}
const selectClass = (info) => {
  curClass.value = info
  getPtScheduleTime()

  if (current.value === 1) {
    getPayInfo()
  }
}
const selectTime = (info) => {
  if (info.check !== 1) {
    if (info.all_num > 1 && info.class_mark_num >= 1) {
      uni.showModal({
        title: '提示',
        content: `本节课为 1对${info.all_num}课程， 时长${info.class_duration}分钟,是否确认参加?`,
        success: function (res) {
          if (res.confirm) {
            curTimeInfo.value = info
            curTime.value = info.time
          } else if (res.cancel) {
            curTimeInfo.value = {}
            curTime.value = ''
          }
        },
      })
    } else {
      curTimeInfo.value = info
      curTime.value = info.time
    }
  }
}
function addReserve() {
  if (current.value === 0) {
    http
      .post('/Schedule/addPtSchedule', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        beg_date: `${day.value} ${curTime.value}`,
        pid: curTimeInfo.value.pid_id || '',
        coach_id: coach_id.value,
        day: day.value,
        card_id: curClass.value.card_id,
        card_user_id: curClass.value.card_user_id,
        loading: true,
      })
      .then((res) => {
        uni.showToast({
          icon: 'success',
          title: '预约成功',
        })
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/my/reserveRecord?type=2',
          })
        }, 1000)
      })
  } else {
    const payMethod = payMethodList.value[payMethodIdx.value]

    if (!curTime.value) {
      uni.showToast({
        icon: 'none',
        title: '请选择上课时间',
      })
      return
    }

    if (!pay) {
      uni.showToast({
        icon: 'none',
        title: '请选择支付方式',
      })
      return
    }

    if (cardIdx.value === -1 && payMethod.pay_type === 8) {
      uni.showToast({
        icon: 'none',
        title: '请选择储值卡',
      })
      return
    }

    http.post('/Schedule/addPtSchedulePayment', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      coach_id: coach_id.value,
      card_id: curClass.value.card_id,
      beg_date: `${day.value} ${curTime.value}`,
      pt_charge_plan_detail_id: price.value.pt_charge_plan_detail_id,
      amount: killBill.value,
      pay_type: payMethod.pay_type,
      balance: selectedCard.value?.balance || '',
      card_user_id: selectedCard.value?.card_user_id || '',
      pid: curTimeInfo.value.pid_id || '',
      day: day.value,
      loading: true,
    }).then((res) => {
      if (res.data.info) {
        pay(res.data.info)
      } else {
        uni.showToast({
          icon: 'success',
          title: '预约成功',
        })
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/my/reserveRecord?type=2',
          })
        }, 1000)
      }
    })
  }
}
function pay(info) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'alipay',
    orderInfo: info.orderInfo || '',
    success: () => {
      uni.showToast({
        icon: 'success',
        title: '预约成功',
      })
      setTimeout(() => {
        uni.redirectTo({
          url: '/pages/my/reserveRecord?type=2',
        })
      }, 1000)
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      uni.hideLoading()
      // 如果是储值卡组合支付,并且有余额，立即返还扣除的储值卡余额，否则会5分钟后自动返还
      const payTypeState = payMethodList.value[payMethodIdx.value]
      const card = cardList.value[cardIdx.value]
      // if (payTypeState.pay_type === 8 && +payTypeState.balance !== 0) {
        const params = {
          bus_id: userStore.userInfoBusId,
          user_id: userStore.userInfoUserId,
          card_user_id: card?.card_user_id || '0',
          business_id: info.order_sn, // 业务id
          business_type: '8', // 业务类型，1--散场票，2--订场，3--购卡
        }
        http.post('Booking/storedCardChangeRevoke', params).then((res) => {
          if (res.errorcode != 0) {
            uni.showToast({ title: res.data.errormsg, icon: 'none' })
          }
        })
      // }
    },
  })
}
function bindDateChange(e) {
  day.value = e.detail.value
  getPtScheduleTime()
}

// one time pay
const current = ref(0);
const items = ref(['用卡预约', '直接付费预约']);
const onClickItem = (e) => {
  if (current.value !== e.currentIndex) {
    current.value = e.currentIndex
    getInfo()
  }
}

const tagFirst = ref('');
const tagList = ref<string[]>([]);


const price = ref()
const payMethodList = ref([])
const payMethodIdx = ref('0')
const cardList = ref<any[]>([])
const cardIdx = ref(-1)

const selectedCard: any = computed(() => {
  return cardList.value[cardIdx.value]
})
const needPrice = computed(() => {
  const total = Number (selectedCard.value ? selectedCard.value.balance : 0)
  const pay = Number(price.value?.pay_amount)
  return pay - total
})
const killBill = computed(() => {
  const bill = Number(price.value?.pay_amount || 0)
  const cardAmount = Number(selectedCard.value?.balance)
  
  if (payMethodIdx.value === '0') {
    return bill.toFixed(2)
  } else {
    if (bill > cardAmount) {
      return (bill - cardAmount).toFixed(2)
    } else {
      return 0
    }
  }
})
const killValueCard = computed(() => {
  if (payMethodIdx.value === '0') {
    return 0
  } else {
    const bill = Number(price.value?.pay_amount)
    const cardAmount = Number(selectedCard.value?.balance)

    if (bill > cardAmount) {
      return cardAmount.toFixed(2)
    } else {
      return bill.toFixed(2)
    }
  }
})

const getPayInfo = (hasValueCard = false, card_user_id = '', stored_card_id = '') => {
  http.post('/Schedule/getPtSchedulePayAmount', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    card_id: curClass.value.card_id,
    sort: curClass.value.sort,
    pt_charge_plan_id: curClass.value.pt_charge_plan_id,
    is_stored: hasValueCard ? 1 : 0,
    card_user_id,
    stored_card_id,
  }).then((res) => {
    price.value = res.data

    payMethodList.value = res.data.pay_type_arr
    cardList.value = res.data.card_list

    if (hasValueCard) {
      if (!card_user_id && !stored_card_id) {
        payMethodIdx.value = String(payMethodList.value.findIndex((item: any) => item.pay_type === 8))
        cardIdx.value = cardList.value.findIndex((item: any) => item.checked)
      }
    } else {
      payMethodIdx.value = '0'
      cardIdx.value = -1
    }
  })
}

function handlePayMethodChange(data) {
  payMethodIdx.value = data.index

  getPayInfo(data.payType === 8)
}

function handleCardChange(data) {
  cardIdx.value = data.index
  if (data.choseCard) {
    getPayInfo(true, data.choseCard.card_user_id, data.choseCard.card_id)
  }
}
</script>

<style lang="scss">
.price {
  font-size: 24rpx;
  font-weight: bold;
  color: orange;
}
.tip {
  font-size: 24rpx;
  line-height: 50rpx;
  color: $theme-text-color-grey;
}
.reserve-box {
  width: 100%;
  min-height: 100%;
}
.coach {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-top: 50rpx;
}
.coach .avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  box-shadow: 0 3rpx 8rpx 0 rgba(65, 92, 145, 0.15);
}
.coach .name {
  margin-top: 20rpx;
  font-size: 30rpx;
}
.coach .des {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: $theme-text-color-grey;
}
.course {
  padding: 30rpx 50rpx;

  .line-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60rpx;

    .label {
      font-size: 26rpx;
      color: $theme-text-color-grey;
    }

    .value {
      font-size: 30rpx;
      color: #1b1b1b;

      .type {
        font-size: 24rpx;
        font-weight: bold;
        color: #dddddd;
        margin-right: 10rpx;
        border: 1rpx solid #eeeeee;
        padding: 0 10rpx;
        border-radius: 8rpx;
      }

      .amount {
        color: $theme-text-color-other;
      }
    }
  }
}
.time-wrap {
  padding: 30rpx 50rpx 200rpx;
}
.course .title,
.time-wrap .title {
  font-size: 26rpx;
  font-weight: bold;
  line-height: 80rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.private-card {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.private-card-item {
  width: 32%;
  padding: 16rpx;
  line-height: 1.75;
  border-radius: 8rpx;
  box-sizing: border-box;
  margin-right: 14rpx;
  overflow: hidden;
  font-size: 26rpx;
  margin-right: 2%;
  margin-bottom: 20rpx;
  border: 2rpx solid #e8e8e8;
  &:nth-child(3n) {
    margin-right: 0;
  }
  &.selected {
    border: 2rpx solid var(--THEME-COLOR);
  }
  .top {
    font-size: 30rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .des {
    font-size: 24rpx;
    color: #7d7d7d;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.duration {
  margin-top: 50rpx;
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  background: #f6f6f8;
}
[data-theme='dark'] {
  .duration {
    background: #0f0f0f;
  }
}
.duration .tit {
  padding-left: 20rpx;
  position: relative;
}
.duration .tit::before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background: var(--THEME-COLOR);
  border-radius: 50%;
}
.duration .time {
  font-weight: bold;
  margin-left: 20rpx;
}

.time-tags {
  display: flex;
  flex-wrap: wrap;
  .tag {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 4rpx;
    font-size: 24rpx;
    line-height: 1.3;
    color: #434343;
    height: 60rpx;
    margin-right: 16rpx;
    margin-bottom: 20rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    min-width: calc((100% - 64rpx) / 5);
    border: 1px solid #e8e8e8;
    border-radius: 6rpx;
  }
  .min {
    font-size: 20rpx;
  }
  .tag:nth-child(5n) {
    margin-right: 0;
  }
  .active {
    border: 0 none;
    font-weight: bold;
    background-color: var(--THEME-COLOR);
  }
  .disabled {
    background-color: #e7e7e7;
    color: #898989;
  }
}
.date-wrap {
  display: flex;
  align-items: center;
  font-weight: normal;
  font-size: 24rpx;
}
.date-wrap .img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
</style>
