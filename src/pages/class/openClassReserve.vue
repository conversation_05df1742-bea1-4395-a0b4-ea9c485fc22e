<template>
  <view v-if="!showErrMsg" class="footer-hasfixed">
    <OpenClassInfo :class-info="reserveInfo" />
    <view class="form-items theme-bg">
      <view class="item">
        <text class="key">预约用卡</text>
        <view class="value rig-sel org-text" @tap="goToChargeCardPage">{{ selectedCard.card_name }}</view>
      </view>
      <view class="item">
        <text class="key">单价</text>
        <tex class="value">{{ priceDes }}</tex>
      </view>
      <view class="item">
        <text class="key">预约人数</text>
        <view class="value reserve-number">
          <uni-number-box v-model="formData.num" :min="1" :max="maxReserveNum"></uni-number-box>
        </view>
      </view>
      <view v-if="payDataInfo.isUseValueCard" class="item recharge-item">
        <view class="key">
          <image
            class="vip-icon"
            src="https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/vip.png"
            mode="widthFix"
          />
          <text class="card-name">{{ selectedCard.card_name }}</text>
        </view>
        <view class="value">
          <text class="card-balance">{{ payDataInfo.valueText }}</text>
          <text v-if="selectedCard.self_recharge == '1'" class="recharge-link" @click.stop="handleToRecharge"
            >去充值</text
          >
        </view>
      </view>
      <view v-if="payDataInfo.isUseValueCard" class="item sum-item">
        <view class="key"></view>
        <view class="value">
          总共&nbsp;&nbsp;
          <text class="theme-color-other">¥</text>
          <text class="total theme-color-other">{{ payDataInfo.total }}</text>
        </view>
      </view>
      <!-- 团课折扣券 ，仅支持 非会员价格 和 预约用卡 使用微信支付时使用，及时使用储值卡预约余额不足时 用微信进行补余也不能用折扣券 -->
      <CouponSelect
        v-if="selectedCard.pay_type === 2 && payDataInfo.total"
        :id="loadOptions.class_id"
        :amount="payDataInfo.total"
        :not-show-bill="true"
        :use-limit="6"
        @on-change="changeCouponInfo"
      />
    </view>
    <RoomSeats
      v-if="reserveInfo.seat_list && reserveInfo.seat_list.length"
      :reserve-num="formData.num"
      :can-select="true"
      :seats-list="reserveInfo.seat_list"
      @on-select="setRoom"
    />
    <label v-if="reserveInfo.is_open_reservation_protocol" class="fixed-protocol-col">
      <checkbox-group class="checkbox-group" @change="checkboxChange">
        <checkbox value="1" :checked="formData.is_protocol" />
        我已阅读并同意<navigator url="/pages/class/protocol" hover-class="none">《团操课预约协议》</navigator>
      </checkbox-group>
    </label>
    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <view v-if="payDataInfo.isUseValueCard || selectedCard.pay_type === 2" class="lef-flex mgr">
          <text class="bill"> <text class="unit">￥</text>{{ payDataInfo.bill }} </text>
          <text v-if="payDataInfo.usageText" class="usage-text">{{ payDataInfo.usageText }}</text>
        </view>
        <button
          class="normal-btn"
          :class="{ custom: payDataInfo.isUseValueCard || selectedCard.pay_type === 2 }"
          type="primary"
          :disabled="showInvitationModal"
          @tap="addReserveThrottle"
        >
          {{ showInvitationModal ? '已约' : '立即预约' }}
        </button>
      </view>
    </view>
    <!-- 推荐邀请弹窗 -->
    <InvitationModal v-model="invitationActivityId" title="您已成功预约" :type="1" :show="showInvitationModal" />
  </view>
  <view v-else class="nodata">
    {{ showErrMsg }}
  </view>
</template>

<script setup lang="ts" name="class">
import http from '@/utils/request'
import CouponSelect from '@/components/CouponSelect.vue'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
import OpenClassInfo from './components/OpenClassInfo'
import RoomSeats from './components/RoomSeats.vue'

import InvitationModal from '@/components/InvitationModal'
import { goUrlPage } from '@/utils/urlMap'
import lodash from 'lodash'


const userStore = useUserStore()
const { checkLogin } = useLogin()
const formData = reactive({
  course_schedule_id: '',
  pay_type: 1, //  1微信 8储值卡(可能是混合支付、也可能只有储值卡支付) 期限卡、次卡等 -1
  balance: '0', // 使用储值卡时，储值卡的余额
  card_user_id: '',
  num: 1,
  is_protocol: true,
  seats: '',
  todeduct: '', // 单价
  coupon_receive_id: '', // 折扣券id
  coupon_amount: '', // 折扣券金额
  original_price: '', // 折前原价/无折扣金额 --- 有折扣券才传值
  amount: '', // 实付金额
})

const invitationActivityId = ref('')
const classCardId = ref('')
const showInvitationModal = ref(false)
const orderInfo = ref(null)
const showErrMsg = ref('')
const timeer = ref('')
const reserveInfo = reactive({
  class_name: '',
})
const selectedCard = reactive({
  card_user_id: '',
  card_id: '',
  pay_type: 1, // 1卡内余额支付 2微信支付
  todeduct: '',
  card_name: '',
  card_type_id: null,
})
function goToChargeCardPage() {
  uni.navigateTo({
    url: '/pages/class/chargeCardSelect',
    events: {
      // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
      'confirm-select': (info) => {
        cardChanged(info.selectedCard)
      },
    },
    success(res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('init-charge-list', {
        reserveInfo: reserveInfo,
        selectedCard: selectedCard,
      })
    },
  })
}
const couponInfo = ref({
  coupon_receive_id: '',
  coupon_amount: '',
  bill: 0,
})
function changeCouponInfo(info) {
  couponInfo.value = info
}
// 支付相关金额
const payDataInfo = computed(() => {
  if (selectedCard.pay_type === 1 && selectedCard.card_type_id !== 3) {
    return {
      isUseValueCard: false, // 是否储值卡支付
      total: 0, // 总售价/原价
      usage: 0, // 储值卡抵扣金额
      bill: 0, // 实付金额  储值卡与折扣券抵扣后售价，大于0则代表储值卡余额不足
      valueText: '', // 余额文本
      usageText: '', // 抵扣文本
    }
  }
  const isUseValueCard = selectedCard.pay_type === 1 && selectedCard.card_type_id === 3
  const total = +(formData.num * selectedCard.todeduct).toFixed(2)
  const afterCoupon = couponInfo.value.coupon_receive_id ? +couponInfo.value.bill : total // 折扣券后售价
  let usage = 0
  if (isUseValueCard) {
    usage = selectedCard.balance >= total ? total : +selectedCard.balance
  }
  const bill = +(afterCoupon - usage).toFixed(2)
  const valueText = `(余额${selectedCard.balance}${bill > 0 ? '，还需' + bill : ''})`
  let usageText = ''
  if ((couponInfo.value && couponInfo.value.coupon_amount) || (isUseValueCard && usage > 0)) {
    usageText = couponInfo.value.coupon_amount ? `折扣券已抵${couponInfo.value.coupon_amount}` : `储值卡已抵${usage}元`
  }
  return {
    isUseValueCard,
    total,
    usage,
    bill,
    valueText,
    usageText,
  }
})
const loadOptions = ref()
onLoad((options) => {
  formData.course_schedule_id = options.id || ''
  loadOptions.value = options
})

onShow(() => {
  if (!reserveInfo.class_name) {
    checkLogin().then(() => {
      getInfo()
    })
  }
})
onShareAppMessage((options) => {
  // 新用户在注册页面注册时需要传activity_id和inviter_user_id
  const obj = {
    path: `/pages/class/openClassDetail?bus_id=${userStore.userInfoBusId}&id=${formData.course_schedule_id}&coach_id=${loadOptions.value.coach_id}&class_id=${loadOptions.value.class_id}`,
    title: '',
    imageUrl: '',
  }
  if (invitationActivityId.value && userStore.userInfoUserId) {
    obj.title = '健身组队！快乐加倍！～'
    obj.imageUrl = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/share.jpg'
    obj.path =
      obj.path + `&activity_id=${invitationActivityId.value}&inviter_user_id=${userStore.userInfoUserId}&from=share`
  }
  return obj
})

const maxReserveNum = computed(() => {
  const maxReserve = selectedCard.maxresv_num || Infinity
  return Math.min(Math.max(+reserveInfo.surplus, 1), maxReserve)
})
function setRoom(ids) {
  formData.seats = ids
}
function getPriceDes(info) {
  // pay_type 1卡内余额支付 2微信支付
  const { card_type_id, pay_type, todeduct } = info
  if (pay_type === 1 && card_type_id === 1) {
    return '免费'
  }
  const typeName = card_type_id === 2 ? '次' : card_type_id === 3 ? '元' : '节'
  return `${pay_type === 1 ? '' : '支付 '}${todeduct}${pay_type === 1 ? typeName : '元'}/人`
}
const priceDes = ref('')
function cardChanged(info) {
  formData.num = 1
  Object.assign(selectedCard, { ...info, maxresv_num: info.maxresv_num || Infinity })
  formData.card_user_id = selectedCard.card_user_id
  formData.todeduct = selectedCard.todeduct || ''
  formData.pay_type =
    selectedCard.card_type_id === 3 ? (selectedCard.pay_type === 2 ? 1 : 8) : selectedCard.pay_type === 2 ? 1 : -1
  formData.balance = selectedCard.balance || ''
  priceDes.value = getPriceDes(info)
  if (selectedCard.pay_type !== 2) {
    couponInfo.value = {
      coupon_receive_id: '',
      coupon_amount: '',
      bill: 0,
    }
  }
}
const getInfo = () => {
  http
    .get('/Schedule/getCourseScheduleDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      course_schedule_id: formData.course_schedule_id,
    })
    .then((res) => {
      Object.assign(reserveInfo, res.data)
      if (res.data?.cards.length) {
        const card = res.data?.cards.find((v) => v.card_user_id === selectedCard.card_user_id)
        if (card) {
          cardChanged(card)
        } else {
          cardChanged(res.data.cards[0])
        }
      } else if (reserveInfo.mark_object === 1) {
        cardChanged({
          card_user_id: '',
          card_id: '',
          pay_type: 2,
          maxresv_num: Infinity,
          todeduct: reserveInfo.class_price,
          card_name: '无/不使用会员卡',
        })
      }
    })
    .catch((err) => {
      showErrMsg.value = err.errormsg || '出错了'
      setTimeout(() => {
        goList()
      }, 2000)
    })
}

// 储值卡去充值
function handleToRecharge() {
  uni.navigateTo({
    url: '/packageMy/recharge/detail?card_user_id=' + selectedCard.card_user_id,
    events: {
      'refresh-page': () => {
        getInfo()
      },
    },
  })
}

const addReserveThrottle = lodash.throttle(() => {
  addReserve()
}, 2000)

function addReserve() {
  if (!formData.is_protocol) {
    uni.showToast({ title: '请勾选《协议》', icon: 'none' })
    return
  }
  if (selectedCard.pay_type === 1) {
    // 用卡预约
    cardReserve()
  } else {
    // 微信支付
    singlePurchase()
  }
}
function cardReserve() {
  // 卡预约不能使用折扣券
  const params = {
    ...formData,
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    amount: payDataInfo.value.bill,
    original_price: '',
    coupon_receive_id: '',
    coupon_amount: '',
  }
  http.post('Classmark/addCourseMark', params).then((res) => {
    if (res.data?.appId) {
      const order_sn = res.data.order_sn
      pay(res.data, order_sn)
    } else {
      cardReserveSuccess()
    }
  })
}
function goList() {
  goUrlPage(`/pages/class/class?type=1&bus_id=${userStore.userInfoBusId}`)
}

function cardReserveSuccess() {
  if (invitationActivityId.value) {
    showInvitationModal.value = true
  } else {
    uni.showToast({
      icon: 'success',
      title: '预约成功',
    })
    setTimeout(() => {
      goList()
    }, 1000)
  }
}
function cancelWxPay(params) {
  http.post('Booking/storedCardChangeRevoke', params).then((res) => {
    if (res.errorcode !== 0) {
      uni.showToast({ title: res.data.errormsg, icon: 'none' })
    }
  })
}
function pay(info: UniApp.RequestPaymentOptions, order_sn) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success: cardReserveSuccess,
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      // 如果是储值卡组合支付，返还扣除的储值卡余额
      if (selectedCard.card_type_id === 3 && payDataInfo.value.bill > 0) {
        const params = {
          bus_id: userStore.userInfoBusId,
          user_id: userStore.userInfoUserId,
          card_user_id: formData.card_user_id,
          business_id: order_sn, // 业务id，团课传order_sn
          business_type: '7', // 业务类型，7--团课，1--散场票，2--订场，3--购卡
        }
        cancelWxPay(params)
      }
    },
  })
}

// 临时购课
function singlePurchase() {
  http
    .post('Classmark/addCourseMark', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      ...formData,
      amount: payDataInfo.value.bill,
      original_price: couponInfo.value.coupon_amount ? payDataInfo.value.total : '',
      coupon_receive_id: couponInfo.value.coupon_receive_id,
      coupon_amount: couponInfo.value.coupon_amount,
      single_purchase: 1,
    })
    .then((res) => {
      orderInfo.value = res.data
      // 没有order_sn代表不需要再拉起支付 如支付金额为0
      if (!res.data.order_sn) {
        uni.showToast({ title: res.errormsg, image: '/static/img/success.png' })
        setTimeout(() => {
          goList()
        }, 1000)
        return
      }
      uni.requestPayment({
        ...orderInfo.value,
        success(res) {
          if (res.errMsg == 'requestPayment:ok') {
            getPayStatus()
          }
        },
        fail(err) {
          cancelWxPay({
            bus_id: userStore.userInfoBusId,
            user_id: userStore.userInfoUserId,
            card_user_id: 0,
            business_id: res.data.order_sn, // 业务id，团课传order_sn
            business_type: '7', // 业务类型，7--团课，1--散场票，2--订场，3--购卡
          })
          uni.showToast({ title: '支付失败', image: '/static/img/fail.png' })
        },
        complete(res) {
          if (res.errMsg == 'requestPayment:fail cancel') {
            uni.showToast({ title: '支付已取消', image: '/static/img/fail.png' })
          }
        },
      })
    })
}
function getPayStatus() {
  http
    .get('Classmark/getPayCourseStatus', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      order_sn: orderInfo.value.order_sn,
      showToast: false,
    })
    .then((res) => {
      uni.showToast({ title: '支付成功', image: '/static/img/success.png' })
      setTimeout(() => {
        goList()
      }, 1000)
    })
    .catch((err) => {
      if (err.errorcode === 60034) {
        timeer.value = setTimeout(() => {
          getPayStatus()
        }, 1000)
      } else {
        uni.showToast({ title: err.errormsg, image: '/static/img/danger.png' })
      }
    })
}

onUnmounted(() => {
  clearTimeout(timeer.value)
})
function checkboxChange(e) {
  formData.is_protocol = e.detail.value[0] === '1'
}
</script>
<style lang="scss" scoped>
.recharge-item {
  .vip-icon {
    margin-right: 10rpx;
    width: 29rpx;
  }
  .key {
    font-weight: bold;
  }
  .value {
    color: $theme-text-color-other;
    white-space: nowrap;
    .card-balance {
      margin-left: 12rpx;
      font-weight: 400;
    }
    .recharge-link {
      padding: 20rpx 0 20rpx 12rpx;
      text-decoration: underline;
    }
  }
}

.sum-item {
  .value {
    font-size: 24rpx;
    .total {
      font-size: 30rpx;
    }
  }
}

.fixed-bottom-wrap {
  .lef-flex {
    display: flex;
    color: $theme-text-color-other;
  }
  .bill {
    margin-right: 7px;
    font-weight: bold;
    font-size: 36rpx;
    .unit {
      font-size: 24rpx;
    }
  }
  .usage-text {
    align-self: flex-end;
    font-size: 24rpx;
  }
  .normal-btn.custom {
    width: 296rpx;
  }
}
.org-text {
  color: $theme-text-color-other;
}
</style>
