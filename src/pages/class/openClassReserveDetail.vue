<template>
  <view class="footer-hasfixed">
    <OpenClassInfoMore :class-info="classInfo" />
    <RoomSeats
      v-if="classInfo.seat_list && classInfo.seat_list.length"
      :reserve-num="formData.num"
      :can-select="false"
      :seats-list="classInfo.seat_list"
    />
    <view class="form-items theme-bg">
      <view class="item">
        <text class="key">预约方式</text>
        <text class="value">{{ classInfo.card_user_id ? '用卡预约' : '临时购课' }}</text>
      </view>
      <view v-if="selectedCard && selectedCard.name" class="item">
        <text class="key">预约用卡</text>
        <text class="value">{{ selectedCard.name }}</text>
      </view>

      <view class="item">
        <text class="key">课程单价</text>
        <text v-if="classInfo.card_user_id">
          <!-- selectedCard.pay_type == 1卡余额抵扣 2微信支付 -->
          <text v-if="selectedCard.card_type_id == 1 && selectedCard.pay_type == 1" class="value">免费</text>
          <text v-else-if="selectedCard.card_type_id == 3 || selectedCard.pay_type == 2" class="value"
            >{{ selectedCard.todeduct }}元/人</text
          >
          <text v-else class="value"
            >{{ selectedCard.todeduct }}{{ selectedCard.card_type_id == 2 ? '次' : '节' }}/人</text
          >
        </text>
        <text v-else class="value">{{ classInfo.nonmember_price }}元/人</text>
      </view>
      <view class="item">
        <text class="key">预约人数</text>
        <view class="value"> {{ classInfo.mark_num }}人 </view>
      </view>
    </view>

    <view v-if="formData.class_mark_id" class="form-items theme-bg">
      <view class="box-tit">预约信息</view>
      <view v-if="classInfo.status != 1" class="item">
        <text class="key">创建时间</text>
        <text class="value">{{ classInfo.create_date }}</text>
      </view>
      <view v-if="classInfo.status == 1" class="item">
        <text class="key">预约时间</text>
        <text class="value">{{ classInfo.mark_date }}</text>
      </view>
      <view v-if="classInfo.status == 3" class="item">
        <text class="key">取消时间</text>
        <text class="value">{{ classInfo.cancel_date }}</text>
      </view>
      <view v-if="classInfo.status == 3" class="item">
        <text class="key">取消方式</text>
        <text class="value">{{ classInfo.cancel_type }}</text>
      </view>
      <view v-if="classInfo.remark" class="item">
        <text class="key">备注</text>
        <text class="value">{{ classInfo.remark }}</text>
      </view>
    </view>
  </view>
  <view v-if="formData.class_mark_id && classInfo.status == 1" class="fixed-bottom-wrap theme-bg">
    <view class="buttons">
      <button class="normal-btn outer-org" @tap="cancelReserve(classInfo.class_mark_id)">取消预约</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import RoomSeats from './components/RoomSeats.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useOpenClass } from '@/hooks/useOpenClass'
import OpenClassInfoMore from './components/OpenClassInfoMore.vue'
const userStore = useUserStore()
const { cancelReserve, confirmWait, cancelWait } = useOpenClass()
const formData = reactive({
  class_mark_id: '',
  class_waitting_id: '',
})
const classInfo = reactive({
  class_name: '',
  checkmark: '',
})
const selectedCard = reactive({
  card_user_id: '',
})
onLoad((options) => {
  formData.class_mark_id = options.class_mark_id || ''
  formData.class_waitting_id = options.class_waitting_id || ''
  if (formData.class_waitting_id) {
    uni.setNavigationBarTitle({
      title: '团课候补详情',
    })
  }
})
onShow(() => {
  getInfo()
})
const getInfo = () => {
  http
    .get(formData.class_waitting_id ? '/Classwaiting/getDetail' : '/classMark/classDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      ...formData,
    })
    .then((res) => {
      Object.assign(classInfo, res.data)
      Object.assign(selectedCard, res.data?.card)
    })
}
</script>

<style lang="scss">
[data-theme='dark'] {
  .item-notice {
    color: #fff;
  }
}
.form-items {
  margin-top: 20rpx;
}
.tips {
  margin-top: 26rpx;
  padding-bottom: 30rpx;
  line-height: 1.75;
  .title {
    font-weight: bold;
    color: #050505;
  }
}
.des-wrap {
  padding: 0 20rpx 20rpx;
}
.item-notice {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  color: #03080e;
  .circle-8 {
    margin: 0rpx 18rpx;
    width: 8rpx;
    height: 8rpx;
    background: $theme-text-color-grey;
    border-radius: 50%;
  }
}
.info-des {
  font-size: 24rpx;
  line-height: 1.7;
}
.process {
  height: 358rpx;
  padding: 10rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  .line {
    position: absolute;
    height: 300rpx;
    width: 2rpx;
    background-color: #e7e7e7;
    left: 53rpx;
    top: 48rpx;
    z-index: 1;
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 62rpx;
    position: relative;
    z-index: 2;
    .circle {
      font-size: 24rpx;
      width: 44rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      box-sizing: border-box;
      border: 1rpx solid #e7e7e7;
      background-color: #f5f7f9;
      border-radius: 50%;
      color: #898989;
    }
    text {
      color: #313131;
      width: 524rpx;
      background-color: #f5f7f9;
      height: 62rpx;
      line-height: 62rpx;
      text-align: center;
      font-size: 22rpx;
      position: relative;
      &::before {
        content: '';
        width: 0;
        height: 0;
        display: block;
        position: absolute;
        left: -21rpx;
        top: 24rpx;
        border: solid transparent;
        border-width: 7rpx 12rpx;
        border-right-color: #f5f7f9;
      }
    }
  }
}
</style>
