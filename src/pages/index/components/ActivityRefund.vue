<template>
  <view class="refund-box" @tap="goDetail">
    <image class="boximg" mode="scaleToFill" :src="activityInfo.thumb" />
    <view class="refund-info">
      <view class="info-lef">
        <view class="info-tit">{{activityInfo.name}}</view>
        <view class="info-des" v-if="activityInfo.refund_condition == 1">[首次入场不满{{activityInfo.refund_time}}分钟可退款]</view>
        <view class="info-des" v-if="involvedInfo.enter_time">[首次入场时间：{{involvedInfo.enter_time}}]</view>
      </view>
      <view class="info-rig">
         <view v-if="involvedInfo.id" class="qrcode-wrap" @tap.stop="handleQrCode">
          <image class="code-img" mode="scaleToFill" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/qrcode-dark.png" />
          <view>点我进场</view>
        </view>
        <view v-else class="refund-btn" @tap.stop="buyRefundCard">¥{{ activityInfo.curr_cost }} 购买</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="BusInfo">
import { goUrlPage } from '@/utils/urlMap'
import { parseUrl } from '@/utils/shared'
import { useIndexBus } from '@/hooks/useIndexBus'
import { useUserStore } from '@/store/user'
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin } = useLogin()

const props = defineProps<{
  config: Record<string, any>
  isBusPage?: boolean
}>()


const userStore = useUserStore()
const activityInfo = reactive({
  activity_id: '',
  card_name: '',
  name: '',
  curr_cost: '',
  ori_cost: '',
  refund_condition: '',
  refund_time: '',
})
const involvedInfo = reactive({
  enter_time: '',
  leave_time: '',
  duration: '',
  refund_condition: '',
  id: ''
})
const getInfo = (bus_id = busId?.value) => {
  http
    .get('/Activity/getExperienceActivity', {
      bus_id,
      user_id: bus_id === userStore.userInfoBusId ? userStore.userInfoUserId : '',
      activity_id: props.config.activity_id,
    })
    .then((res) => {
      const resData = res.data
      Object.assign(activityInfo, resData.activity_info)
      Object.assign(involvedInfo, resData.activity_experience_involved_info)
    })
}
const { busId } = useIndexBus(props.config.bus_id, getInfo)
function goDetail() {
  if(!involvedInfo.id) {
    return;
  }
  goUrlPage(`/pages/activity/refundActivity?bus_id=${busId.value}&activity_id=${props.config.activity_id}`)
}
function handleQrCode() {
  uni.navigateTo({
    url: '/pages/my/ticket?from=index',
  })
}
async function buyRefundCard() {
  const login = await checkLogin(true, busId.value)
  http
    .post('/Activity/buyExperienceActivity', {
      bus_id: login.bus_id,
      user_id: login.user_id,
      activity_id: props.config.activity_id,
    })
    .then((res) => {
      pay(res.data) 
    })
}
function pay(info: UniApp.RequestPaymentOptions) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success: () => {
      handlePaySuccess()
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
    },
  })
}
function handlePaySuccess() {
  uni.showToast({
    title: '支付成功',
    icon: 'none',
    complete() {
      setTimeout(() => {
        getInfo();
      }, 500)
    },
  })
}
</script>
<style lang="scss">
.refund-box {
  width: 710rpx;
  height: 170rpx;
  margin: 20rpx auto;
  position: relative;
  .boximg {
    width: 100%;
    height: 100%;
    border-radius: 6rpx;
  }
  .refund-info {
    position: absolute;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 30rpx 35rpx 0; 
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 20rpx;
    color: rgba(0,0,0,0.6);
    top: 0;
    left: 0;
  }
  .info-tit {
    font-size: 30rpx;
    color: #000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .info-lef {
    flex: 1;
    overflow: hidden;
  }
  .info-rig {
    flex-shrink: 0;
    text-align: center;
  }
  .info-des {
    margin-top: 12rpx;
  }
  .code-img {
    width: 74rpx;
    height: 74rpx;
  }
  .qrcode-wrap {
    margin-top: 10rpx;
  }
  .refund-btn {
    box-sizing: border-box;
    padding: 0 10rpx;
    min-width: 170rpx;
    height: 53rpx;
    margin-top: 36rpx;
    background: #92DFE8;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    font-weight: bold;
    font-size: 26rpx;
    color: #000000;
    line-height: 53rpx;
  }
}

</style>
