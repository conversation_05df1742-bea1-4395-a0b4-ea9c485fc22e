<template>
  <view class="bot-wrap theme-bg" :class="config.type == 2 ? 'style2' : ''">
    <view class="box-tit">
      {{ config.name || '明星教练' }}
      <view v-if="coachList && coachList.length" class="rig" @tap="goList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <view class="bot-con coach-wrap">
      <view v-for="item in coachList" :key="item.coach_id" class="coach-item" @tap="goDetail(item)">
        <image class="coach-img" mode="aspectFill" :src="item.avatar" />
        <view class="bot">
          <view class="name">{{ item.coach_name }}</view>
          <view v-if="arrIndexOf(config.show_content, '3') && item.position" class="pos">{{ item.position }}</view>
          <view v-if="arrIndexOf(config.show_content, '1') && item.specialty" class="des">{{ item.specialty }}</view>
        </view>
      </view>
      <view v-if="coachList && coachList.length == 0" class="nodata">暂未设置教练</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="CoachInfo">
import http from '@/utils/request'
import { arrIndexOf } from '@/utils/shared'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
  index: number
}>()

const coachList = ref<Record<string, any>>([])
const getInfo = (bus_id, merchant_type_get) => {
  http
    .get('/Coach/getList', {
      bus_id,
      merchant_type_get,
      config_index: props.index,
      is_index: true,
    })
    .then((res) => {
      coachList.value = res.data?.list
    })
}
const { busId } = useIndexBus(props.config?.bus_id, getInfo)
function goDetail(info) {
  goUrlPage(`/pages/coach/detail?coach_id=${info.coach_id}&bus_id=${busId.value}`)
}
function goList() {
  goUrlPage(`/pages/coach/list?bus_id=${busId.value}`)
}
</script>
<style lang="scss">
.coach-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow-y: scroll;
}
.coach-item {
  width: 207rpx;
  margin-right: 20rpx;
  font-size: 24rpx;
  margin-bottom: 30rpx;
  .pos {
    font-size: 22rx;
    margin-bottom: 10rpx;
    color: $theme-text-color-grey;
  }
  .name {
    font-weight: bold;
    margin-bottom: 12rpx;
  }
  .des,
  .name,
  .pos {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .bot {
    text-align: center;
  }
}
.coach-img {
  width: 205rpx;
  height: 205rpx;
  border: 1rpx solid #f6f6f8;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
[data-theme='dark'] {
  .coach-img {
    border-color: #2d2d2d;
  }
}
.style2 {
  .bot-con {
    flex-direction: column;
    align-items: flex-start;
  }
  .coach-item {
    display: flex;
    width: 100%;
    margin-right: 20rpx;
    justify-items: flex-start;
    align-items: center;
    .bot {
      text-align: left;
      flex: 1;
      overflow: hidden;
    }
    .name {
      font-size: 30rpx;
    }
  }
  .coach-img {
    width: 150rpx;
    height: 150rpx;
    margin-right: 30rpx;
    margin-bottom: 0;
  }
}
</style>
