<template>
  <view v-if="payscoreFlag" class="invitation-wrap" @tap="goDetail">
    <image class="invitation-img" :src="config.background_img" />
  </view>
</template>

<script setup lang="ts" name="BusInfo">
import http from '@/utils/request'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
}>()

// check payscore
const payscoreFlag = ref(false)
const getPayscoreFlag = (bus_id, merchant_type_get) => {
  return http
    .post('/Contract/checkBusContractStatus', {
      bus_id,
      merchant_type_get,
      loading: false,
    })
    .then((res) => {
      payscoreFlag.value = res?.data?.bus_status
    })
}

const { busId } = useIndexBus(props.config?.bus_id, getPayscoreFlag)

function goDetail() {
  goUrlPage(`/pages/payscore/list?bus_id=${busId.value}`)
}
</script>
<style lang="scss">
.invitation-wrap {
  width: 710rpx;
  height: 180rpx;
  margin: 0 auto 20rpx;
  .invitation-img {
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
  }
}
</style>
