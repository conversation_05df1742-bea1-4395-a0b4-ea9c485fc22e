<template>
  <view class="img-box">
    <block v-if="config.link_url === 'contact'">
      <button class="contact-btn" open-type="contact" hover-class="none">
        <image class="boximg" mode="scaleToFill" :src="config.img_url" />
      </button>
    </block>
    <block v-else>
      <image class="boximg" mode="scaleToFill" :src="config.img_url" @tap="goDetail" />
    </block>
  </view>
</template>

<script setup lang="ts" name="BusInfo">
import { goUrlPage } from '@/utils/urlMap'
import { parseUrl } from '@/utils/shared'
const props = defineProps<{
  config: Record<string, any>
  isBusPage?: boolean
}>()

function goDetail() {
  const { query } = parseUrl(decodeURIComponent(props.config.link_url))
  if (props.config.link_url) {
    goUrlPage(props.config.link_url, { isShowMerchantMode: props.isBusPage ? false : query.isShowMerchantMode })
  }
}
</script>
<style lang="scss">
.img-box {
  width: 710rpx;
  height: 280rpx;
  margin: 0 auto 20rpx;

  .contact-btn {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    line-height: 1;

    &::after {
      display: none;
    }
  }

  .boximg {
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
  }
}
</style>
