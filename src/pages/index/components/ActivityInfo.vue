<template>
  <view class="bot-wrap theme-bg" :class="config.type == 2 ? 'style2' : ''">
    <view class="box-tit">
      <view class="tit-left">
        {{ config.name || '热门活动' }}
        <image class="hot-icon" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/hot.png" />
      </view>
      <view class="rig" @tap="goList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <template v-if="actList && actList.length">
      <uni-swiper-dot
        v-if="config.type != 2"
        class="uni-swiper-dot-box"
        mode="round"
        :dots-styles="dotsStyles"
        :info="actList"
        :current="current"
        field="content"
      >
        <swiper
          class="swiper-box"
          :style="{
            height:
              !arrIndexOf(config.show_content, '1') && !arrIndexOf(config.show_content, '2')
                ? '530rpx'
                : arrIndexOf(config.show_content, '1') && arrIndexOf(config.show_content, '2')
                ? '610rpx'
                : '570rpx',
          }"
          autoplay
          @change="swiperChange"
        >
          <swiper-item v-for="(item, index) in actList" :key="index">
            <view class="swiper-item" :class="'swiper-item' + index" @tap="goDetail(item)">
              <image class="act-img" mode="scaleToFill" :src="item.thumb" />
              <view class="act-con">
                <view class="act-con-lef">
                  <view class="name text-overflow">{{ item.name }}</view>
                  <view v-if="arrIndexOf(config.show_content, '1')" class="time"
                    >活动时间 {{ item.beg_date }}~{{ item.end_date }}</view
                  >
                  <view v-if="arrIndexOf(config.show_content, '2')" class="time"
                    >报名截止时间 {{ item.cutoff_date }}</view
                  >
                </view>
                <view
                  v-if="arrIndexOf(config.show_content, '3') || arrIndexOf(config.show_content, '4')"
                  class="act-con-rig"
                >
                  <text v-if="arrIndexOf(config.show_content, '3')">
                    已报：
                    <text>
                      {{ item.sign_count }}
                    </text>
                  </text>
                  <text v-if="arrIndexOf(config.show_content, '3') && arrIndexOf(config.show_content, '4')">/</text>
                  <text v-if="arrIndexOf(config.show_content, '4')">
                    {{ item.about_number }}
                  </text>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </uni-swiper-dot>
      <view v-for="(item, index) in actList" v-else :key="index" class="list-item" @tap="goDetail(item)">
        <image class="act-img" mode="scaleToFill" :src="item.thumb" />
        <view class="act-con">
          <view class="name">{{ item.name }}</view>
          <view v-if="arrIndexOf(config.show_content, '1')" class="time"
            >活动时间 {{ item.beg_date }}~{{ item.end_date }}</view
          >
          <view v-if="arrIndexOf(config.show_content, '2')" class="time">报名截止时间 {{ item.cutoff_date }}</view>
          <view class="time">
            <text v-if="arrIndexOf(config.show_content, '3')">
              已报人数
              <text class="theme-color-other">
                {{ item.sign_count }}
              </text>
            </text>
            <text v-if="arrIndexOf(config.show_content, '3') && arrIndexOf(config.show_content, '4')">/</text>
            <text v-if="arrIndexOf(config.show_content, '4')" class="theme-color-other">
              {{ item.about_number }}
            </text>
          </view>
        </view>
      </view>
    </template>
    <view v-if="actList && actList.length == 0" class="nodata">暂未设置热门活动</view>
  </view>
</template>

<script setup lang="ts" name="ActivityInfo">
import http from '@/utils/request'
import { arrIndexOf } from '@/utils/shared'
import { useUserStore } from '@/store/user'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const actList = ref<Record<string, any>[]>([])
const userStore = useUserStore()
const getInfo = (bus_id = busId?.value, merchant_type_get = isShowMerchantMode?.value, loading = false) => {
  http
    .get('/Activity/getList', {
      bus_id,
      config_index: props.index,
      merchant_type_get,
      user_id: userStore.userInfoUserId,
      is_index: true,
      loading,
    })
    .then((res) => {
      actList.value = res.data?.list
    })
}
const props = defineProps<{
  config: Record<string, any>
  index: number
}>()
const { busId, isShowMerchantMode } = useIndexBus(props.config.bus_id, getInfo, true)

const current = ref(0)
function swiperChange(e) {
  current.value = e.detail.current
}
const dotsStyles = {
  width: 3,
  height: 3,
  backgroundColor: '#7D7D7D',
  border: '1px solid #7D7D7D',
  selectedBorder: '1px solid #7D7D7D',
  selectedBackgroundColor: '#7D7D7D',
}

uni.$on('refresh-activity', getInfo)
onUnmounted(() => {
  uni.$off('refresh-activity', getInfo)
})

function goDetail(info) {
  goUrlPage(`/pages/activity/activityDetail?id=${info.id}&bus_id=${busId.value}`)
}
function goList(info) {
  goUrlPage(`/pages/activity/index?bus_id=${busId.value}`)
}
</script>
<style lang="scss">
.tit-left {
  display: flex;
  align-items: center;
}
.hot-icon {
  margin-left: 10rpx;
  width: 62rpx;
  height: 22rpx;
}
.act-img {
  width: 670rpx;
  height: 387rpx;
  border-radius: 20rpx;
}
.swiper-box {
  height: 610rpx;
}
.act-con {
  position: relative;
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  .name {
    margin-top: 20rpx;
    max-width: 510rpx;
    font-weight: bold;
    font-size: 30rpx;
  }
  .time {
    margin-top: 15rpx;
    color: $theme-text-color-grey;
  }
  .act-con-rig {
    position: absolute;
    top: 20rpx;
    right: 0;
    // margin-top: 20rpx;
    padding: 0 18rpx;
    border-radius: 21rpx;
    height: 41rpx;
    background: rgba(var(--THEME-RGB), 0.2);
    line-height: 41rpx;
    font-size: 22rpx;
    color: #535353;
  }
}
.list-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e8e8e8;
  &:last-child {
    border-bottom: none;
  }
  .act-con {
    display: block;
    .name {
      margin-top: 0;
    }
  }
  .act-img {
    margin-right: 23rpx;
    width: 182rpx;
    height: 105rpx;
    border-radius: 6rpx;
  }
}
[data-theme='dark'] {
  .list-item {
    border-color: #2d2d2d;
  }
}
</style>
