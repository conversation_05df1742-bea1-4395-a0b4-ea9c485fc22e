<template>
  <view class="bot-wrap theme-bg" :class="config.type == 2 ? 'style2' : ''">
    <view class="box-tit">
      {{ config.name || '套餐包' }}
      <view class="rig" @tap="goCardList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <view class="bot-con">
      <view v-for="item in cardList" :key="item.card_id" class="card-item" @tap="goDetail(item)">
        <image class="card-img" :src="item.thumb" mode="scaleToFill" />
        <view class="lef">
          <view class="tit">
            <image
              v-if="item.is_point_activity === 1"
              class="icon"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/song.png"
              mode="scaleToFill"
            />
            {{ item.card_name }}
          </view>
          <view class="des">
            {{ item.contend }}
          </view>
          <view class="price">{{
            item.current_price && +item.current_price > 0 ? `￥${item.current_price}` : '价格面议'
          }}</view>
        </view>
      </view>
      <view v-if="cardList && cardList.length == 0" class="nodata">暂未设置套餐包</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="PackageInfo">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'
interface CardItem {
  card_type_id: string | number
  package_id?: string | number
  name: string
  is_point_activity: 0 | 1
  [propName: string]: string | number | undefined
}

const props = defineProps({
  config: {
    type: Object,
    default: {},
  },
  index: Number,
})

const cardList = ref<CardItem[]>([])

const userStore = useUserStore()
const getInfo = (bus_id: string, merchant_type_get) => {
  http
    .get('/Card/getList', {
      bus_id,
      merchant_type_get,
      type: 3,
      config_index: props.index,
      is_index: true,
    })
    .then((res) => {
      cardList.value = res.data?.list
    })
}
const { busId } = useIndexBus(props.config?.bus_id, getInfo)
function goCardList() {
  goUrlPage('/pages/card/list?type=3&bus_id=' + busId.value)
}
function goDetail(info: CardItem) {
  goUrlPage(
    `/pages/card/${[1, 2, 3].includes(+info.card_type_id) ? 'buyCardDetail' : 'cardDetail'}?package_id=${
      info.package_id || ''
    }&bus_id=${busId.value}`
  )
}
</script>
<style lang="scss" scoped>
.card-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  .tit {
    margin-bottom: 13rpx;
    font-weight: bold;
    font-size: 30rpx;
    .icon {
      vertical-align: middle;
    }
  }
  .lef {
    flex: 1;
  }
  .des {
    display: -webkit-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    /*! autoprefixer: ignore next */
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 84rpx;
    color: $theme-text-color-grey;
  }
  .price {
    margin-top: 20rpx;
    font-weight: bold;
    font-size: 30rpx;
    color: $theme-text-color-other;
  }
}
.card-img {
  margin-right: 24rpx;
  width: 258rpx;
  height: 134rpx;
}
.style2 {
  .bot-con {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .card-item {
    flex-direction: column;
    align-items: flex-start;
    box-sizing: border-box;
    padding: 31rpx;
    border: 1px solid #e8e8e8;
    border-radius: 10rpx;
    width: 322rpx;
    .tit {
      margin-bottom: 18rpx;
    }
    .lef {
      margin-top: 16rpx;
    }
  }
  .card-img {
    margin-bottom: 10rpx;
  }
}
[data-theme='dark'] {
  .style2 {
    .card-item {
      border-color: #2d2d2d;
    }
  }
}
</style>
