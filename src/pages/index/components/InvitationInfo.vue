<template>
  <view v-if="showInvitationPolite" class="invitation-wrap" @tap="goDetail">
    <image class="invitation-img" :src="config.background_img" />
  </view>
</template>

<script setup lang="ts" name="BusInfo">
import http from '@/utils/request'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
}>()
const showInvitationPolite = ref(false)
// 是否有进行中的邀请有礼活动
function getInvitationPolite(bus_id, merchant_type_get) {
  http
    .get('Personalcenter/getInvitationPolite', {
      bus_id,
      merchant_type_get,
      loading: false,
    })
    .then((res) => {
      showInvitationPolite.value = res.data.show_invitation_polite
    })
}
const { busId } = useIndexBus(props.config?.bus_id, getInvitationPolite)
function goDetail() {
  goUrlPage(`/pages/invitation/detail?bus_id=${busId.value}`)
}
</script>
<style lang="scss">
.invitation-wrap {
  width: 710rpx;
  height: 180rpx;
  margin: 0 auto 20rpx;
  .invitation-img {
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
  }
}
</style>
