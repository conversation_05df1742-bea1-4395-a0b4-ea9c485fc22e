<template>
  <view class="bot-wrap theme-bg" :class="config.type == 2 ? 'style2' : ''">
    <view class="box-tit">
      {{ config.name || '推荐团课' }}
      <view v-if="classList && classList.length" class="rig" @tap="goList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <view class="bot-con class-wrap">
      <view v-for="item in classList" :key="item.coach_id" class="class-item" @tap="goDetail(item)">
        <view class="img-wrap">
          <image class="item-img" mode="aspectFill" :src="item.thumb" />
          <view class="img-con">
            <view v-if="item.min_number && arrIndexOf(config.show_content, '4')" class="con-text"
              >满{{ item.min_number }}人开课</view
            >
            <uni-rate
              v-if="arrIndexOf(config.show_content, '3')"
              class="rate"
              :readonly="true"
              :value="item.class_level"
              :max="item.class_level"
              :size="12"
            />
          </view>
        </view>
        <view class="bot">
          <view class="name">{{ item.class_name }}</view>
          <view v-if="item.schedule_info" class="des">
            <text class="item time">上课时间：{{ item.schedule_info.beg_time }}-{{ item.schedule_info.end_time }}</text>
            <text v-if="arrIndexOf(config.show_content, '2')" class="item">{{
              item.schedule_info.classroom_name
            }}</text>
            <text v-if="arrIndexOf(config.show_content, '1')" class="item">{{ item.schedule_info.coach_name }}</text>
          </view>
          <view v-else>
            <view class="item time">今日暂无排课信息</view>
            <view class="item">&nbsp;</view>
          </view>
        </view>
      </view>
      <view v-if="classList && classList.length == 0" class="nodata">暂未设置推荐团课</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { arrIndexOf } from '@/utils/shared'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
  index: number
}>()

const classList = ref<Record<string, any>[]>([])
const getInfo = (bus_id, merchant_type_get) => {
  http
    .get('Course/getCourseList', {
      bus_id,
      merchant_type_get,
      config_index: props.index,
    })
    .then((res) => {
      classList.value = res.data
    })
}
const { busId } = useIndexBus(props.config?.bus_id, getInfo)
function goDetail(info) {
  if (info.schedule_info) {
    goUrlPage(
      `/pages/class/openClassDetail?class_id=${info.id}&id=${info.schedule_info.course_schedule_id || ''}&coach_id=${
        info.schedule_info.coach_id
      }&bus_id=${busId.value}`
    )
  } else {
    goUrlPage(`/pages/class/openClassDetail?class_id=${info.id}&only_class_info=1&bus_id=${busId.value}`)
  }
}
function goList() {
  goUrlPage(`/pages/class/class?type=1&bus_id=${busId.value}`)
}
</script>
<style lang="scss">
.class-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.class-item {
  width: 330rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 20rpx;
  overflow: hidden;
  font-size: 24rpx;
  margin-bottom: 30rpx;
  &:first-child {
    width: 670rpx;
    .time {
      width: 100%;
    }
    .item-img {
      height: 388rpx;
    }
  }
  .item-img {
    width: 100%;
    height: 190rpx;
    border-radius: 20rpx 20rpx 0 0;
  }
  .img-wrap {
    position: relative;
    color: #fff;
    .img-con {
      position: absolute;
      left: 0;
      bottom: 20rpx;
      width: 100%;
      box-sizing: border-box;
      padding: 0 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .con-text {
      text-shadow: 0px 1rpx 4rpx rgba(0, 0, 0, 0.3);
      font-weight: bold;
      color: #fff;
    }
  }
  .des {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .item {
      margin-right: 10rpx;
      min-height: 22rpx;
    }
  }

  .bot {
    padding: 0 20rpx 20rpx;
    line-height: 1.75;

    .name {
      font-size: 30rpx;
      font-weight: bold;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.style2 {
  .class-wrap {
    width: 100%;
    overflow-x: scroll;
    white-space: nowrap;
    flex-wrap: nowrap;
    .class-item {
      width: 450rpx;
      flex-shrink: 0;
      margin-right: 20rpx;
      .item-img {
        width: 100%;
        height: 261rpx;
      }
      .time {
        width: 100%;
      }
    }
  }
}
[data-theme='dark'] {
  .class-item {
    border-color: #2d2d2d;
  }
}
</style>
