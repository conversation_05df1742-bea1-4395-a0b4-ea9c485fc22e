<template>
  <view class="bot-wrap theme-bg" :class="config.type == 2 ? 'style2' : ''">
    <view class="box-tit">
      {{ config.name || '会员卡' }}
      <view class="rig" @tap="goCardList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <view class="bot-con">
      <view v-for="item in cardList" :key="item.card_id" class="card-item" @tap="goDetail(item)">
        <image class="card-img" :src="item.thumb" mode="scaleToFill" />
        <view class="lef">
          <view class="tit">
            <image
              v-if="item.is_point_activity === 1"
              class="icon"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/song.png"
              mode="scaleToFill"
            />
            {{ item.name }}
          </view>
          <view class="des">
            <text v-if="arrIndexOf([1, 2, 3], item.card_type_id)">
              <text class="theme-color-other">{{ item.card_type_id == 1 ? item.end_time : item.number }}</text>
              {{ getCardTypeUnit(item.card_type_id, item.is_pt_time_limit_card) }}
              <text v-if="item.gift_number && arrIndexOf(config.show_content, '2')">
                +赠送<text class="theme-color-other">{{ item.gift_number }}</text
                >{{ getCardTypeUnit(item.card_type_id, item.is_pt_time_limit_card) }}
              </text>
            </text>
            <text v-if="item.card_type_id == 4 || item.card_type_id == 5">
              单节时长<text class="theme-color-other">{{ item.class_duration }}</text
              >分钟
            </text>
            <text v-if="arrIndexOf(config.show_content, '1')"
              ><text class="cut-up">|</text>{{ getEndTimeString(item) }}</text
            >
          </view>

          <view
            v-if="!item.is_pt_time_limit_card && (item.card_type_id == 4 || item.card_type_id == 5)"
            class="price"
            >{{ item.single_price ? `￥${item.single_price}/节` : '价格面议' }}</view
          >
          <view v-else class="price">{{ item.current_price ? `￥${item.current_price}` : '价格面议' }}</view>
        </view>
      </view>
      <view v-if="cardList && cardList.length == 0" class="nodata">暂未设置会员卡</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="CardInfo">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { arrIndexOf, getCardTypeUnit } from '@/utils/shared'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'
interface CardItem {
  card_type_id: string | number
  package_id?: string | number
  name: string
  is_point_activity: 0 | 1
  [propName: string]: string | number | undefined
}

const props = defineProps({
  config: {
    type: Object,
    default: {},
  },
  index: Number,
})
const getEndTimeString = (item: CardItem): string => {
  if(item.end_time) {
    const allNum = parseInt(item.end_time) + parseInt(item.gift_number || 0)
    return `有效期${item.is_pt_time_limit_card || item.card_type_id == 1 ? allNum : item.end_time}天`
  } else {
    return '永久有效'
  }
}

const cardList = ref<CardItem[]>([])

const userStore = useUserStore()
const getInfo = (bus_id: string, merchant_type_get) => {
  http
    .get('/Card/getList', {
      bus_id,
      merchant_type_get,
      config_index: props.index,
      is_index: true,
    })
    .then((res) => {
      cardList.value = res.data?.list
    })
}
const { busId } = useIndexBus(props.config.bus_id, getInfo)
function goCardList() {
  goUrlPage(`/pages/card/list?bus_id=${busId.value}`)
}
function goDetail(info: CardItem) {
  goUrlPage(
    `/pages/card/${[1, 2, 3].includes(+info.card_type_id) ? 'buyCardDetail' : 'cardDetail'}?card_id=${
      info.card_id || ''
    }&package_id=${info.package_id || ''}&bus_id=${busId.value}`
  )
}
</script>
<style lang="scss" scoped>
.card-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  .tit {
    margin-bottom: 13rpx;
    font-weight: bold;
    font-size: 30rpx;
    .icon {
      vertical-align: middle;
    }
  }
  .des {
    font-weight: 400;
    font-size: 24rpx;
    color: $theme-text-color-grey;
  }
  .price {
    margin-top: 20rpx;
    font-weight: bold;
    font-size: 30rpx;
    color: $theme-text-color-other;
  }
}
.card-img {
  margin-right: 24rpx;
  width: 258rpx;
  height: 134rpx;
}
.style2 {
  .bot-con {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .card-item {
    flex-direction: column;
    align-items: flex-start;
    box-sizing: border-box;
    padding: 31rpx;
    border: 1px solid #e8e8e8;
    border-radius: 10rpx;
    width: 322rpx;
    .tit {
      margin-bottom: 18rpx;
    }
    .lef {
      margin-top: 16rpx;
    }
  }
  .card-img {
    margin-bottom: 10rpx;
  }
}
[data-theme='dark'] {
  .style2 {
    .card-item {
      border-color: #2d2d2d;
    }
  }
}
</style>
