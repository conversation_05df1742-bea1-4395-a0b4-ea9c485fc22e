<template>
  <view class="bot-wrap theme-bg">
    <view class="box-tit">
      {{ config.name || '票务推荐' }}
      <view v-if="sanList && sanList.length" class="rig" @tap="goList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <view class="bot-con">
      <TicketItem v-for="item in sanList" :key="item.id" :info="item" :bus-id="busId" />
      <view v-if="sanList && sanList.length == 0" class="nodata">暂未设置推荐票务</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="TicketInfo">
import http from '@/utils/request'
import TicketItem from '@/pages/stadium/components/TicketItem.vue'
import { useUserStore } from '@/store/user'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
  index: number
}>()

const sanList = ref<Record<string, any>[]>([])
const getInfo = (bus_id, merchant_type_get) => {
  http
    .get('/Santicket/getList', {
      bus_id,
      merchant_type_get,
      config_index: props.index,
      is_index: true,
    })
    .then((res) => {
      sanList.value = res.data?.list
    })
}
const { busId } = useIndexBus(props.config?.bus_id, getInfo)
function goList() {
  goUrlPage(`/pages/stadium/ticketList?bus_id=${busId.value}`)
}
</script>
<style lang="scss" scoped></style>
