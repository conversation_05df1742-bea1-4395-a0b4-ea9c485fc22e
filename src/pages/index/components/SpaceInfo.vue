<template>
  <view class="bot-wrap theme-bg">
    <view class="box-tit">
      {{ config.name || '场地推荐' }}
      <view v-if="sanList && sanList.length" class="rig" @tap="goList">
        查看更多
        <view class="arrow-right"></view>
      </view>
    </view>
    <view class="bot-con">
      <SpaceItem v-for="item in sanList" :key="item.id" :bus-id="busId" :info="item" />
      <view v-if="sanList && sanList.length == 0" class="nodata">暂未设置推荐场地</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="TicketInfo">
import http from '@/utils/request'
import SpaceItem from '@/pages/stadium/components/SpaceItem.vue'
import { useUserStore } from '@/store/user'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
  index: number
}>()
const sanList = ref<Record<string, any>[]>([])
const userStore = useUserStore()
const getInfo = (bus_id, merchant_type_get) => {
  http
    .get('/Booking/getSpaceTypeList', {
      bus_id,
      merchant_type_get,
      user_id: userStore.userInfoUserId,
      config_index: props.index,
      is_index: true,
    })
    .then((res) => {
      sanList.value = res.data?.list
    })
}

const { busId } = useIndexBus(props.config?.bus_id, getInfo)
function goList() {
  goUrlPage(`/pages/class/class?type=3&bus_id=${busId.value}`)
}
</script>
<style lang="scss" scoped>
.bot-con {
  padding-bottom: 20rpx;
}
</style>
