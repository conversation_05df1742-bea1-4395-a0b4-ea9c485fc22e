<template>
  <view class="nav-wrap " :class="{'style-1': config.type == 1 || !config.type, 'style-2': config.type == 2, 'style-3': config.type == 3 }">
    <view
      v-for="(item, index) in config.icon_content"
      :key="index"
      class="nav-item"
      @tap="goDetail(item)"
    >
      <block v-if="item.link === 'contact'">
        <button class="contact-btn" open-type="contact" hover-class="none">
          <image class="nav-img" :src="item.icon" mode="scaleToFill" />
          <view class="bot">
            {{ item.name }}
          </view>
        </button>
      </block>
      <block v-else>
        <image class="nav-img" :src="item.icon" mode="scaleToFill" />
        <view class="bot">
          {{ item.name }}
        </view>
      </block>
    </view>
  </view>
</template>

<script setup lang="ts">
import { goUrlPage } from '@/utils/urlMap'
import { parseUrl } from '@/utils/shared'
const props = defineProps<{
  config: Record<string, any>
  isBusPage?: boolean
  index: number
}>()
function goDetail(info) {
  if (info.link === 'contact') {
    return
  }
  const { query } = parseUrl(decodeURIComponent(info.link))
  if (info.link) {
    goUrlPage(info.link, { isShowMerchantMode: props.isBusPage ? false : query.isShowMerchantMode })
  }
}
</script>
<style lang="scss" scoped>
.nav-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  overflow: hidden;
  margin: 20rpx;

  // Add contact button styles
  .contact-btn {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    background: transparent;
    border: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    line-height: normal;
    font-size: inherit;
    color: inherit;

    &::after {
      display: none;
    }
  }

  .nav-item {
    display: flex;
    background: #fff;
    box-sizing: border-box;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 22rpx;
    padding: 20rpx;
    flex: 1;
    overflow: hidden;
  }
  .nav-img {
    width: 82rpx;
    height: 82rpx;
    margin-bottom: 20rpx;
  }
  .bot {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &.style-1{
    border-radius: 10rpx;
    background: #fff;
    .nav-item {
      min-width: 20%;
      &:nth-child(n+6) {
        flex-basis: 20%;
        flex-grow: 0;
      }
    }
  }
}
.nav-wrap.style-2 {
  .nav-item {
    position: relative;
    flex-basis: 50% !important;
    &:first-child, &:nth-child(2) {
      height: 240rpx;
      text-align: left;
      font-size: 30rpx;
      font-weight: bold;
      .nav-img {
        position: absolute;
        right: 0;
        bottom: 8rpx;
        width: 190rpx;
        height: 190rpx;
      }
      .bot {
        position: absolute;
        left: 20rpx;
        top: 10rpx;
        font-weight: bold;
        font-size: 30rpx;
        color: #000000;
        line-height: 64rpx;
      }
    }
    &:first-child {
      padding-left: 20rpx;
      border-radius: 10rpx 0 0 10rpx;
      flex-grow: 0;
      .nav-img {
        right: 20rpx;
      }
      // 右侧实线
      &::after {
        content: '';
        position: absolute;
        top: 30rpx;
        right: 0;
        bottom: 0;
        width: 1rpx;
        height: 180rpx;
        background: #D9D9D9;
      }
    }
    &:nth-child(2) {
      border-radius: 0 10rpx 10rpx 0;
    }
    &:nth-child(n+3) {
      flex-grow: 0 !important;
      flex-shrink: 0 !important;
      flex-basis: 31.2% !important;
      font-size: 24rpx;
      font-weight: bold;
      height: 210rpx;
      margin-right: 3.2%;
      margin-top: 20rpx;
      border-radius: 10rpx;
      .nav-img {
        width: 120rpx;
        height: 120rpx;
      }
    }
    &:nth-child(3n+2) {
      margin-right: 0;
    }
  }
}
.nav-wrap.style-3 {
  .nav-item {
    position: relative;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: 31.2% !important;
    font-size: 26rpx;
    font-weight: bold;
    height: 222rpx;
    border-radius: 10rpx;
    margin-right: 3.2%;
    .nav-img {
      width: 130rpx;
      height: 130rpx;
    }
    &:nth-child(3) {
      margin-right: 0;
    }
    &:nth-child(n+4) {
      font-size: 20rpx;
      margin-right: 0;
      margin-top: 20rpx;
      flex-basis: 33.33% !important;
      height: 190rpx;
      .nav-img {
        width: 100rpx;
        height: 100rpx;
      }
    }
    &:nth-child(3n+4) {
      border-radius: 10rpx 0 0 10rpx;
    }
    &:nth-child(3n+5) {
      border-radius: 0;
    }
    &:nth-child(3n+6) {
      border-radius: 0 10rpx 10rpx 0;
    }
  }
}
</style>
