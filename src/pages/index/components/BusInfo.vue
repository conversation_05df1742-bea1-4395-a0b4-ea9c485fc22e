<template>
  <view class="bus-box" :class="config.type == 2 ? 'style2' : ''">
    <view class="banner-box">
      <swiper
        v-if="busInfo.imgs && busInfo.imgs.length && !(busInfo.imgs.length === 1 && busInfo.imgs[0].img_url === '')"
        class="swiper"
        :autoplay="true"
        :current="curImgIndex"
        @animationfinish="changCurrent"
      >
        <swiper-item v-for="(item, index) in busInfo.imgs" :key="index">
          <image class="bg-img" mode="scaleToFill" :src="item.img_url" @tap="goPage(item)" />
        </swiper-item>
      </swiper>
      <view
        v-if="busInfo.imgs && busInfo.imgs.length && !(busInfo.imgs.length === 1 && busInfo.imgs[0].img_url === '')"
        class="swiper-page"
      >
        <uni-icons type="image" size="12" color="#fff"></uni-icons>
        <view class="swiper-page-text">
          {{ curImgIndex + 1 + '/' + (busInfo.imgs && busInfo.imgs.length ? busInfo.imgs.length : 0) }}
        </view>
      </view>
    </view>

    <view class="bus-wrap theme-bg">
      <view class="bus-info" @tap="goBusDetail">
        <image class="logo" mode="scaleToFill" :src="busInfo.thumb" />
        <view class="bus-info-rig">
          <view>{{ busInfo.name }}</view>
          <view class="arrow-right"></view>
          
        </view>
      </view>
      <view class="bus-des" @tap="findMe">
        <view class="address-info">
          <image class="icon icon-mr" mode="aspectFit" src="/static/img/address.png" />
          <view>{{ busInfo.address }}</view>
        </view>
        <view class="act-list">
          <view v-if="config.toggle_bus === '1'" class="act-wrap" @tap.stop="switchBus">
            <ThemeIcon type="t-icon-shouye-qiehuan" :size="18" style="margin-bottom: 10rpx;" />
            切换场馆
          </view>
          <view class="act-wrap" @tap.stop="callMe">
            <ThemeIcon type="t-icon-shouye-dianhua" :size="18" style="margin-bottom: 10rpx;" />
            电话
          </view>
        </view>
      </view>
    </view>
    <scroll-view v-if="busInfo.imgs && busInfo.imgs.length && config.type == 2" class="scroll-box" scroll-x="true">
      <image
        v-for="(item, index) in busInfo.imgs"
        :key="index"
        class="scroll-img"
        mode="scaleToFill"
        :src="item.img_url"
        @tap="goPage(item)"
      />
    </scroll-view>

    <view v-if="isDetail" class="bot-wrap theme-bg">
      <view class="box-tit">品牌故事</view>
      <view class="info-des rich-text">
        <rich-text :nodes="busInfo.bus_description"></rich-text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="BusInfo">
import http from '@/utils/request'
import { unescapeHTML } from '@/utils/shared'
import { goUrlPage } from '@/utils/urlMap'
import { parseUrl } from '@/utils/shared'
import { useIndexBus } from '@/hooks/useIndexBus'
const props = withDefaults(
  defineProps<{
    config: Record<string, any>
    isDetail?: boolean
    isBusPage?: boolean
    index?: number
  }>(),
  {
    isDetail: false,
  }
)

const busInfo = reactive<Record<string, any>>({})
const curImgIndex = ref(0)
const changCurrent = (e) => {
  curImgIndex.value = e.target.current
}

function switchBus() {
  uni.navigateTo({
    url: '/pages/busSelect',
  })
}

const getInfo = (bus_id, merchant_type_get) => {
  http
    .get('/Business/getDetail', {
      bus_id,
      config_index: props.index || '',
      merchant_type_get,
      is_index: !props.isDetail,
    })
    .then((res) => {
      const info = res.data?.info
      info.bus_description = unescapeHTML(info.bus_description)
      info.imgs = info.imgs && info.imgs.length ? info.imgs.filter((item) => item.img_url) : []
      Object.assign(busInfo, info)
    })
}
const { busId } = useIndexBus(props.config.bus_id, getInfo)
function callMe() {
  uni.makePhoneCall({
    phoneNumber: busInfo.phone,
  })
}
function findMe() {
  uni.openLocation({
    latitude: parseFloat(busInfo.lat),
    longitude: parseFloat(busInfo.lng),
    name: busInfo.name,
    address: busInfo.address,
  })
}
function goBusDetail() {
  if (props.isDetail) {
    findMe()
    return false
  }
  goUrlPage(`/pages/bus/detail?bus_id=${busId.value}`)
}
function goPage(info) {
  const { query } = parseUrl(decodeURIComponent(info.img_link))
  if (info.img_link) {
    goUrlPage(info.img_link, { isShowMerchantMode: props.isBusPage ? false : query.isShowMerchantMode })
  }
}
</script>
<style lang="scss" scoped>
.swiper {
  height: 460rpx;
}
.banner-box {
  position: relative;
  min-height: 140rpx;
}
.swiper-page {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 16rpx;
  right: 20rpx;
  width: 113rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 18rpx;
}
.bg-img {
  width: 750rpx;
  height: 460rpx;
}
.swiper-page-text {
  margin-left: 10rpx;
  font-size: 20rpx;
  font-weight: 800;
  color: #ffffff;
}
.bus-box {
  position: relative;
  .scroll-box {
    white-space: nowrap;
    padding-left: 20rpx;
    box-sizing: border-box;
    width: 100%;
    display: none;
  }
  .scroll-img {
    width: 275rpx;
    height: 168rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
  }
}
.style2 {
  margin-top: 70rpx;
  .banner-box {
    display: none;
  }
  .scroll-box {
    display: block;
  }
}

.bus-wrap {
  position: relative;
  margin: 20rpx;
  padding: 108rpx 20rpx 0;
  border-radius: 10rpx;
}
.bus-info {
  width: 100%;
  position: absolute;
  top: -40rpx;
  left: 20rpx;
  display: flex;
  align-items: flex-end;

  font-size: 36rpx;
  font-weight: bold;
}
.bus-info-rig {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  .arrow-right {
    margin-right: 40rpx;
  }
}
.bus-info .logo {
  width: 112rpx;
  height: 112rpx;
  margin-right: 14rpx;
  border-radius: 20rpx;
}
.bus-des {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  font-weight: bold;
  padding-bottom: 30rpx;
}

.address-info {
  display: flex;
  align-items: flex-start;
  width: 420rpx;
  font-size: 24rpx;
  .icon {
    margin-top: 6rpx;
  }
}
.address-info .icon {
  line-height: 36rpx;
  width: 22rpx;
  height: 22rpx;
}
.address-info view {
  flex: 1;
}
.act-list {
  display: flex;
  align-items: center;
  font-size: 20rpx;
}
.act-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 50rpx;
}
.bot-wrap {
  margin: 20rpx;
  padding: 0 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
}
</style>
