<template>
  <view class="bot-wrap people-counting">
    <view>当前 <text class="people-counting-num">{{ peopleNum }}</text> 人正在健身</view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useIndexBus } from '@/hooks/useIndexBus'
import { goUrlPage } from '@/utils/urlMap'

const props = defineProps<{
  config: Record<string, any>
}>()

const peopleNum = ref(0)
const getPeopleNum = (bus_id, merchant_type_get) => {
  return http
    .get('/Business/getBusRealtimeHeadCount', {
      bus_id,
      merchant_type_get,
      loading: false,
    })
    .then((res) => {
      peopleNum.value = res.data.head_count
    })
}

const { busId } = useIndexBus(props.config?.bus_id, getPeopleNum)

</script>
<style lang="scss">
.people-counting {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 24rpx;
}
.people-counting-num {
  color: $theme-text-color-other;
}
</style>
