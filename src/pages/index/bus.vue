<template>
  <view class="hastabbar">
    <!-- 如果不是BusInfo第一个渲染且为样式1的情况则需要占位-->
    <NavBar :show-bus-switch="setting.toggle_bus === '1'" :show-qr-code="setting.enter_voucher === '1'"  :fixed="!showNav" :left-width="600" />
    <template v-if="isConfigGet">
      <template v-for="(item, index) in themeStore.theme2.list" :key="index">
        <BusInfo v-if="item.temp_type == 1" :config="item" :index="index" :is-bus-page="true" />
        <CoachInfo v-if="item.temp_type == 2" :config="item" :index="index" />
        <CardInfo v-if="item.temp_type == 3" :config="item" :index="index" />
        <ActivityInfo v-if="item.temp_type == 4" :config="item" :index="index" />
        <PictureInfo v-if="item.temp_type == 5" :config="item" :index="index" :is-bus-page="true" />
        <NavInfo v-if="item.temp_type == 6" :config="item" :index="index" :is-bus-page="true" />
        <OpenClassInfo v-if="item.temp_type == 7" :config="item" :index="index" />
        <InvitationInfo v-if="item.temp_type == 9" :config="item" :index="index" />
        <TicketInfo v-if="item.temp_type == 10" :config="item" :index="index" />
        <SpaceInfo v-if="item.temp_type == 11" :config="item" :index="index" />
        <PackageInfo v-if="item.temp_type == 12" :config="item" :index="index" />
        <PayscoreInfo v-if="item.temp_type == 13" :config="item" :index="index" :is-bus-page="true" />
        <PeopleCounting v-if="item.temp_type == 14" :config="item" :index="index" />
        <ActivityRefund v-if="item.temp_type == 15" :config="item" :index="index" />
      </template>
    </template>
    <!-- 悬浮的红包入口 -->
    <RedBagFloat v-if="isConfigGet" styles="bottom: 70rpx" />
  </view>
</template>

<script setup lang="ts" name="index">
import BusInfo from './components/BusInfo.vue'
import PictureInfo from './components/PictureInfo.vue'
import CoachInfo from './components/CoachInfo.vue'
import CardInfo from './components/CardInfo.vue'
import PackageInfo from './components/PackageInfo.vue'
import NavInfo from './components/NavInfo.vue'
import PeopleCounting from './components/PeopleCounting.vue'
import ActivityInfo from './components/ActivityInfo.vue'
import OpenClassInfo from './components/OpenClassInfo.vue'
import TicketInfo from './components/TicketInfo.vue'
import SpaceInfo from './components/SpaceInfo.vue'
import NavBar from '@/components/NavBar.vue'
import InvitationInfo from './components/InvitationInfo.vue'
import PayscoreInfo from './components/PayscoreInfo.vue'
import { useLogin } from '@/hooks/useLogin'
import { useThemeStore } from '@/store/theme'
import { useUserStore } from '@/store/user'
import RedBagFloat from '@/components/RedBagFloat.vue'
import ActivityRefund from './components/ActivityRefund.vue'

const themeStore = useThemeStore()
const setting = computed(() => {
  return themeStore.theme2.setting || {}
})
const userStore = useUserStore()
const { checkLogin, getParam } = useLogin()
const showNav = ref(true)

const curOptions = reactive({
  bus_id: '',
  device_id: '',
  title: '', // 模板消息种类
  content: '', // 模板消息内容
  action: '',
})
const isConfigGet = ref(false)
async function init() {
  isConfigGet.value = false
  await themeStore.changeShowMerchantMode(false)
  themeStore.getConfig({ type: 2 }).then(() => {
    const { list } = themeStore.theme2
    isConfigGet.value = true
    showNav.value = !(
      list[0] &&
      (list[0].temp_type === 1 || list[0].temp_type === '1') &&
      (list[0].type === 1 || list[0].type === '1')
    )
  })
}
onLoad((options) => {
  Object.assign(curOptions, options)
})
onShow(async () => {
  const curParams = await getParam()
  if (curParams) {
    Object.assign(curOptions, curParams)
  }
  init()
})
onShareAppMessage((options) => {
  return {
    path: `/pages/index/bus?bus_id=${userStore.userInfoBusId}&from=share`,
  }
})
</script>

<style lang="scss">
// .page {
//   padding-bottom: 0; // tabBar页面不需要底部安全距离设置
// }
.bot-wrap {
  margin: 20rpx;
  padding: 0 20rpx;
  background: #ffffff;
  border-radius: 10rpx;
  overflow: hidden;
}
</style>
<style lang="scss" scoped>
.release-wrap {
  padding: 5rpx;
}
.train-over {
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 490rpx;
  box-sizing: border-box;
  padding: 0 30rpx 50rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  background: #fff;
  position: relative;
  .model-tit {
    font-size: 36rpx;
    margin-bottom: 30rpx;
    text-align: center;
  }
  .face {
    width: 161rpx;
    height: 127rpx;
    margin-top: -90rpx;
    margin-bottom: 30rpx;
    align-self: center;
  }
  .plus {
    font-size: 20rpx;
    margin-bottom: 30rpx;
    text {
      font-size: 48rpx;
      font-weight: bold;
      margin-right: 10rpx;
      margin-bottom: -5rpx;
    }
  }
  .items {
    width: 428rpx;
    margin: 0 autp;
    border: 1rpx solid #ffd066;
    border-radius: 6px;
    padding: 0 30rpx;
    box-sizing: border-box;
    .item {
      display: flex;
      justify-content: center;
      padding: 24rpx 0;
      justify-content: space-between;
      &:last-child {
        border-bottom: 0;
      }
      .key {
        white-space: nowrap;
      }
      .bold {
        font-weight: bold;
        max-width: 70%;
      }
    }
  }
  .close-view {
    width: 308rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    background: $theme-text-color-other;
    border-radius: 35rpx;
    margin: 36rpx auto 0;
    font-size: 30rpx;
    color: #000000;
  }
}
</style>
