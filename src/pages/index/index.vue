<template>
  <view class="hastabbar">
    <!-- 如果不是BusInfo第一个渲染且为样式1的情况则需要占位-->
    <NavBar :show-bus-switch="setting.toggle_bus === '1'" :show-qr-code="setting.enter_voucher === '1'" :fixed="!showNav" :left-width="600" />
    <template v-if="isConfigGet">
      <template v-for="(item, index) in themeStore.theme2.list" :key="index">
        <BusInfo v-if="item.temp_type == 1" :config="item" :index="index" />
        <CoachInfo v-if="item.temp_type == 2" :config="item" :index="index" />
        <CardInfo v-if="item.temp_type == 3" :config="item" :index="index" />
        <ActivityInfo v-if="item.temp_type == 4" :config="item" :index="index" />
        <PictureInfo v-if="item.temp_type == 5" :config="item" :index="index" />
        <NavInfo v-if="item.temp_type == 6" :config="item" :index="index" />
        <OpenClassInfo v-if="item.temp_type == 7" :config="item" :index="index" />
        <InvitationInfo v-if="item.temp_type == 9" :config="item" :index="index" />
        <TicketInfo v-if="item.temp_type == 10" :config="item" :index="index" />
        <SpaceInfo v-if="item.temp_type == 11" :config="item" :index="index" />
        <PackageInfo v-if="item.temp_type == 12" :config="item" :index="index" />
        <PayscoreInfo v-if="item.temp_type == 13" :config="item" :index="index" />
        <PeopleCounting v-if="item.temp_type == 14" :config="item" :index="index" />
        <ActivityRefund v-if="item.temp_type == 15" :config="item" :index="index" />
      </template>
    </template>

    <uni-popup ref="trainPopup" :is-mask-click="false">
      <view class="train-over theme-bg">
        <image class="face" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/modal-success.png" />
        <view v-if="modalType == 'signSuccess'">
          <view class="model-tit">签到成功</view>
          <template v-if="!signModelInfo.isBooking">
            <view v-if="signModelInfo.sign_class_list" class="items">
              <view class="item">
                <text class="key">签到时间</text>
                <text class="value bold">{{ signModelInfo.create_time_date }}</text>
              </view>
              <view v-for="item in signModelInfo.sign_class_list" :key="item.class_mark_id">
                <view class="item">
                  <text class="key">课程</text>
                  <text class="value">{{ item.class_name }}</text>
                </view>
                <view class="item">
                  <text class="key">时间</text>
                  <text class="value">{{ item.beg_time ? item.beg_time : item.b_time }}</text>
                </view>
              </view>
            </view>
            <view v-else class="items">
              <view class="item">
                <text class="key">时间</text>
                <text class="value bold">{{ signModelInfo.create_time_date }}</text>
              </view>
              <view class="item">
                <text class="key">卡种</text>
                <text class="value">{{ signModelInfo.card_name }}</text>
              </view>
              <view class="item">
                <text class="key">剩余</text>
                <text class="value"
                  >{{ signModelInfo.last_num
                  }}{{
                    signModelInfo.card_type == 1
                      ? '天'
                      : signModelInfo.card_type == 2
                      ? '次'
                      : signModelInfo.card_type == 3
                      ? '元'
                      : '节'
                  }}</text
                >
              </view>
            </view>
          </template>
        </view>
        <view v-if="modalType == 'opendoor'">
          <view class="model-tit">开门成功</view>
          <view class="items">
            <view v-for="(item, index) in qropenArr" :key="index" class="item">
              <text class="key">{{ item.paramName }}</text>
              <text class="value bold">{{ item.paramValue }}</text>
            </view>
          </view>
        </view>
        <!-- 模板消息进入 -->
        <view v-if="modalType == 'template'">
          <view class="model-tit">{{ curOptions.title }}</view>
          <view>
            {{ curOptions.content }}
          </view>
        </view>
        <view v-if="modalType == 'ebank'">
          <view class="model-tit">关联成功</view>
          <view class="model-content">扫设备上二维码进行训练吧</view>
        </view>
        <view v-if="modalType == 'ironmanLogin'">
          <view class="model-tit">连接成功</view>
          <view class="model-content">运动后可在训练记录中查看数据</view>
        </view>
        <view class="close-view" @tap="closeModal">
          {{ modalType == 'template' ? '知道了' : '关闭' }}
        </view>
      </view>
    </uni-popup>
  </view>
  <CustomTabsBar />
  <!-- 邀请奖励弹窗 -->
  <InvitationModal :type="2" />
  <!-- 悬浮的红包入口 -->
  <RedBagFloat v-if="isConfigGet" @hasRedBag="handleHasRedBag" />
  <ComeInModal v-if="showComeInMsg" :show="showComeInMsg" />
  <!-- 位置权限弹窗 -->
  <LocationPermissionDialog :show="showLocationDialog" :direction="direction" :distance="distance" @close="showLocationDialog = false" @enable="switchBus" />
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import BusInfo from './components/BusInfo.vue'
import PictureInfo from './components/PictureInfo.vue'
import CoachInfo from './components/CoachInfo.vue'
import CardInfo from './components/CardInfo.vue'
import PackageInfo from './components/PackageInfo.vue'
import NavInfo from './components/NavInfo.vue'
import PeopleCounting from './components/PeopleCounting.vue'
import ActivityInfo from './components/ActivityInfo.vue'
import OpenClassInfo from './components/OpenClassInfo.vue'
import TicketInfo from './components/TicketInfo.vue'
import SpaceInfo from './components/SpaceInfo.vue'
import CustomTabsBar from '@/components/custom-tabs-bar/index.vue'
import NavBar from '@/components/NavBar.vue'
import InvitationInfo from './components/InvitationInfo.vue'
import PayscoreInfo from './components/PayscoreInfo.vue'
import InvitationModal from '@/components/InvitationModal.vue'
import { useLogin } from '@/hooks/useLogin'
import { useThemeStore } from '@/store/theme'
import { useUserStore } from '@/store/user'
import RedBagFloat from '@/components/RedBagFloat.vue'
import ComeInModal from '@/components/ComeInModal.vue'
import ActivityRefund from './components/ActivityRefund.vue'
import LocationPermissionDialog from '@/components/LocationPermissionDialog.vue'

type stringPropObj = Record<string, any>
const showComeInMsg = ref(false)
const themeStore = useThemeStore()
const setting = computed(() => {
  return themeStore.theme2.setting || {}
})
const userStore = useUserStore()
const { checkLogin, getParam } = useLogin()
const showNav = ref(true)
const trainPopup = ref(null) as any
const signModelInfo = ref() as stringPropObj
const modalType = ref('')
const qropenArr = ref<stringPropObj[]>([])
const curOptions = reactive({
  bus_id: '',
  device_id: '',
  title: '', // 模板消息种类
  content: '', // 模板消息内容
  action: '',
})
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
const isConfigGet = ref(false)
// function checkShowMessage() {
//   // 每日首次进入显示showComeInMsg
//   const today = new Date().toISOString().split('T')[0];
//   const busId = userStore.userInfoBusId || '';
//   const visitKey = `${today}_${busId}`;

//   // 获取已访问列表
//   const visitedList = uni.getStorageSync('visitedList') || [];

//   // remove items before today
//   const filteredList = visitedList.filter(item => item.split('_')[0] === today);

//   if (filteredList.includes(visitKey)) {
//     showComeInMsg.value = false;
//   } else {
//     showComeInMsg.value = true;
//     filteredList.push(visitKey);
//     uni.setStorageSync('visitedList', filteredList);
//   }
// }
async function init() {
  isConfigGet.value = false
  showComeInMsg.value = false
  // 从其它页面返回到商家首页 设置为商家模式
  await themeStore.changeShowMerchantMode(true)
  checkLogin(false, curOptions.bus_id, true).then(() => {
    themeStore.getConfig({ type: 2, bus_id: themeStore.isShowMerchantMode ? '' : userStore.userInfoBusId || '' }).then(() => {
      const { list } = themeStore.theme2

      isConfigGet.value = true
      showNav.value = !(
        list[0] &&
        (list[0].temp_type === 1 || list[0].temp_type === '1') &&
        (list[0].type === 1 || list[0].type === '1')
      )
      handleTemplateAction()
      handleSignAction()
      // checkShowMessage()
      nextTick(() => {
        showComeInMsg.value = true
      })
      curOptions.bus_id = ''
    })
  })
}
onLoad((options) => {
  Object.assign(curOptions, options)
})
onShow(async () => {
  const curParams = await getParam()
  if (curParams) {
    Object.assign(curOptions, curParams)
  }
  init()

  nextTick(() => {
    setTimeout(() => {
      showLocationDialog.value = userStore.getShowLocationDialog()
    }, 666)
  })
})
onShareAppMessage((options) => {
  return {
    path: `/pages/index/index?bus_id=${userStore.userInfoBusId}&from=share`,
  }
})

function handleTemplateAction() {
  if (curOptions.action === 'template' && !!curOptions.title) {
    modalType.value = 'template'
    curOptions.title = decodeURIComponent(curOptions.title)
    curOptions.content = decodeURIComponent(curOptions.content)
    openModal()
    curOptions.action = ''
  }
}

function handleSignAction() {
  signModelInfo.value = uni.getStorageSync('signInfo')
  if (signModelInfo.value) {
    uni.setStorageSync('signInfo', '')
    modalType.value = 'signSuccess'
    openModal()
  }
  if (curOptions.action === 'opendoor' && !!curOptions.device_id) {
    openDoor()
  } else if (curOptions.from === 'ironmanLogin') {
    modalType.value = 'ironmanLogin'
    tirenDeviceLogin()
  } else if (curOptions.from === 'ebank') {
    modalType.value = 'ebank'
    tirenUserRelation()
  }
}

// 铁人设备用户首次通过其它小程序过来关联我们用户
function tirenUserRelation() {
  checkLogin(true, curOptions.bus_id).then((info) => {
    curOptions.from = ''
    Object.assign(loginUserInfo, info)
    http
      .post('User/bindEbankUser', {
        bus_id: loginUserInfo.bus_id,
        user_id: loginUserInfo.user_id,
        from: 'ebank',
        usermsg: curOptions.usermsg,
      })
      .then((res) => {
        openModal()
      })
      .catch((err) => {
        uni.navigateTo({
          url: `/pages/train/signResult?errorcode=${err.errorcode}&errormsg=${err.errormsg}&action=ebank`,
        })
      })
  })
}

// 铁人设备上扫我们二维码登录设备
function tirenDeviceLogin() {
  checkLogin(true, curOptions.bus_id).then((info) => {
    curOptions.from = ''
    Object.assign(loginUserInfo, info)
    http
      .post(
        'Api/Ironman/qrCodeLogin',
        { bus_id: loginUserInfo.bus_id, user_id: loginUserInfo.user_id, deviceKey: curOptions.deviceKey },
        'saasUrl'
      )
      .then((res) => {
        console.log(res)
        openModal()
      })
      .catch((err) => {
        uni.navigateTo({
          url: `/pages/train/signResult?errorcode=${err.errorcode}&errormsg=${err.errormsg}&action=ironmanLogin`,
        })
      })
  })
}

const direction = ref('none')
const distance = ref(0)
// const small = ref(false)
const handleHasRedBag = (val) => {
  if (val.hasRedBag) {
    direction.value = 'up'
    distance.value = 190
    // small.value = val.small
  } else {
    direction.value = 'none'
    distance.value = 0
    // small.value = val.small
  }
}
const showLocationDialog = ref(false)
function openDoor() {
  checkLogin(true, curOptions.bus_id).then((info) => {
    curOptions.action = ''
    Object.assign(loginUserInfo, info)
    http
      .post('Vein/Hardware/get_location_conf', { busId: loginUserInfo.bus_id }, 'saasUrl')
      .then(async (res) => {
        if (res.is_bus_location_sign) {
          await userStore.getLocationInfo()
          showLocationDialog.value = userStore.getShowLocationDialog()
          openQrcodeguard()
        } else {
          openQrcodeguard()
        }
      })
      .catch(() => {
        openQrcodeguard()
      })
  })
}

function openQrcodeguard() {
  http
    .post(
      'Vein/Hardware/openQrcodeguard',
      {
        busId: loginUserInfo.bus_id,
        userId: loginUserInfo.user_id,
        deviceId: curOptions.device_id,
        lng: userStore.locationInfo.longitude,
        lat: userStore.locationInfo.latitude,
      },
      'saasUrl'
    )
    .then((res) => {
      // 二维码开门成功
      qropenArr.value = res.choosing_data
      modalType.value = 'opendoor'
      openModal()
    })
    .catch((err) => {
      // 二维码开门失败
      uni.navigateTo({
        url: `/pages/train/signResult?errorcode=${err.errorcode}&errormsg=${err.errormsg}&action=opendoor`,
      })
    })
}

async function openModal() {
  if (trainPopup.value) {
    trainPopup.value.open()
  } else {
    await nextTick()
    trainPopup.value.open()
  }
}
function closeModal() {
  trainPopup.value.close()
}

function switchBus() {
  uni.navigateTo({
    url: '/pages/busSelect',
  })
}
</script>

<style lang="scss">
// .page {
//   padding-bottom: 0; // tabBar页面不需要底部安全距离设置
// }
.bot-wrap {
  margin: 20rpx;
  padding: 0 20rpx;
  background: #ffffff;
  border-radius: 10rpx;
  overflow: hidden;
}
.comein-modal {
  .comein-box {
    border: 0 none !important;
    padding: 0 !important;
  }
}
</style>

<style lang="scss" scoped>
.release-wrap {
  padding: 5rpx;
}
.train-over {
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 490rpx;
  box-sizing: border-box;
  padding: 0 30rpx 50rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  background: #fff;
  position: relative;
  .model-tit {
    font-size: 36rpx;
    margin-bottom: 30rpx;
    text-align: center;
  }
  .model-content {
    text-align: center;
  }
  .face {
    width: 161rpx;
    height: 127rpx;
    margin-top: -90rpx;
    margin-bottom: 30rpx;
    align-self: center;
  }
  .plus {
    font-size: 20rpx;
    margin-bottom: 30rpx;
    text {
      font-size: 48rpx;
      font-weight: bold;
      margin-right: 10rpx;
      margin-bottom: -5rpx;
    }
  }
  .items {
    width: 428rpx;
    margin: 0 autp;
    border: 1rpx solid #ffd066;
    border-radius: 6px;
    padding: 0 30rpx;
    box-sizing: border-box;
    .item {
      display: flex;
      justify-content: center;
      padding: 24rpx 0;
      justify-content: space-between;
      &:last-child {
        border-bottom: 0;
      }
      .key {
        white-space: nowrap;
      }
      .bold {
        font-weight: bold;
        max-width: 70%;
      }
    }
  }
  .close-view {
    width: 308rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    background: $theme-text-color-other;
    border-radius: 35rpx;
    margin: 36rpx auto 0;
    font-size: 30rpx;
    color: #000000;
  }
}
</style>
