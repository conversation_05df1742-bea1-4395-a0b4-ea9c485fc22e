<template>
  <NavBar />
  <view
    class="login-wrap"
    :class="!showAgreementBtn ? '' : 'agreement-wrap'"
    :style="{ backgroundImage: `url(${loginBg})`, backgroundSize: 'cover' }"
  >
    <template v-if="showAgreementBtn">
      <view class="logo">
        <image :src="logoImg" mode="scaleToFill" />
      </view>
      <view class="agreement-text">
        根据相关政策，在您使用之前我们诚恳的告诉您，为了确保会员服务的正常运行，我们需要使用您的部分信息，请查看并同意软件<navigator
          url="/packageMy/agreement/user"
          hover-class="none"
          class="link-text"
          >《最终用户许可协议》</navigator
        >及<navigator class="link-text" url="/packageMy/agreement/private" hover-class="none">《隐私政策》</navigator>。
      </view>
      <view v-if="busProtocol.length" class="agreement-text">
        为了您更清晰的了解场馆及提升您的会员体验，请您查看
        <navigator
          v-for="item in busProtocol"
          :key="item.bus_id"
          hover-class="none"
          class="link-text"
          :url="`/packageMy/agreement/busProtocol?bus_id=${item.bus_id}`"
          >《{{ item.bus_protocol_name }}》</navigator
        >
      </view>

      <view>
        <button class="normal-btn login-btn" style="background: var(--THEME-COLOR)" @tap="loginReader">
          我已阅读并同意
        </button>
        <view class="code-login" @tap="loginUnReader">不同意</view>
      </view>
    </template>
    <template v-if="!showAgreementBtn">
      <view class="logo" :class="loginType === 'code' ? 'align' : ''">
        <image :src="logoImg" mode="scaleToFill" />
      </view>
      <view class="login-main">
        <view v-if="loginType === 'phone'">
          <button
            v-if="!hasReadAgreement"
            class="normal-btn login-btn"
            style="background: var(--THEME-COLOR)"
            @tap="checkIsReadAgreement"
          >
            一键登录
          </button>
          <button
            v-else
            class="normal-btn login-btn"
            style="background: var(--THEME-COLOR)"
            open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumber"
          >
            一键登录
          </button>
          <view class="code-login" @tap="loginTypeChange">验证码登录</view>
        </view>
        <view v-if="loginType === 'code'">
          <view class="input-wrap">
            <input v-model="postData.phone" placeholder="请输入手机号码" placeholder-class="input-pla" type="number" />
          </view>
          <view class="input-wrap">
            <input
              v-model="postData.sms_code"
              class="code-in"
              placeholder="请输入验证码"
              placeholder-class="input-pla"
              type="number"
            />
            <button class="btn-code" :disabled="codeDisabled" @tap="getCode">{{ codeText }}</button>
          </view>
          <button class="normal-btn login-btn" style="background: var(--THEME-COLOR)" @tap="doLogin">登 录</button>
          <view class="code-login" @tap="loginTypeChange">授权手机号登录</view>
        </view>
      </view>
      <checkbox-group class="checkbox-group" @change="checkboxChange">
        <checkbox class="cb-transform" value="1" :checked="hasReadAgreement" />
        我已阅读并同意<navigator url="/packageMy/agreement/private" class="link-text" hover-class="none"
          >《隐私政策》</navigator
        >
        <navigator class="link-text" url="/packageMy/agreement/user" hover-class="none">《最终用户许可协议》</navigator>
        <template v-if="busProtocol.length">
          <navigator
            v-for="item in busProtocol"
            :key="item.bus_id"
            hover-class="none"
            class="link-text"
            :url="`/packageMy/agreement/busProtocol?bus_id=${item.bus_id}`"
            >《{{ item.bus_protocol_name }}》</navigator
          >
        </template>
      </checkbox-group>
    </template>
  </view>
</template>

<script setup lang="ts" name="login">
import NavBar from '@/components/NavBar.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useTimeInterval } from '@/hooks/useTimeInterval'
import { useThemeStore } from '@/store/theme'
import { useMerchant } from '@/store/merchant'
import { goUrlPage, aesEncrypt } from '@/utils'
import Auth from '@/utils/auth'

const { setTimeInterval, disButton, codeText, codeDisabled } = useTimeInterval()
const userStore = useUserStore()
const backUrl = ref('')
const loginBg = ref('https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg')
const logoImg = ref('https://imagecdn.rocketbird.cn/minprogram/member/image/logomin.png')
const navigateBack = ref(false)
const themeStore = useThemeStore()
const useMerchantStore = useMerchant()
const busProtocol = ref<
  { is_open_bus_protocol: number; bus_protocol_name: string; name: string; bus_id: string; thumb: string }[]
>([
  {
    is_open_bus_protocol: 0,
    bus_protocol_name: '',
    name: '',
    bus_id: '',
    thumb: '',
  },
])
onLoad((optios) => {
  backUrl.value = optios.backUrl || ''
  navigateBack.value = !!optios.navigateBack

  const operationMode = themeStore.operationMode
  let config = { type: 6 } as any
  if (operationMode === 0) {
    config = { type: 6, bus_id: userStore.userInfo.bus_id }
  }
  
  themeStore.getConfig(config).then(() => {
    loginBg.value =
      themeStore.theme6.background_type === '2'
        ? themeStore.theme6.background_img
        : 'https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg'
    logoImg.value = themeStore.theme6.bus_logo || logoImg.value
    busProtocol.value = (themeStore.theme6.bus_list || []).filter((v) => v.is_open_bus_protocol === 1)
  })
})
const loginType = ref('phone')
const showAgreementBtn = ref(false)
const hasReadAgreement = ref(false)
interface postObj {
  phone: string
  bus_id: string
  sms_code: string
  js_code: string
  phone_code: string
  unionid: string
  openid: string
  source_name: string
  [propName: string]: any
}
const postData: postObj = reactive({
  phone: '',
  bus_id: userStore.userInfo.bus_id,
  sms_code: '',
  js_code: '',
  phone_code: '',
  unionid: userStore.userInfo.unionid,
  openid: userStore.userInfo.openid,
  source_name: '',
})
const loginReader = () => {
  hasReadAgreement.value = true
  showAgreementBtn.value = false
}
function loginUnReader() {
  uni.switchTab({ url: '/pages/index/index' })
}
const checkboxChange = (e) => {
  hasReadAgreement.value = e.detail.value[0] || false
  showAgreementBtn.value = true
}
const loginTypeChange = () => {
  loginType.value = loginType.value === 'code' ? 'phone' : 'code'
}
function checkPhone() {
  if (!/^1\d{10}$/.test(postData.phone)) {
    uni.showToast({
      title: '手机号码不正确',
      icon: 'error',
    })
    return false
  }
  return true
}
function getPhoneNumber(e) {
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    // 基础库要求 >=2.21.2
    postData.phone_code = e.detail.code || ''
    if (postData.phone_code) {
      doLogin()
    } else {
      uni.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请使用验证码登录',
      })
    }
  } else {
    uni.showToast({
      title:
        e.detail?.errMsg === 'getPhoneNumber:fail user deny'
          ? '拒绝授权,登录失败！'
          : e.detail?.errMsg || '手机授权失败',
      icon: 'none',
    })
  }
}

function getCode() {
  if (checkPhone()) {
    setTimeInterval()
    http
      .get('/Public/sendSmsCode', {
        phone: aesEncrypt(postData.phone),
        openid: userStore.userInfo.openid,
      })
      .then((res) => {
        uni.showToast({
          title: res.errormsg,
        })
      })
      .catch((err) => {
        disButton()
      })
  }
}
function checkIsReadAgreement() {
  if (!hasReadAgreement.value) {
    showAgreementBtn.value = true
    return false
  }
  return true
}
async function doLogin() {
  if (!checkIsReadAgreement()) {
    return
  }
  const login: any = await uni.login({
    onlyAuthorize: true,
  })
  postData.js_code = login?.code || ''
  postData.phone = loginType.value === 'code' ? postData.phone : ''
  // 有介绍人ID则带上注册，介绍人ID来自转赠体验卡领取
  const introducer_id = uni.getStorageSync('introducer_id') // 介绍人ID
  if (introducer_id) postData.introducer_id = introducer_id
  // 营销红包引来的用户
  const source_name = uni.getStorageSync('source_name')
  if (source_name) postData.source_name = source_name

  if (postData.bus_id) postData.bus_id = userStore.userInfo.bus_id

  // 有带客活动id 和 邀请人id, 则带上注册。id来自邀请有礼分享
  const activity_id = uni.getStorageSync('activity_id') // 邀请有礼活动id
  const inviter_user_id = uni.getStorageSync('inviter_user_id') // 邀请人id
  if (activity_id && inviter_user_id) {
    postData.activity_id = activity_id
    postData.inviter_user_id = inviter_user_id
  }
  http
    .post('/User/authLogin', postData)
    .then(async (res) => {
      removeRegisterStorage()
      const info = res.data?.info
      Auth.setToken(info?.access_token || '')
      Auth.setRefreshToken(info?.refresh_token || '')
      userStore.setUserInfo(info)
      uni.showToast({
        title: res.errormsg,
      })
      // 重新获取场馆用户信息列表
      await useMerchantStore.requestBusList()
      successGo()
    })
    .catch((err) => {
      disButton()
    })
}

// 移除注册新场馆会员的部分缓存
function removeRegisterStorage() {
  // 移除用于注册使用的介绍人ID
  uni.removeStorageSync('introducer_id')
  // 移除用于注册使用的source_name
  uni.removeStorageSync('source_name')
  // 移除用于注册使用的邀请有礼id
  uni.removeStorageSync('activity_id')
  uni.removeStorageSync('inviter_user_id')
}
function successGo() {
  if (backUrl.value) {
    uni.redirectTo({
      url: backUrl.value,
    })
  } else if (navigateBack.value) {
    uni.navigateBack()
  } else {
    const pages = getCurrentPages()
    let prevPage = 'pages/index/index'
    if(pages && pages.length > 1) {
      prevPage = pages[pages.length - 2].route
    }
    uni.switchTab({
      url: '/' + prevPage,
    })
  }
}
</script>

<style lang="scss">
.checkbox-group {
  position: absolute;
  width: 90%;
  left: 50%;
  transform: translateX(-50%);
  bottom: 60rpx;
  text-align: center;
  color: #fff;
}
.agreement-wrap {
  .code-login {
    text-align: center;
  }
}
.agreement-text {
  width: 620rpx;
  margin: 0 auto;
  color: #fff;
  line-height: 1.75;
}
.under-white {
  display: inline-block;
  text-decoration: underline;
}
.under-text {
  display: inline-block;
  color: dodgerblue;
  text-decoration: underline;
}
.logo {
  display: block;
  width: 150rpx;
  height: 150rpx;
  margin: 300rpx auto 88rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
.align {
  margin-left: 86rpx;
}
.login-wrap {
  width: 100vw;
  height: 100vh;
  // background-image: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg');
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
  overflow: hidden;
}
.login-btn {
  width: 580rpx;
  border-radius: 40rpx;
  margin: 80rpx auto 0;
}
.code-login {
  text-align: right;
  color: #fff;
  font-size: 26rpx;
  margin: 25rpx 30rpx 0 0;
}
.login-main {
  width: 600rpx;
  margin: 88rpx auto;
}

.input-wrap {
  position: relative;
  width: 100%;
  border-bottom: 1rpx solid var(--THEME-COLOR);
  margin: 0 auto;
}
.input-wrap input {
  width: 100%;
  height: 106rpx;
  line-height: 106rpx;
  color: #fff;
  text-indent: 3rpx;
}

.input-pla {
  color: #bfbfc1;
}

.btn-code {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 182rpx;
  height: 62rpx;
  line-height: 62rpx;
  text-align: center;
  font-size: 26rpx;
  color: #fff;
  background: transparent;
  border: 1rpx solid var(--THEME-COLOR);
  border-radius: 30rpx;
}
.input-wrap .code-in {
  width: 255rpx;
}
</style>
