<template>
  <view class="card-detail-container footer-hasfixed theme-bg">
    <view class="card-item">
      <image class="card-img" :src="actInfo.card_thumb" mode="scaleToFill" />
      <view class="tit">
        {{ actInfo.card_name }}
      </view>
    </view>

    <view class="card-detail-wrap">
      <view class="form-items">
        <view class="item">
          <view class="label">
            <view v-if="actInfo.refund_condition == 1">首次入场不满{{ actInfo.refund_time }}分钟可退款</view>
            <view v-else>不支持退款</view>
            <view class="label-des">
              <view class="des-item" v-if="involvedInfo.enter_time">入场时间：{{ involvedInfo.enter_time }}</view>
              <view class="des-item" v-if="involvedInfo.leave_time">出场时间：{{ involvedInfo.leave_time }}</view>
              <view class="des-item" v-if="involvedInfo.duration">时长：{{ involvedInfo.duration }}</view>
              <view class="des-item other" v-if="actInfo.refund_condition == 1 && involvedInfo.refund_condition == 0">
                {{ involvedInfo.refund_condition_str || '不满足退款条件，不可发起退款' }}
              </view> 
            </view>
          </view>
        </view>

        <view class="item" v-if="actInfo.refund_condition == 1">
          <view class="label label-hastips">
            退款金额
            <view class="label-tips">1-7个工作日退款至原支付渠道</view>
          </view>
          <view class="value price"> ¥{{ actInfo.curr_cost }} </view>
        </view>

        <view class="item" v-if="actInfo.refund_condition == 1">
          <text class="label">退款原因</text>
          <picker
            :value="resultIdx"
            :range="resultArr"
            @change="
              (event) => {
                handleBuyMethodsChange(+event.detail.value)
              }
            "
          >
            <view class="value rig-sel"> {{ typeof resultIdx === 'undefined' ? '请选择' : resultArr[resultIdx] }}</view>
          </picker>
        </view>
        <view class="item" v-if="actInfo.refund_condition == 1">
          <view class="label label-hastips">
            提示
            <view class="label-tips">提交退款申请后，本会员卡将自动注销</view>
          </view>
        </view>
      </view>
    </view>
    <view class="fixed-bottom-wrap theme-bg" v-if="actInfo.refund_condition == 1">
      <view class="price-row">
        <view class="left-price"> ¥{{ actInfo.curr_cost }} </view>
        <view class="buttons custom">
          <button class="normal-btn" :disabled="involvedInfo.refund_condition == 0" @tap="handleRefund">
            申请退款
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="buyCardDetail">
import http from '@/utils/request'
import CardItem from '@/pages/card/components/CardItem'
import { useLogin } from '@/hooks/useLogin'
import { useCard } from './hooks/useCard'
import { useUserStore } from '@/store/user'

const { checkLogin } = useLogin()
const resultArr = ['已购买会籍卡', '计划有变，不想运动了', '买错了', '场馆人太多', '环境差', '器械太少', '器械质量差']
const actInfo = reactive({
  activity_id: '',
  card_name: '',
  name: '',
  curr_cost: '',
  ori_cost: '',
  refund_condition: '',
  refund_time: '',
})
const involvedInfo = reactive({
  enter_time: '',
  leave_time: '',
  duration: '',
  refund_condition: '',
  refund_condition_str: '',
  id: '',
})
const activityId = ref('')
const userStore = useUserStore()
function getInfo() {
  http
    .get('/Activity/getExperienceActivity', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      activity_id: activityId.value,
    })
    .then((res) => {
      const resData = res.data
      Object.assign(actInfo, resData.activity_info)
      Object.assign(involvedInfo, resData.activity_experience_involved_info)
    })
}

onLoad((options) => {
  activityId.value = options.activity_id
})
onShow(() => {
  checkLogin().then(() => {
    getInfo()
  })
})
const resultIdx = ref()
const handleBuyMethodsChange = (value) => {
  resultIdx.value = value
}
function handleRefund() {
  if (typeof resultIdx.value === 'undefined') {
    uni.showToast({
      title: '请选择退款原因',
      icon: 'none',
    })
    return
  }
  http
    .post('/Activity/refundExperienceActivity', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      activity_involved_id: involvedInfo.id,
      remark: resultArr[resultIdx.value],
    })
    .then((res) => {
      uni.showToast({
        title: '退款成功!',
        icon: 'none',
      })
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index',
        })
      }, 1000)
    })
}
</script>

<style lang="scss" scoped>
.card-detail-container {
  overflow-y: auto;
  box-sizing: border-box;
  padding-top: 20rpx;
  height: 100%;
}
.card-item {
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  border: 1rpx solid var(--THEME-COLOR);
  .card-img {
    width: 161rpx;
    height: 94rpx;
    flex-shrink: 0;
    margin-right: 20rpx;
  }
  .tit {
    overflow: hidden;
    margin-bottom: 13rpx;
    text-overflow: ellipsis;
    font-weight: bold;
    font-size: 30rpx;
    white-space: nowrap;
  }
}
.label-des {
  color: #888;
  margin-top: 20rpx;
  .des-item {
    margin-bottom: 10rpx;
    font-size: 24rpx;
  }
  .other {
    color: $theme-text-color-other;
  }
}
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.left-price {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  color: $theme-text-color-other;
}
.buttons.custom {
  width: 296rpx;
}
</style>
