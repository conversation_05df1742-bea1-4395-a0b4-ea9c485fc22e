<template>
  <view class="activity-list-container">
    <template v-if="state.activityList.length">
      <ActivityItem
        v-for="item in state.activityList"
        :key="item.id"
        :item="item"
        @click-item="handleToDetail"
      />
    </template>
    <view v-else class="nodata">暂无活动</view>
  </view>
</template>

<script setup lang="ts" name="activity">
import ActivityItem from './components/ActivityItem'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'

const userStore = useUserStore()
const { getParam } = useLogin()
const state = reactive({
  bus_id: '',
  user_id: '',
  bus_name: userStore.userInfo.bus_name,
  activityList: [],
})

onLoad(async (options) => {
  const curOptions = options.scene !== undefined ? await getParam(options.scene) : options

  state.bus_id = curOptions.bus_id || userStore.launchBusId || userStore.userInfoBusId
  state.user_id = curOptions.user_id || userStore.userInfoUserId || ''

  getActivityList()
})

onShareAppMessage((options) => {
  return {
    title: '活动进行中',
    path: `/pages/activity/index?bus_id=${state.bus_id}&from=share`,
  }
})

const getActivityList = () => {
  const params = {
    bus_id: state.bus_id,
    user_id: state.user_id,
  }
  http.get('Activity/getList', params).then((res) => {
    if (res.errorcode === 0) {
      const list = res.data?.list || []
      state.activityList = list
    }
  })
}

const handleToDetail = (item) => {
  uni.navigateTo({
    url: `/pages/activity/activityDetail?id=${item.id}&bus_id=${state.bus_id}`,
  })
}
</script>

<style lang="scss" scoped>
.activity-list-container {
  margin: 30rpx;
}
</style>
