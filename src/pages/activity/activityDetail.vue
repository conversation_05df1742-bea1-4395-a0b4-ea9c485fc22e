<template>
  <view class="activity-detail-container theme-bg">
    <view class="top-thumb-box">
      <image class="thumb-img" :src="detailInfo.thumb" mode="scaleToFill" />
    </view>
    <view class="title-row">
      <image
        v-if="detailInfo.is_top"
        class="hot-icon"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/hot.png"
        mode="scaleToFill"
      />
      <text class="title">{{ detailInfo.name }}</text>
    </view>
    <view class="deadline-row" :class="{ 'is-disabled': detailInfo.sign_status !== 0 }">
      <ThemeIcon
        class="icon-mr"
        type="t-icon-shijian"
        :size="10"
        :color="detailInfo.sign_status === 0 ? '#FF7427' : '#7d7d7d'"
      />
      <text class="tips-text">{{ signStatusText }}</text>
      <text v-if="countDownState.isShow" class="deadline-text">
        <text>距{{ detailInfo.type === 3 ? '抢购' : '报名' }}结束还有</text>
        <text class="num">{{ countDownState.d }}</text>
        <text>天</text>
        <text class="num">{{ countDownState.h }}</text>
        <text>时</text>
        <text class="num">{{ countDownState.m }}</text>
        <text>分</text>
        <text class="num">{{ countDownState.s }}</text>
        <text>秒</text>
      </text>
    </view>

    <view class="table-info-box">
      <view class="box-tit">
        活动详情
        <view v-if="pointTips" class="rig">
          <uni-icons type="star-filled" size="10" color="#FF7427"></uni-icons>
          {{ pointTips }}
          <uni-icons type="star-filled" size="10" color="#FF7427"></uni-icons>
        </view>
      </view>
      <view class="content-wrap">
        <view v-if="detailInfo.type === 2" class="item">
          <view class="label">报名费用</view>
          <view class="value price">{{ detailInfo.signup_cost }} 元</view>
        </view>
        <view class="item">
          <view class="label">活动时间</view>
          <view class="value">{{ detailInfo.activity_time }}</view>
        </view>
        <view class="item">
          <view class="label">{{ detailInfo.type === 3 ? '抢购' : '报名' }}截止时间</view>
          <view class="value">{{ detailInfo.deadline }}</view>
        </view>
        <view v-if="[1, 2].includes(detailInfo.type)" class="item">
          <view class="label">报名人数</view>
          <view class="value">
            {{ `${detailInfo.about_number - detailInfo.surplus}/${detailInfo.about_number}` }}
            人
          </view>
        </view>
      </view>
    </view>

    <view v-if="detailInfo.type == 3" class="card-info-box">
      <view class="box-tit">会员卡</view>
      <view v-if="tagList.length" class="tag-view">
        <uni-data-checkbox
          v-model="tagCheckValue"
          mode="tag"
          :selected-color="`#${themeStore.theme1.fashion_color}`"
          :localdata="tagList"
        ></uni-data-checkbox>
      </view>
      <view v-if="detailInfo.sell_cards?.length">
        <CardItem
          v-for="(card, cardIndex) in detailInfo.sell_cards"
          v-show="
            tagCheckValue === 0 ||
            (tagCheckValue === 1
              ? [1, 2, 3].includes(card.card_type_id)
              : card.card_type_id === tagCheckValue)
          "
          :key="cardIndex"
          :item="card"
          @click-card="handleToCard($event, 1)"
        >
          <template #middle>
            <view class="des">
              <text>{{ card.card_content }}</text>
            </view>
          </template>
          <template #bottom>
            <view class="bot">
              <text class="price">￥{{ card.curr_cost }}</text>
              <text class="total">￥{{ card.ori_cost }}</text>
            </view>
          </template>
          <template #right>
            <button
              class="normal-btn"
              :class="{ disabled: card.can_buy_status !== 1 || !canIBuy(card) }"
              @tap.stop="handleToCard(card, 2)"
            >
              抢购
            </button>
          </template>
        </CardItem>
      </view>
    </view>

    <view class="desc-info-box">
      <view class="box-tit">活动介绍</view>
      <view class="info-des rich-text">
        <rich-text :nodes="detailInfo.description"></rich-text>
      </view>
    </view>

    <view v-if="detailInfo.type == 3" class="notice-info-box">
      <view class="box-tit">抢购须知</view>
      <view class="content-wrap">
        <view class="item">
          <image
            class="notice-icon"
            src="https://imagecdn.rocketbird.cn/minprogram/uni-member/activity-notice-user-icon.png"
            mode="scaleToFill"
          />
          <view class="notice-info">
            <view class="title">购买用户</view>
            <text>{{ objLimit }}</text>
          </view>
        </view>
        <view class="item">
          <image
            class="notice-icon"
            src="https://imagecdn.rocketbird.cn/minprogram/uni-member/activity-notice-car-icon.png"
            mode="scaleToFill"
          />
          <view class="notice-info">
            <view class="title">购买数量</view>
            <text>{{ numLimit }}</text>
          </view>
        </view>
      </view>
    </view>
    <canvas v-show="canvasShow" style="width: 750px; height: 1334px" canvas-id="myCanvas"></canvas>
  </view>

  <view class="fixed-bottom-wrap theme-bg">
    <view class="buttons">
      <button
        :class="[
          'normal-btn',
          [1, 2].includes(detailInfo.type) ||
          (detailInfo.type === 4 && detailInfo.buy_card_way_has_online)
            ? 'outer-org mgr'
            : 'friend',
        ]"
        @tap="handleSaveDom"
      >
        即刻分享
      </button>
      <template v-if="[1, 2].includes(detailInfo.type)">
        <button
          class="normal-btn"
          :class="{ disabled: detailInfo.sign_status !== 0 }"
          :loading="loading"
          @tap="handleSignActivity"
        >
          立即报名
        </button>
      </template>
      <button
        v-if="detailInfo.type === 4 && detailInfo.buy_card_way_has_online"
        class="normal-btn"
        :disabled="detailInfo.isOutTime"
        @tap="handleToCardPage"
      >
        查看活动卡种
      </button>
    </view>
  </view>
</template>

<script setup lang="ts" name="activityDetail">
import _ from 'lodash'
import CardItem from '@/pages/card/components/CardItem'
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import {
  formatDate,
  unescapeHTML,
  getCardTypeUnit,
  savePhotosAlbum,
  shareCanvasInPhoto,
} from '@/utils'

const userStore = useUserStore()
const themeStore = useThemeStore()
const { checkLogin, getParam } = useLogin()
// type 1 一般活动 2 付费活动 3 购卡活动 4 积分活动

const activityId = ref('')
const curOptions = ref()
const busId = ref('')
const detailInfo = ref({})
const SIGN_STATUSES = {
  0: ['报名中', '抢购中'],
  1: '您已报过名了',
  2: ['报名已截止', '抢购已截止'],
  3: '已报满',
  4: '活动已结束',
  5: '活动已关闭',
  6: '仅限新用户参加',
  7: '仅限会员参加',
  8: '抢购中',
  9: '已购卡',
  10: '已售罄',
  11: '活动未开始',
}
// 报名状态文本显示及提示
const signStatusText = computed(() => {
  const info = unref(detailInfo)
  const text = SIGN_STATUSES[info.sign_status]
  if (Array.isArray(text)) {
    return info.type === 3 ? text[1] : text[0]
  }
  return text
})

/* 报名倒计时相关 */
let timer = null
const countDownState = reactive({
  isShow: false,
  d: 0,
  h: 0,
  m: 0,
  s: 0,
})
// 处理报名倒计时
function countDownFn(deadline) {
  let date = new Date(deadline.replace(/\u5e74|\u6708/g, '/').replace(/\u65e5/, ''))
  date = date instanceof Date && !isNaN(date.getTime()) ? date : new Date()

  clearInterval(timer)
  timer = setInterval(() => {
    const timeStamp = date - new Date()

    if (timeStamp > 0) {
      const leave1 = timeStamp % (24 * 3600 * 1000)
      const leave2 = leave1 % (3600 * 1000) // 计算小时数后剩余的毫秒数
      const leave3 = leave2 % (60 * 1000) // 计算分钟数后剩余的毫秒数
      countDownState.d = (Math.floor(timeStamp / (24 * 3600 * 1000)) + '').padStart(2, '0')
      countDownState.h = (Math.floor(leave1 / (3600 * 1000)) + '').padStart(2, '0')
      countDownState.m = (Math.floor(leave2 / (60 * 1000)) + '').padStart(2, '0')
      countDownState.s = (Math.floor(leave3 / 1000) + '').padStart(2, '0')
    } else {
      clearInterval(timer)
      timer = null
      countDownState.isShow = false
      if (unref(detailInfo).sign_status === 0) {
        detailInfo.value.sign_status = 2
      }
    }
  }, 1000)
}

onLoad((options) => {
  curOptions.value = options
})

// 购卡完成后返回，重新获取活动数据
onShow(async () => {
  curOptions.value = curOptions.value.scene !== undefined ? await getParam(curOptions.value.scene || '') : curOptions.value
  activityId.value = curOptions.value.id
  busId.value = curOptions.value.bus_id || userStore.launchBusId || userStore.userInfoBusId
  getActivityDetail()
})

onUnload(() => {
  uni.$emit('refresh-activity') // 刷新主页热门活动的报名人数等
})

// sign_status 报名状态， 0 未报名 1已报名 2报名已截止
async function getActivityDetail() {
  setLoading(false)
  const { user_id } = await checkLogin(false, busId.value)
  const params = {
    activity_id: activityId.value,
    bus_id: busId.value,
    user_id: user_id || '',
  }
  http.get('Activity/getDetail', params).then((res) => {
    if (res.errorcode === 0) {
      const { data } = res
      if (data.type == 4) {
        data.buy_card_way_has_online = data.buy_card_way?.includes(0) // 赠积分活动卡课，是否支持线上购买 [0支持线上, 1支持线下]
        data.isOutTime =
          new Date() >
          new Date(data.deadline.replace('年', '-').replace('月', '-').replace('日', ''))
      }
      detailInfo.value = {
        ...data,
        description: data.description ? unescapeHTML(data.description) : '暂无描述',
      }
      // 处理报名倒计时
      if (
        ([1, 2].includes(data.type) && data.sign_status === 0) ||
        data.type === 3 ||
        (data.type === 4 && !data.isOutTime)
      ) {
        countDownState.isShow = true
        countDownFn(data.deadline)
      }

      if (
        data.share_pic === 'https://imagecdn.rocketbird.cn/minprogram/member/image/share-bg1.jpg'
      ) {
        shareBgUrlType.value = 1
      } else if (
        !data.share_pic ||
        data.share_pic === 'https://imagecdn.rocketbird.cn/minprogram/member/image/share-bg.jpg'
      ) {
        shareBgUrlType.value = 2
      } else {
        shareBgUrlType.value = 3
      }
    }
  })
}

/* 分享相关 */
const shareBgUrlType = ref(1)
const canvasShow = ref(false)

function getShareInfo() {
  return http.get('Activity/shareActivityInfo', { activity_id: activityId.value }).then((res) => {
    if (res.errorcode === 0) {
      return res.data
    }
  })
}

function ctxDetail1(ctx, infoObj, res) {
  ctx.drawImage(res.tempFilePath, 534, 1033, 180, 180)
  ctx.setFillStyle('#535353')
  ctx.setTextAlign('center')
  ctx.setFontSize(28)
  ctx.fillText(infoObj.name, 80, 155)
  ctx.setTextAlign('right')
  ctx.setFontSize(60)
  ctx.fillText(detailInfo.value.name, 702, 222)
  ctx.setFontSize(36)
  ctx.fillText(detailInfo.value.activity_time, 702, 315)
  ctx.setFontSize(26)
  ctx.setTextAlign('left')
  ctx.fillText('TELL：' + infoObj.phone, 103, 1090)
  ctx.fillText('ADD ：' + infoObj.address, 103, 1143)
  ctx.draw(false, function () {
    shareCanvasInPhoto({
      width: 750,
      height: 1334,
      id: 'myCanvas',
      callBack() {
        canvasShow.value = false
      },
    })
  })
}

function ctxDetail2(ctx, infoObj, res) {
  ctx.drawImage(res.tempFilePath, 315, 990, 130, 130)
  ctx.setFillStyle('#4e499b')
  ctx.setTextAlign('center')
  let activityName = detailInfo.value.name
  if (activityName.length <= 4) {
    ctx.setFontSize(60)
  } else {
    if (activityName.length > 14) {
      activityName = activityName.substr(0, 14) + '...'
    }
    ctx.setFontSize(46)
  }
  ctx.fillText(activityName, 375, 863)
  ctx.setFillStyle('#ffffff')
  ctx.setFontSize(28)
  ctx.fillText(detailInfo.value.activity_time, 380, 950)
  ctx.setFillStyle('#757575')
  ctx.setFontSize(24)
  ctx.setTextAlign('left')
  ctx.fillText('电话：' + infoObj.phone, 222, 1237)
  ctx.fillText('地址：' + infoObj.address, 222, 1280)
  ctx.draw(false, function () {
    shareCanvasInPhoto({
      width: 750,
      height: 1334,
      id: 'myCanvas',
      callBack() {
        canvasShow.value = false
        uni.hideLoading()
      },
    })
  })
}
function drawShareBg(ctx, infoObj, res) {
  const codeImg = infoObj.qrcode
  if (shareBgUrlType.value === 1) {
    ctx.drawImage(res.tempFilePath, 30, 13, 100, 100)
  } else if (shareBgUrlType.value === 2) {
    ctx.drawImage(res.tempFilePath, 58, 23, 170, 170)
  }
  uni.downloadFile({
    url: codeImg,
    success: (res) => {
      if (res.statusCode === 200) {
        if (shareBgUrlType.value === 1) {
          ctxDetail1(ctx, infoObj, res)
        } else if (shareBgUrlType.value === 2) {
          ctxDetail2(ctx, infoObj, res)
        }
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({ title: '保存小程序码失败！', image: '/static/img/fail.png' })
    },
  })
}
const handleSaveDom = async (params) => {
  uni.showLoading({
    title: '正在获取分享图',
    mask: true,
  })

  if (shareBgUrlType.value === 3) {
    uni.downloadFile({
      url: detailInfo.value.share_pic,
      success: async function (res) {
        if (res.statusCode === 200) {
          try {
            await savePhotosAlbum(res.tempFilePath, uni.hideLoading)
          } catch (error) {
            uni.hideLoading()
            uni.navigateTo({
              url: '/pages/infoAuthor',
            })
          }
        }
      },
    })
  } else {
    canvasShow.value = true
    const infoObj = await getShareInfo()
    if (!infoObj) {
      canvasShow.value = false
      return false
    }
    const ctx = uni.createCanvasContext('myCanvas')
    if (shareBgUrlType.value === 1) {
      ctx.drawImage('../../static/img/share-bg1.jpg', 0, 0, 750, 1334)
    } else if (shareBgUrlType.value === 2) {
      ctx.drawImage('../../static/img/share-bg.jpg', 0, 0, 750, 1334)
    }
    const logoImg = infoObj.logo
    uni.downloadFile({
      url: logoImg,
      success: (res) => {
        if (res.statusCode === 200) {
          drawShareBg(ctx, infoObj, res)
        }
      },
      fail: function () {
        uni.showToast({ title: '保存logo失败！', image: '/static/img/fail.png' })
      },
    })
  }
}

/* 报名相关 */
const loading = ref(false)
const setLoading = (bool: boolean) => {
  loading.value = bool
}

/* 立即报名 type 1, 2 */
function pay(info) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success() {
      detailInfo.value.sign_status = 1
      setLoading(false)
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
        complete() {
          setLoading(false)
        },
      })
    },
  })
}
const handleSignActivity = _.throttle(async () => {
  if (unref(detailInfo).sign_status !== 0) {
    uni.showToast({
      title: signStatusText.value,
      icon: 'none',
    })
    return 'sign_status'
  }

  if (unref(loading)) {
    return 'loading'
  }
  setLoading(true)

  const { user_id } = await checkLogin(true, unref(busId))
  if (!user_id) {
    setLoading(false)
    return uni.showToast({
      title: '未拿到user_id',
      icon: 'none',
    })
  }

  const postData = {
    bus_id: unref(busId),
    user_id: user_id,
    activity_id: unref(activityId),
  }
  http
    .post('Activity/ActivitySign', postData)
    .then((res) => {
      if (res.errorcode === 0) {
        // const activityType = res.data.activity_type
        if (unref(detailInfo).type == 1) {
          uni.showToast({ title: res.errormsg, icon: 'success' })
          detailInfo.value.sign_status = 1
          detailInfo.value.surplus--
          setLoading(false)
        } else {
          // 2 付费活动
          pay(res.data.info)
        }
      } else if (res.errorcode === 40026) {
        detailInfo.value.sign_status = 1
        setLoading(false)
      } else {
        uni.showToast({
          title: res.errormsg,
          icon: 'error',
        })
        setLoading(false)
      }
    })
    .catch((err) => {
      setLoading(false)
      console.error(err)
    })
}, 2000)

/* 购卡活动相关 */
const tagList = ref([])
const tagCheckValue = ref(0) // 0全部 1会籍卡 4私教 5泳教
// 调整为v-show控制
/* const cardList = computed(() => {
  const { type, sell_cards } = detailInfo.value
  return type === 3 && sell_cards?.length
    ? tagCheckValue.value === 0
      ? sell_cards
      : sell_cards.filter((v) =>
          tagCheckValue.value === 1
            ? [1, 2, 3].includes(v.card_type_id)
            : v.card_type_id === tagCheckValue.value
        )
    : []
}) */
watch(
  () => detailInfo.value.sell_cards,
  (list) => {
    let types = []
    // 处理活动卡
    if (detailInfo.value.type === 3 && list?.length) {
      detailInfo.value.sell_cards.forEach((v) => {
        // 处理渲染参数
        const unit = getCardTypeUnit(v.card_type_id, v.is_pt_time_limit_card)
        const text_1 = `${
          v.card_type_id == 1
            ? v.end_time
            : [2, 3].includes(v.card_type_id)
            ? v.number
            : v.is_pt_time_limit_card === 1
            ?  v.number
            : v.pt_class_num
        }${unit}`
        const text_2 = `${
          [1, 2, 3].includes(v.card_type_id) && v.gift_number
            ? '+赠送' + v.gift_number + unit
            : [4, 5].includes(v.card_type_id) && v.mc_gift_number
            ? '+赠送' + v.mc_gift_number + unit
            : ''
        }`
        const text_3 = `剩余${v.stock_balance || 0}张`
        v.card_content = `${text_1}${text_2} | ${text_3}`

        types.push(+v.card_type_id)
      })

      types = [...new Set(types)]
      const hasMsCard = types.some((v) => v >= 1 && v <= 3)
      const hasPtCard = types.includes(4)
      const hasSwimCard = types.includes(5)
      const tags = []
      if ((hasMsCard && (hasPtCard || hasSwimCard)) || (hasPtCard && hasSwimCard)) {
        tags.push({ text: '全部', value: 0 })
        hasMsCard && tags.push({ text: '会籍卡', value: 1 })
        hasPtCard && tags.push({ text: '私教课', value: 4 })
        hasSwimCard && tags.push({ text: '泳教课', value: 5 })
        tagList.value = tags
      }
    }
  }
)
const OBJ_LIMIT = {
  0: '无限制',
  1: '仅限新用户可以购买',
  2: '仅限会员可以购买',
}
const NUM_LIMIT = {
  0: '无限制',
  1: '每种卡每人限购一张',
  2: '每人限购一张',
}
const pointTips = computed(() => {
  let tips = ''
  if (detailInfo.value.type == 4) {
    const wayName = { 0: '线上', 1: '线下' }
    tips = `活动期间在${detailInfo.value.buy_card_way
      .split(',')
      .map((v) => wayName[v] || '')
      .join('、')}购买可额外获赠积分`
  }
  return tips
})
const objLimit = computed(() => OBJ_LIMIT[detailInfo.value.buy_obj_limit])
const numLimit = computed(() => NUM_LIMIT[detailInfo.value.buy_num_limit])
const canIBuy = (card, showTips = false) => {
  // "card_id":6347, //卡ID
  // "card_name":"aaaaa", //卡名称
  // "card_type_id":2, //卡类型
  // "ori_cost":"400.00", //原价
  // "curr_cost":"200.00", //当前价
  // "sell_num_limit":5, //卡售卖设置的售卖总量
  // "pt_class_num":0, //私教课课时数
  // "card_use_tip":"", //卡使用时段 天数 等描述
  // "stock_balance":5, //当前剩余库存
  // "user_buy":0 //本次活动 当前用户 购买这张卡的数量
  // "can_buy_status" ：1  // 1可用购买  2已买过卡 3本卡售罄
  const { user_buy, stock_balance } = card
  const {
    sign_status,
    buy_num_limit,
    buy_obj_limit,
    user_is_efficient,
    user_buy_sum,
    can_buy_status,
  } = detailInfo.value
  function showToast(title: string) {
    showTips &&
      uni.showToast({
        icon: 'none',
        title,
      })
    return false
  }

  if (can_buy_status !== 1) {
    if (sign_status === 2) {
      return showToast(unref(signStatusText.value))
    }
    if (buy_obj_limit === 1 && user_is_efficient) {
      return showToast(OBJ_LIMIT[buy_obj_limit])
    }
    if (buy_obj_limit === 2 && !user_is_efficient) {
      return showToast(OBJ_LIMIT[buy_obj_limit])
    }
    if (buy_num_limit === 1 && user_buy >= 1) {
      return showToast(NUM_LIMIT[buy_num_limit])
    }
    if (buy_num_limit === 2 && user_buy_sum >= 1) {
      return showToast(NUM_LIMIT[buy_num_limit])
    }
    if (can_buy_status === 3 || stock_balance === 0 || stock_balance === undefined) {
      return showToast('会员卡已售罄')
    }
  }
  return true
}
const handleToCard = async (card, step: 1 | 2) => {
  const isOK = canIBuy(card, true)
  if (!isOK) return

  const { user_id, bus_id } = await checkLogin(true, unref(busId))
  if (user_id && bus_id) {
    const { card_id, card_type_id, ae_id } = unref(card)
    uni.navigateTo({
      url: `/pages/card/${
        step === 1 && [4, 5].includes(card_type_id) ? 'cardDetail' : 'buyCardDetail'
      }?card_id=${card_id}&activity_id=${detailInfo.value.id}&index=${ae_id}`,
    })
  }
}

// 查看活动卡课
function handleToCardPage() {
  uni.navigateTo({
    url: `/pages/card/pointCard?bus_id=${userStore.userInfoBusId}&activityId=${activityId.value}`,
  })
}
onShareAppMessage(() => {
  return {
    title: detailInfo.value.name,
    path: `/pages/activity/activityDetail?id=${detailInfo.value.id}&bus_id=${userStore.userInfoBusId}`,
  }
})
</script>

<style lang="scss" scoped>
.activity-detail-container {
  padding: 20rpx;
  padding-bottom: 178rpx;
  .top-thumb-box {
    margin: 0 10rpx;
    .thumb-img {
      display: block;
      box-sizing: border-box;
      border-radius: 20rpx;
      width: 100%;
      height: 400rpx;
      box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.05);
    }
  }
  .title-row {
    margin-top: 32rpx;
    margin-bottom: 26rpx;
    .hot-icon {
      margin-right: 8rpx;
      width: 62rpx;
      height: 22rpx;
    }
    .title {
      vertical-align: middle;
      font-weight: bold;
      font-size: 36rpx;
    }
  }
  .deadline-row {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    border-radius: 30rpx;
    height: 60rpx;
    background: rgba(255, 116, 39, 0.1);
    font-size: 24rpx;
    .icon-mr {
      margin-bottom: 4rpx;
    }
    .tips-text {
      font-weight: bold;
    }
    .deadline-text {
      margin-left: auto;
    }
    .num {
      display: inline-block;
      box-sizing: border-box;
      margin: 0 6rpx;
      padding: 0 5rpx;
      border-radius: 6rpx;
      min-width: 36rpx;
      height: 34rpx;
      background: $theme-text-color-other;
      text-align: center;
      font-weight: bold;
      color: #fff;
    }
    &.is-disabled {
      background: rgba(232, 232, 232, 1);
      .num {
        background: #7d7d7d;
      }
    }
  }
  .table-info-box,
  .desc-info-box,
  .notice-info-box {
    margin-top: 36rpx;
    .box-tit {
      font-size: 30rpx;
    }
  }
  .table-info-box {
    overflow: hidden;
    margin-top: 36rpx;
    padding: 0 20rpx;
    border-radius: 10rpx;
    background: #f6f6f8;
    .box-tit {
      .rig {
        color: $theme-text-color-other;
      }
    }
    .content-wrap {
      .item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 28rpx;
        &:last-child {
          margin-bottom: 32rpx;
        }
      }
      .price {
        font-weight: bold;
        color: $theme-text-color-other;
      }
    }
  }
  .card-info-box {
    .box-tit {
      font-size: 30rpx;
    }
    .tag-view {
      margin-bottom: 20rpx;
    }
    .bot {
      display: flex;
      align-items: center;
      .tag {
        margin-left: 10rpx;
      }
    }
    .des {
      margin-bottom: 13rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: $theme-text-color-grey;
    }
    .price {
      font-weight: bold;
      font-size: 30rpx;
      color: $theme-text-color-other;
    }
    .tag {
      margin-left: 10rpx;
      padding: 0 14rpx;
      border: 1rpx solid $theme-text-color-other;
      border-radius: 14rpx;
      height: 29rpx;
      line-height: 29rpx;
      text-align: center;
      font-size: 20rpx;
      color: $theme-text-color-other;
    }
    .unit {
      font-weight: normal;
      font-size: 26rpx;
    }
    .total {
      text-decoration: line-through;
      font-size: 20rpx;
      color: $theme-text-color-grey;
    }
    .normal-btn {
      border-radius: 10rpx;
      width: 110rpx;
      height: 60rpx;
      line-height: 60rpx;
      font-size: 24rpx;
    }
  }
  .desc-info-box {
    margin: 0 10rpx;
    .info-des {
      line-height: 1.7;
      font-size: 24rpx;
    }
  }
  .notice-info-box {
    margin: 0 10rpx;
    .content-wrap {
      padding: 30rpx;
      border: 1rpx solid #dedee0;
      border-radius: 20rpx;
    }
    .item {
      display: flex;
      &:last-child {
        margin-top: 30rpx;
      }
    }
    .notice-icon {
      margin-right: 24rpx;
      width: 64rpx;
      height: 64rpx;
    }
    .notice-info {
      font-size: 24rpx;
      .title {
        font-weight: bold;
      }
    }
  }
}
.fixed-bottom-wrap {
  .buttons {
    justify-content: space-between;
  }
}

[data-theme='dark'] {
  .activity-detail-container {
    background-color: #000 !important;
    .deadline-row {
      background: #0f0f0f;
    }

    .table-info-box {
      background: #0f0f0f;
      color: #fff;
    }
  }
}
</style>
