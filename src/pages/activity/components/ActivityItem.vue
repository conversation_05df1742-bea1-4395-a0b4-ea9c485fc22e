<template>
  <view
    class="activity-wrap"
    :style="{ background: `url(${props.item.thumb}) center/cover no-repeat` }"
    @tap.stop="handleClick"
  >
    <view class="info-box">
      <image
        class="hot-icon"
        v-if="item.is_top"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/hot-2.png"
      />
      <view class="name">{{ props.item.name }}</view>
      <view class="date">{{ date }}</view>
    </view>
    <view class="activity-mask"></view>
  </view>
</template>

<script setup lang="ts" name="activityItem">
import { formatDate } from '@/utils/shared'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
})

const emits = defineEmits(['click-item'])

const date = computed(() => {
  const { beg_date, end_date } = props.item
  return `${formatDate(new Date(beg_date), 'yyyy年MM月dd日')}~${formatDate(
    new Date(end_date),
    'yyyy年MM月dd日'
  )}`
})

const handleClick = () => {
  emits('click-item', props.item)
}
</script>

<style lang="scss" scoped>
.activity-wrap {
  overflow: hidden;
  position: relative;
  margin-bottom: 15rpx;
  border-radius: 20rpx;
  width: 100%; // 690rpx
  height: 400rpx;
  background-color: #000;
  // box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
  .info-box {
    position: absolute;
    left: 28rpx;
    right: 28rpx;
    bottom: 42rpx;
    z-index: 1;
  }
}
.hot-icon {
  width: 82rpx;
  height: 34rpx;
}
.name {
  overflow: hidden;
  margin: 10rpx 0;
  max-height: 96rpx;
  line-height: 1.2;
  text-shadow: 0 3px 5px rgba(0, 0, 0, 0.5);
  font-weight: 500;
  font-size: 40rpx;
  color: #fff;
}
.date {
  text-shadow: 0 3px 5px rgba(0, 0, 0, 0.5);
  font-size: 24rpx;
  color: #fff;
}
.activity-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
  background-color: rgba(0, 0, 0, 0.1);
}
</style>
