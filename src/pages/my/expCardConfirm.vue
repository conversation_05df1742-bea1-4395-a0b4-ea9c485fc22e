<template>
  <PageExperienceCard :card-info="cardInfo" position="会籍顾问" @get-card="handleSubmitClick" />
</template>

<script setup lang="ts" name="expCardConfirm">
import PageExperienceCard from '@/pages/card/components/PageExperienceCard'
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'

const { checkLogin } = useLogin()
const sceneId = ref('')
const cardInfo = ref({})

onLoad((options) => {
  sceneId.value = decodeURIComponent(options.scene)
  getInfo()
})

function getInfo() {
  http
    .get('Depositexpcard/confirmExpcard', {
      scene_id: sceneId.value,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        cardInfo.value = res.data
      }
    })
}

const handleSubmitClick = () => {
  // uni.setStorageSync('introducer_id', cardInfo.value.ms_id)

  checkLogin(true, cardInfo.value.bu_id).then((userInfo) => {
    http
      .post('Depositexpcard/sendExpcard', {
        scene_id: sceneId.value,
        // bus_id: userInfo.bus_id,
        user_id: userInfo.user_id,
      })
      .then((res) => {
        if (res.errorcode === 0) {
          uni.redirectTo({
            url: '/pages/my/card',
            success: uni.hideLoading,
          })
        }
      })
  })
}
</script>
