<template>
  <custom-tabs :model-value="current" @change="tabChange">
    <template #right>
      <MerchantBusPick
        v-if="chooseLeaveCard !== undefined"
        v-model="busUserInfo.bus_id"
        :is-select-default="chooseLeaveCard"
        :disabled="chooseLeaveCard"
        @change="handleBusChange"
      />
    </template>
    <custom-tab-pane v-for="(tab, index) in tabList" :key="index" :label="tab.label">
      <view class="card-page theme-bg">
        <view v-if="current == 2" class="card-tips"> 合同签署完成后以下会员卡才能正常使用 </view>
        <view
          v-for="item in (current == 1 ? overdueList : current == 2 ? waitList : cardList) as any"
          :key="item.uc_id"
          class="card-item"
        >
          <view class="card-info" :class="{ excard: item.experience_card == 1, notuse: current == 1 }">
            <view class="right-btn-wrap">
              <!-- 次卡拆分一次送朋友 -->
              <button
                v-if="item.member_share === 1 && current == 0 && !chooseLeaveCard"
                class="right-btn"
                @tap="handleShareCard(item)"
              >
                送朋友
              </button>
              <!-- 送朋友 -->
              <button
                v-if="item.is_receive === '0' && current == 0 && !chooseLeaveCard"
                class="right-btn"
                open-type="share"
                :data-bus_id="item.bus_id"
                :data-user_id="item.user_id"
                :data-ucid="item.uc_id"
                :data-elid="item.el_id"
              >
                送朋友
              </button>
              <button v-if="chooseLeaveCard && item.can_suspend" class="right-btn" @tap="handleChooseCard(item)">
                选择
              </button>
              <button
                v-if="item.remark === '未激活' && current != 2"
                class="right-btn"
                @tap="handleActiveCard(item)"
              >
                激活
              </button>
            </view>
            <view class="card-top"
              ><text class="card-name">{{ item.name }}</text>
              <text v-if="item.initial" class="card-init">(初始{{ item.initial }})</text></view
            >
            <view v-if="item.remark === '未激活' && current != 2" class="status-des">
              <uni-icons type="info-filled" size="20" color="#ff7427"></uni-icons>激活后才正式生效
            </view>
            <view v-else-if="item.remark" class="status">{{ item.remark }}</view>
            <view class="card-top-row">
              <text class="card-type">{{ item.experience_card == 1 ? '体验卡' : cardTypeIds[item.card_type_id] }}</text>
              <text v-if="item.user_number > 1" class="card-member">已添加{{ item.user_number }}个成员</text>
            </view>
            <view class="card-des">
              <view class="card-num">{{ item.card_sn ? 'NO. ' + item.card_sn : '' }}</view>
              <view class="overplus"
                >剩余 <text>{{ item.surplus }}</text></view
              >
            </view>
            <view class="card-bottom-row">
              <view class="left-btn" @tap="handleTapCard(item)">
                <text class="drop-down">{{ item.uc_id !== cardId ? '会员卡详情' : '收起' }}</text>
              </view>
              <view>{{ item.expiry_date }}</view>
            </view>
          </view>

          <view class="card-info-more box-shadow" :hidden="item.uc_id !== cardId">
            <view class="more-row">
              <text class="label">归属门店</text>
              <text class="content">{{ item.bus_name }}</text>
            </view>
            <view class="more-row">
              <text class="label">获取时间</text>
              <text class="content">{{ item.buy_time }}</text>
            </view>
            <view class="more-row">
              <text class="label">获取方式</text>
              <text class="content">{{ item.card_source || '-' }}</text>
            </view>
            <view v-if="curVolumeInfo" class="more-row">
              <text class="label">购买剩余</text>
              <text class="content">{{ curVolumeInfo.purchase_surplus }}</text>
            </view>
            <view v-if="curVolumeInfo" class="more-row">
              <text class="label">赠送剩余</text>
              <text class="content">{{ curVolumeInfo.gift_surplus }}</text>
            </view>
          </view>
        </view>
        <view
          v-if="
            current == 1
              ? overdueList && overdueList.length == 0
              : current == 2
              ? waitList && waitList.length == 0
              : cardList && cardList.length == 0
          "
          class="nodata"
          >暂无对应卡课数据</view
        >
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import { useUserStore } from '@/store/user'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import { useLogin } from '@/hooks/useLogin'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { goUrlPage } from '@/utils/urlMap'
import { formatDate, addDays, dateDiff } from '@/utils/shared'

const tabList = ref([
  { value: 1, label: '可用' },
  { value: 2, label: '不可用' },
  { value: 3, label: '待签署' },
])
const { checkLogin } = useLogin()
const userStore = useUserStore()
const isRequest = ref(true)
const cardTypeIds = reactive({
  '1': '期限卡',
  '2': '次数卡',
  '3': '储值卡',
  '4': '私教卡',
  '5': '泳教卡',
})
const cardList = ref([])
const overdueList = ref([])
const waitList = ref([])
const current = ref(0)
const cardId = ref(0)
const chooseLeaveCard = ref()
const instanceEventChannel = ref()
const hasBusDataGet = ref(false)
const busUserInfo = reactive({
  bus_id: '',
  user_id: '',
})

onLoad((options) => {
  instanceEventChannel.value = (getCurrentInstance()?.proxy as any).getOpenerEventChannel()
  chooseLeaveCard.value = options.chooseLeaveCard === 'true'
})
onShow(() => {
  checkLogin()
})

// // 处理: 离开前上一个路由不是"我的"那么将会返回至"我的"
// onUnload(() => {
//   const pages = getCurrentPages()
//   const page = pages[pages.length - 2]
//   const path = page.route
//   if (path && path !== '/pages/my/index') {
//     uni.switchTab({
//       url: '/pages/my/index',
//     })
//   } else {
//     uni.navigateBack()
//   }
// })

onShareAppMessage((options) => {
  if (options.from === 'button') {
    return {
      title: '送您一张体验卡',
      imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/member/image/share_gift_card.png',
      path: `/pages/bonuses/giftCard?bus_id=${options.target.dataset.bus_id}&user_id=${options.target.dataset.user_id}&ucid=${options.target.dataset.ucid}&elid=${options.target.dataset.elid}&from=share`,
    }
  }
})

function handleChooseCard(item) {
  instanceEventChannel.value.emit('acceptDataFromOpenedPage', { data: item })
  uni.navigateBack()
}
function handleShareCard(item) {
  goUrlPage(`/packageMy/cardshare/list?card_user_id=${item.card_user_id}&bus_id=${item.bus_id}`)
}

const getInfo = () => {
  if (!busUserInfo.user_id) {
    cardList.value = []
    return Promise.resolve()
  }
  return http
    .get('/Personalcenter/getCardList', {
      ...busUserInfo,
      is_overdue: current.value,
      loading: true,
    })
    .then((res) => {
      const list = res.data
      list.forEach((t) => {
        t.description = t.description.replace(/\r\n|\n|\r/g, '  ')
      })
      if (current.value === 1) {
        overdueList.value = list
        isRequest.value = false
      } else {
        cardList.value = list
      }
      cardId.value = list[0] ? list[0].uc_id : 0
      getCardUserVolume(cardId.value, list[0])
      return list
    })
}

// 获取待签署
const getWaitInfo = () => {
  return http
    .get('/Personalcenter/orderDisableCardList', {
      ...busUserInfo,
      is_overdue: current.value,
      loading: true,
    })
    .then((res) => {
      const list = res.data || []
      list.forEach((t) => {
        t.description = t.description.replace(/\r\n|\n|\r/g, '  ')
      })
      waitList.value = list
      cardId.value = list[0] ? list[0].uc_id : 0
      getCardUserVolume(cardId.value, list[0])
      return list
    })
}

function handleBusChange({ bus_id, user_id }) {
  busUserInfo.bus_id = bus_id
  busUserInfo.user_id = user_id
  if (current.value === 2) {
    getWaitInfo()
  } else {
    getInfo().then(() => {
      hasBusDataGet.value = true
    })
  }
}

function tabChange(e) {
  current.value = e.value
  if (hasBusDataGet.value) {
    if (e.value === 0 && cardList.value.length === 0) {
      getInfo()
    } else if (e.value === 1 && overdueList.value.length === 0) {
      getInfo()
    } else if (e.value === 2 && waitList.value.length === 0) {
      getWaitInfo()
    }
  }
}

function handleTapCard(info) {
  cardId.value = cardId.value === info.uc_id ? 0 : info.uc_id
  getCardUserVolume(cardId.value, info)
}

const curVolumeInfo = ref()
function getCardUserVolume(id, info) {
  if (!id) {
    return
  }
  const { bus_id, user_id, card_user_id } = info
  http
    .get('/Personalcenter/getCardUserVolume', {
      bus_id,
      user_id,
      card_user_id,
      loading: true,
    })
    .then((res) => {
      curVolumeInfo.value = res.data
    })
}
function handleActiveCard(info) {
  const { card_user_id, bus_id, user_id, active_end_time } = info
  const startDay = formatDate(new Date(), 'yyyy-MM-dd')
  uni.showModal({
    title: '确认激活？',
    content: `有效期：${startDay} ~ ${active_end_time || ''}`,
    success: (res) => {
      if (res.confirm) {
        http
          .post('Personalcenter/enableCard', {
            card_user_id,
            bus_id,
            user_id,
            loading: true,
          })
          .then((res) => {
            uni.showToast({
              icon: 'success',
              title: '激活成功',
            })
            getInfo()
          })
      }
    },
  })
}
</script>
<style lang="scss">
.card-page {
  padding-top: 20rpx;
  min-height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.card-item {
  position: relative;
  margin: 0 auto 26rpx;
  width: 691rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  .card-info {
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
  }
  .card-info-more {
    margin: 0 auto 11rpx;
    padding: 10rpx 30rpx 26rpx;
    width: 644rpx;
    border: 1px solid $theme-text-color-other;
    border-top-width: 0;
    border-radius: 0 0 4rpx 4rpx;
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    .more-row {
      display: flex;
      margin-top: 28rpx;
      line-height: 28rpx;
      font-size: 24rpx;
      .label {
        margin-right: 36rpx;
        min-width: 124rpx;
        color: $theme-text-color-grey;
      }
      .content {
        flex: 1;
        text-align: right;
      }
    }
  }
  .status {
    top: 10rpx;
  }
  .status-des {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 20rpx;
    text-align: right;
    font-weight: bold;
    color: $theme-text-color-other;
    display: flex;
    align-items: center;
  }
  .right-btn-wrap {
    position: absolute;
    top: 66rpx;
    right: 30rpx;
  }
  .right-btn {
    width: 130rpx;
    height: 50rpx;
    line-height: 50rpx;
    background: $theme-text-color-other;
    border-radius: 10rpx;
    margin: 0;
    font-weight: bold;
    font-size: 24rpx;
    color: #fff;
    margin-bottom: 10rpx;
  }
}
.card-tips {
  text-align: center;
  font-size: 24rpx;
  color: $theme-text-color-other;
  height: 32rpx;
  line-height: 32rpx;
  margin-bottom: 20rpx;
}
</style>
