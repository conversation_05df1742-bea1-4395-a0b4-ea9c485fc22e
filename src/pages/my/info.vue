<template>
  <view v-if="type == 'fromLogin'" class="top-tips">
    <text @tap="goIndex">跳过</text>
  </view>
  <view class="vip-wrap theme-bg">
    <uni-icons type="vip" size="30"></uni-icons>
  </view>
  <view class="form-items theme-bg" :class="type == 'fromLogin' ? '' : 'info-form'">
    <view v-if="type == 'fromLogin'" class="item">
      <view class="label">真实姓名</view>
      <input v-model="postData.username" class="item-input" placeholder="请填写" />
    </view>
    <view v-else class="item">
      <view class="label">昵称</view>
      <input v-model="postData.nickname" class="item-input" placeholder="请填写" />
    </view>
    <view class="item">
      <view class="label">性别</view>
      <view class="value">
        <picker :value="postData.sex" :range="genderList" @change="handleGenderChange">
          <view class="value rig-sel">
            {{ genderList[postData.sex - 1] || '请选择' }}
          </view>
        </picker>
      </view>
    </view>
    <view class="item">
      <view class="label">出生日期</view>
      <view class="value">
        <picker
          mode="date"
          :value="postData.birthday || '1995-06-15'"
          :end="today"
          @change="bindDateChange"
        >
          <view class="value rig-sel">
            {{ postData.birthday || '请选择' }}
          </view>
        </picker>
      </view>
    </view>
  </view>
  <view class="form-items theme-bg info-form">
    <view class="item">
      <view class="label">入场智能提醒</view>
      <switch :checked="postData.user_get_guide_pop" @change="handleSwitch"/>
    </view>
  </view>
  <view class="fixed-bottom-out" @click="logout">
    退出登录
  </view>
  <view class="fixed-bottom-wrap theme-bg">
    <button class="normal-btn" @tap="updateInfo">确认</button>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { useThemeStore } from '@/store/theme'
import Auth from '@/utils/auth'
const themeStore = useThemeStore()
const { checkLogin } = useLogin()
const genderList = ref(['男', '女'])

const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
const type = ref('')
const postData = reactive({
  username: '',
  nickname: '',
  user_get_guide_pop: true,
  sex: 1,
  birthday: '',
})
function bindDateChange(e) {
  postData.birthday = e.detail.value
}
onLoad((options) => {
  type.value = options.type
})
onShow(() => {
  checkLogin().then((info) => {
    Object.assign(loginUserInfo, info)
    getInfo()
  })
})
const getInfo = () => {
  http.get('User/getUserInfo', { ...loginUserInfo }).then((res) => {
    const info = res.data?.info || {}
    Object.assign(postData, {
      ...info,
      user_get_guide_pop: info.user_get_guide_pop === 0,
    })
  })
}
function handleSwitch(e) {
  postData.user_get_guide_pop = e.detail.value
}
function goIndex() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
const updateInfo = () => {
  http.post('User/updateUserInfo', {
     ...loginUserInfo, 
     ...postData, 
     user_get_guide_pop:  postData.user_get_guide_pop ? 0 : 1
  }).then((res) => {
    uni.showToast({
      icon: 'success',
      title: '更新成功',
    })
    setTimeout(() => {
      if (type.value == 'fromLogin') {
        goIndex()
      } else {
        uni.navigateBack()
      }
    }, 1000)
  })
}
function handleGenderChange(e) {
  postData.sex = parseInt(e.detail.value) + 1
}
function logout() {
  uni.showModal({
    title: '提示',
    content: '确认退出？',
    success: (res) => {
      if (res.confirm) {
        http.post('User/authLoginOut', {
          user_id: loginUserInfo.user_id
        }).then((res) => {
          uni.showToast({
            icon: 'success',
            title: '退出成功',
          })
          Auth.logout()
        }).catch(() => {
          uni.showToast({
            icon: 'none',
            title: '退出失败',
          })
        }) 
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.info-form {
  margin-top: 40rpx;
}
.top-tips {
  text-align: right;
  height: 80rpx;
  line-height: 80rpx;
  padding-right: 20rpx;
  color: $theme-text-color-other;
}
.vip-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx auto;
  width: 100rpx;
  height: 80rpx;
  border-radius: 10rpx;
}
.fixed-bottom-out {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: calc(160rpx + env(safe-area-inset-bottom));
  color: #666;
  text-decoration: underline;
  text-align: center;
  font-size: 24rpx;
}
</style>
