<template>
  <ticket-inner v-if="newOptions.from === 'index'" :from="newOptions.from" :busId="busId" />
  <custom-tabs v-else :model-value="current" @change="tabChange">
    <template #right>
      <MerchantBusPick
        v-model="busId"
      />
    </template>
    <custom-tab-pane v-for="item in tabList" :id="item.type" :key="item.type" :label="item.label"
      :number="item.type === '1' ? canUseNumber : 0">
      <view v-if="computedId !== ''" class="card-page">
        <!-- 会员凭证页面 -->
        <view v-if="computedId === '1'" style="height: 100%">
          <ticket-inner :from="newOptions.from" :busId="busId" />
        </view>

        <!-- 商品凭证页面 -->
        <view v-if="computedId === '2'" style="height: 100%">
          <!-- 筛选按钮 -->
          <view class="filter-tabs">
            <view v-for="filter in filterList" :key="filter.value"
              :class="['filter-tab', { active: currentFilter === filter.value }]"
              @tap="handleFilterChange(filter.value)">
              {{ filter.label }} <text v-if="voucherListCount > 0 && filter.value === '0'">({{ voucherListCount }})</text>
            </view>
          </view>

          <!-- 商品凭证列表 -->
          <view class="voucher-list">
            <!-- 自定义空状态提示 -->
            <view v-if="voucherList.length === 0" class="empty-tips">
              <image class="empty-image" src="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png" mode="aspectFit" />
              <text class="empty-text">暂无相关凭证</text>
            </view>
            
            <z-paging 
              ref="ticketPaging" 
              v-model="voucherList" 
              :fixed="false" 
              :refresher-enabled="false"
              :show-loading-more-no-more-view="voucherList.length > 10 ? true : false"
              :empty-view-img-style="{ width: '103rpx', height: '144rpx' }" 
              empty-view-text="暂无核销数据"
              empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png">
              <view v-for="voucher in voucherList" :key="voucher.consumption_log_id" class="voucher-item">
                <view class="voucher-content">
                  <image :src="voucher.commodity_img" class="voucher-image" mode="aspectFill" />
                  <view class="voucher-info">
                    <view class="voucher-title">{{ voucher.commodity_name || '无名商品' }}
                    </view>
                    <view class="voucher-quantity">数量 x {{ voucher.purchase_count || 0 }} {{ voucher.unit }}</view>
                    <view class="voucher-date">{{ voucher.create_time }}</view>
                  </view>
                </view>
                <view class="voucher-bottom">
                  <view class="voucher-store">门店：{{ voucher.bus_name }}</view>
                  <view class="voucher-action">
                    <image class="qrcode" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/qrcode-oth.png"
                      alt="qrcode" />
                    <view class="action-btn" @tap="goUrlPage(`/pages/my/ticketDetail-new?sanLogId=${voucher.consumption_log_id}`)">
                      查看商品凭证
                    </view>
                  </view>
                </view>
              </view>
            </z-paging>
          </view>
        </view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import TicketInner from './ticket-inner.vue'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { goUrlPage } from '@/utils'

const tabList = ref([
  { type: '1', label: '入场凭证' },
  { type: '2', label: '商品凭证' },
])

const filterList = ref([
  { value: '0', label: '待核销' },
  { value: '1', label: '已核销' },
])

const computedId = ref('')
const { checkLogin } = useLogin()
const current = ref(0)
const currentFilter = ref('0')
const userStore = useUserStore()
const busId = ref('')

onShow(() => {
  checkLogin()
})

const canUseNumber = ref(0)

const newOptions = ref()
onLoad((options) => {
  newOptions.value = options

  if (newOptions.value.tabIndex === '1') {
    computedId.value = '2'
    current.value = 1
  }
})

function tabChange(e: any) {
  current.value = e.value
  computedId.value = e.computedId

  if (computedId.value === '2') {
    getVoucherList()
  }
}

function handleFilterChange(filterValue: string) {
  currentFilter.value = filterValue
  getVoucherList()
}

const ticketPaging = ref()
const voucherList = ref<any[]>([])
const voucherListCount = ref(0)
function getVoucherList() {
  http
    .get('/Good/getVoucherList', {
      bus_id: busId.value || userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      consume_status: currentFilter.value,
    })
    .then(async (res) => {
      const pagingInstance = Array.isArray(ticketPaging.value) ? ticketPaging.value[0] : ticketPaging.value
      if (!pagingInstance || typeof pagingInstance.setLocalPaging !== 'function') {
        await nextTick()
      }

      pagingInstance.reload()
      pagingInstance.setLocalPaging(res.data.list)

      if (currentFilter.value === '0') {
        voucherListCount.value = res.data.list.length
      }
    })
    .catch(async () => {
      const pagingInstance = Array.isArray(ticketPaging.value) ? ticketPaging.value[0] : ticketPaging.value
      if (!pagingInstance || typeof pagingInstance.setLocalPaging !== 'function') {
        await nextTick()
      }

      pagingInstance.setLocalPaging([], false)
    })
}
</script>

<style lang="scss" scoped>
.card-page {
  padding-top: 20rpx;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.normal-btn-min {
  width: 500rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;

  &.center {
    justify-content: center;
  }
}

.text-ellipsis {
  display: inline-block;
  margin: 10rpx;
  max-width: 600rpx;
  width: 600rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 筛选标签样式
.filter-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tab {
  margin-right: 20rpx;
  transition: all 0.3s ease;
  padding: 0 10rpx;
  min-width: 128rpx;
  height: 50rpx;
  background: #E7E7E7;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 24rpx;
  color: #000000;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    background-color: var(--THEME-COLOR);
    color: black;
    font-weight: bold;
  }
}

// 商品凭证列表样式
.voucher-list {
  height: 100%;
}

// 空状态提示样式
.empty-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  
  .empty-image {
    width: 103rpx;
    height: 144rpx;
    margin-bottom: 20rpx;
  }
  
  .empty-text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    text-align: center;
  }
}

.voucher-item {
  width: 710rpx;
  // height: 220rpx;
  background: #FFFFFF;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  margin: 20rpx auto;
}

.voucher-content {
  display: flex;
  align-items: flex-start;
}

.voucher-image {
  width: 140rpx;
  height: 92rpx;
  background: #E7E7E7;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin: 44rpx 24rpx 34rpx 24rpx;
  flex-shrink: 0;
}

.voucher-info {
  flex: 1;
  min-width: 0;
}

.voucher-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 32rpx;
  color: #000000;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 40rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.voucher-quantity {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #7D7D7D;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 6rpx;
}

.voucher-date {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #7D7D7D;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 6rpx;
}

.voucher-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx 190rpx;
}

.voucher-store {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #7D7D7D;
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}

.voucher-action {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .qrcode {
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
  }
}

.action-btn {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #FF7427;
  text-align: left;
  font-style: normal;
  text-transform: none;
  position: relative;
  display: flex;
  align-items: center;

  &::after {
    content: '';
    width: 0;
    height: 0;
    border-left: 10rpx solid #FF7427;
    border-top: 10rpx solid transparent;
    border-bottom: 10rpx solid transparent;
    margin-left: 15rpx;
  }
}
</style>