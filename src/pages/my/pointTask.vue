<template>
  <NavBar />
  <view class="box">
    <view class="my-point-box">
      <view class="label-row">
        <image class="point-icon" :src="`${path}point.png`" />
        <text>当前积分</text>
      </view>
      <view class="detail-row">
        <text class="point-num">{{ currentPoint }}</text>
      </view>
    </view>
    <view class="box-body">
      <scroll-view v-if="task" class="list" scroll-y="true" enable-flex>
        <view v-if="task.sign" class="item">
          <view>
            <image class="left-icon-img" :src="`${path}sign.png`" />
          </view>
          <view class="item-info">
            <text class="title">每日到场签到训练</text>
            <view class="num-row">
              <image class="icon-point" :src="`${path}point.png`" />
              <text class="point">+{{ task.sign.rule_num }}</text>
            </view>
          </view>
          <view style="width: 132rpx"> </view>
        </view>
        <view v-if="task.course_schedule" class="item">
          <view>
            <image class="left-icon-img" :src="`${path}class.png`" />
          </view>
          <view class="item-info">
            <text class="title">每日参与团操课训练</text>
            <view class="num-row">
              <image class="icon-point" :src="`${path}point.png`" />
              <text class="point"> +{{ task.course_schedule.point.min }}~{{ task.course_schedule.point.max }}/节</text>
            </view>
            <view v-if="task.course_schedule.max_limit" class="tips">
              根据课种进行奖励，每日最多奖励 {{ task.course_schedule.max_limit }} 节
            </view>
          </view>
          <view>
            <button class="to-detail-btn" @tap="handleNavigate(task.course_schedule.action)">详情</button>
          </view>
        </view>
        <view v-if="task.pt_schedule" class="item">
          <view>
            <image class="left-icon-img" :src="`${path}pt.png`" />
          </view>
          <view class="item-info">
            <text class="title">每日进行私教课训练</text>
            <view class="num-row">
              <image class="icon-point" :src="`${path}point.png`" />
              <text class="point">+{{ task.pt_schedule.point.min }}~{{ task.pt_schedule.point.max }}/节</text>
            </view>
            <view class="tips">根据课种进行奖励</view>
          </view>
          <view>
            <button class="to-detail-btn" @tap="handleNavigate(task.pt_schedule.action)">详情</button>
          </view>
        </view>
        <view v-if="task.space" class="item">
          <view>
            <image class="left-icon-img" :src="`${path}space.png`" />
          </view>
          <view class="item-info">
            <text class="title">每日进行场地预订</text>
            <view class="num-row">
              <image class="icon-point" :src="`${path}point.png`" />
              <text class="point">+{{ task.space.point.min }}~{{ task.space.point.max }}/节</text>
            </view>
            <view class="tips">根据场地类型和订场时间进行奖励</view>
          </view>
          <view>
            <button class="to-detail-btn" @tap="handleNavigate(task.space.action)">详情</button>
          </view>
        </view>
      </scroll-view>
      <view v-else class="nodata">暂无数据</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="point">
import NavBar from '@/components/NavBar.vue'
import { usePoint } from '@/hooks/usePoint'
import { useUserStore } from '@/store/user'
import http from '@/utils/request'

const userStore = useUserStore()
const { currentPoint, getUserPoint } = usePoint()
const task = ref()
const path = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/point/'
getUserPoint()
getPointRule()

function getPointRule() {
  http
    .get('/Point/getPointRule', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      task.value = res.data
    })
}
function handleNavigate(action) {
  uni.navigateTo({
    url: `/pages/my/pointTaskDetail?action=${action}`,
  })
}
</script>
<style lang="scss" scoped>
.my-point-box {
  height: 274rpx;
  padding: 140rpx 0 0 40rpx;
  box-sizing: border-box;
  font-size: 26rpx;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/point/top.png') top right/contain no-repeat;
  .label-row {
    .point-icon {
      padding-right: 5rpx;
      width: 24rpx;
      height: 24rpx;
    }
    text {
      font-size: 24rpx;
    }
  }
  .detail-row {
    margin-top: 17rpx;
    height: 50rpx;
    .point-num {
      font-size: 50rpx;
      font-weight: bold;
    }
  }
}

.box-body {
  background-color: #f5f7f9;
  .list {
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    width: 100%;
    height: auto;
    box-sizing: border-box;
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 27rpx;
    min-height: 150rpx;
    background-color: #fff;
    border-radius: 10rpx;
    .left-icon-img {
      width: 80rpx;
      height: 80rpx;
    }
    .item-info {
      position: relative;
      padding: 0 24rpx;
      flex: 1;
      font-size: 24rpx;
      .title {
        font-size: 30rpx;
      }
      .num-row {
        display: flex;
        align-items: center;
        margin-top: 10rpx;
      }
      .icon-point {
        margin-right: 12rpx;
        width: 24rpx;
        height: 24rpx;
      }
      .tips {
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #aaa;
        white-space: nowrap;
      }
    }
    .to-detail-btn {
      width: 112rpx;
      height: 50rpx;
      line-height: 48rpx;
      text-align: center;
      font-size: 24rpx;
      color: $theme-text-color-other;
      border-radius: 25rpx;
      border: 1rpx solid $theme-text-color-other;
    }
  }
}
</style>
