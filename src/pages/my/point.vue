<template>
  <NavBar />
  <view class="box-head">
    <view class="point-box">
      <text>可用积分</text>
      <view class="point-num">{{ currentPoint }}</view>
    </view>
    <view class="point-box">
      <text>30天内到期积分</text>
      <view class="point-num">{{ expPoint }}</view>
    </view>
  </view>
  <view>
    <custom-tabs :value="0" center>
      <custom-tab-pane label="积分获取">
        <PointList :type="1" />
      </custom-tab-pane>
      <custom-tab-pane label="积分扣除">
        <PointList :type="2" />
      </custom-tab-pane>
      <custom-tab-pane label="过期积分">
        <PointList :type="3" />
      </custom-tab-pane>
    </custom-tabs>
  </view>
</template>

<script setup lang="ts" name="point">
import NavBar from '@/components/NavBar.vue'
import customTabs from '@/components/custom-tabs/custom-tabs'
import customTabPane from '@/components/custom-tabs/custom-tab-pane'
import PointList from './components/PointList.vue'
import { usePoint } from '@/hooks/usePoint'
const { currentPoint, getUserPointAndExpire, expPoint } = usePoint()
onShow(() => {
  getUserPointAndExpire()
})
</script>
<style lang="scss" scoped>
.box-head {
  height: 304rpx;
  box-sizing: border-box;
  padding-top: 149rpx;
  background: #fdf1d0;
  display: flex;
  align-items: center;

  .point-box {
    flex: 1;
    text-align: center;
    font-size: 24rpx;
    .point-num {
      margin-top: 17rpx;
      font-size: 50rpx;
      font-weight: bold;
    }
  }
}
.point-tab-wrap {
  height: 100%;
}
</style>
