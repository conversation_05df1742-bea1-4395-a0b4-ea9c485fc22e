<template>
  <view v-if="order" :class="showQRCode ? 'footer-hasfixed' : ''">
    <view class="success-tips">
      <text>{{ order.bus_name }}</text>
    </view>
    <view class="form-items theme-bg mt20">
      <!-- <view class="item">
        <view class="label">订单编号</view>
        <view class="value">{{ orderSn }}</view>
      </view> -->
      <view class="item">
        <view class="label">时间</view>
        <view class="value">{{ order.create_time }}</view>
      </view>
      <view class="item">
        <view class="label">商品</view>
        <view class="value">{{ order.commodity_name || '无名商品' }} x {{ order.purchase_count }} {{ order.unit }}</view>
      </view>
      <!-- <view class="item">
        <view class="label">场地</view>
        <view class="value" @tap="handleSpaceName(sanRuleId, order.space_name)">
          <view class="value-overtext">{{
            order.space_name && order.space_name.length ? order.space_name : '入场'
          }}</view>
          <view v-if="sanRuleId && order.space_name" class="arrow-right"></view>
        </view>
      </view> -->
      <!-- <view v-if="!showQRCode" class="item">
        <view class="label">入场人数</view>
        <view class="value">{{ order.max_join_people }}人</view>
      </view> -->

      <!-- <view v-if="adUnitId" class="banner">
        <image class="banner-img" :src="adUnitId" alt="banner" />
      </view> -->

      <!-- <view v-if="!showQRCode" class="qrcode">
        <view class="subtitle">剩余名额 {{ order.invitable_num }} 人</view>
        <view v-if="order.status == 3" class="label">订场已结束</view>
        <view v-else-if="order.status != 1 && order.status != 2" class="label">预订场次已取消</view>
        <view
          v-else-if="!hasTicket && order.invitable_num > 0"
          class="normal-btn friend normal-btn-min mt20"
          @tap="handleGetTicket"
          >领取凭证</view
        >
        <view v-else-if="!hasTicket && order.invitable_num === 0" class="label">凭证已领完</view>
        <view v-else-if="hasTicket" class="label">你已经领取过凭证</view>
      </view> -->
      <view v-if="hasTicket && !showQRCode" class="link" style="margin-top: 60rpx" bindtap="getQRCode">查看凭证详情</view>
      <view v-if="showQRCode" class="qrcode">
        <view class="subtitle">请凭二维码核销提货</view>
        <view class="code">{{ consumeSn }}</view>
        <image :class="qrcodeValid == 0 ? 'picture' : 'pic-false'" :src="qrcode" />
        <view v-if="qrcodeValid == 1" class="text-false">已核销</view>
        <!-- 只有待核销才能退票 -->
        <!-- <view class="link" v-if="qrcodeValid == 1 && order.ticket_type == 1 && order.return_status == 1 && order.cancel_member" @tap="handleReturnTicket">退票</view>
        <view v-if="available" class="link" @tap="handleCancelTicket">放弃凭证</view>
        <view v-else class="link"></view> -->
      </view>
    </view>
    <view v-if="showQRCode" class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <button class="normal-btn outer-org mgr" open-type="share" v-if="canShare">分享朋友</button>
        <navigator class="normal-btn" open-type="redirect" url="/pages/my/ticket?tabIndex=1">完成</navigator>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import { useSpace } from '@/hooks/useSpace'
const { handleSpaceName } = useSpace()
const { checkLogin } = useLogin()
const userStore = useUserStore()
const showQRCode = ref(false)
const qrcode = ref('')
const qrcodeValid = ref(1)
const isShare = ref(false)
const hasTicket = ref(false)
const available = ref(true)
const sanLogId = ref()
const sanRuleId = ref()
const consumeSn = ref('')
const orderSn = ref()
const order = ref()
const canShare = ref(false)
const ticketDetail = ref()

onLoad((options) => {
  console.log(options)
  orderSn.value = options.orderSn || ''
  sanLogId.value = options.sanLogId || ''
  isShare.value = options.fromShare === '1'
})
onShow(async () => {
  await checkLogin()
  if (isShare.value) {
    getInformation()
  } else {
    getQRCode()
  }
})

// 分享
onShareAppMessage(() => {
  const promise = new Promise((resolve, reject) => {
    http
      .post('/Santicket/shareAddLog', {
        user_id: userStore.userInfoUserId,
        bus_id: userStore.userInfoBusId,
        san_log_id: ticketDetail.value.san_log_id,
        loading: true,
      })
      .then((res) => {
        if (res.data.share_id) {
          const share_id = res.data.share_id
          console.log(`/pages/receiveCard/receiveCard?share_id=${share_id}&bus_id=${userStore.userInfoBusId}`)
          resolve({
            title: `有一张【${ticketDetail.value.name}】待领取`,
            path: `/pages/receiveCard/receiveCard?share_id=${share_id}&bus_id=${userStore.userInfoBusId}&san_log_id=${sanLogId.value}`,
            imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/share-ticket.png',
          })
        } else {
          reject({})
        }
      })
      .catch((err) => {
        reject({})
      })
  })
  return {
    title: `有一张【${ticketDetail.value.name}】待领取`,
    path: `/pages/receiveCard/receiveCard?share_id=&bus_id=${userStore.userInfoBusId}&san_log_id=${sanLogId.value}`,
    imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/share-ticket.png',
    promise, // 如果该参数存在，则其它的参数将会以 resolve 结果为准，如果三秒内不 resolve，分享会使用上面传入的默认参数
  }
})

// get banner unit-id
const adUnitId = ref('')
const getAdUnitId = () => {
  http
    .post('/Banner/getList', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      if (Array.isArray(res.data)) {
        const list = res.data.map((item) => item.link)
        const randomNumber = Math.floor(Math.random() * list.length)
        adUnitId.value = list[randomNumber]
      }
    })
}

getAdUnitId()

function handleGetTicket() {
  http
    .post('/Santicket/receiveTicket', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      order_sn: orderSn.value,
      loading: true,
    })
    .then((res) => {
      uni.showToast({
        title: '领取成功！',
        icon: 'none',
        duration: 1500,
        mask: false,
      })
      setTimeout(() => {
        getQRCode()
      }, 2000)
    })
    .catch(() => {
      getInformation()
    })
}

// 退票
function handleReturnTicket() {
  uni.showModal({
    title: '确认退票',
    content: '购票费用将原路退回支付账户预计1-2工作日，确认退票？',
    success: (res) => {
      if (res.confirm) {
        handleReturn();
      }
    },
  })
}

// 实际退票请求接口
function  handleReturn() {
  const url = '/Santicket/cancel'
  const params = {
    bus_id: userStore.userInfoBusId,
    san_id: sanLogId.value
  }
  http
    .post(url, params)
    .then((res) => {
      uni.showToast({
        title: res.errormsg,
        icon: 'none',
        duration: 1500,
        mask: false,
      })
      // 无论成功失败都重新请求获取详情
      if (isShare.value) {
        getInformation()
      } else {
        getQRCode()
      }
    })
}

function handleCancelTicket() {
  uni.showModal({
    title: '提示',
    content: '确认放弃凭证？',
    success: (res) => {
      if (res.confirm) {
        http
          .post('/Santicket/delVoucher', {
            bus_id: userStore.userInfoBusId,
            user_id: userStore.userInfoUserId,
            san_log_id: sanLogId.value,
            loading: true,
          })
          .then((res) => {
            available.value = false
          })
      }
    },
  })
}
function getQRCode() {
  showQRCode.value = true
  http
    .get('/Good/getVoucher', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      order_sn: orderSn.value,
      consumption_log_id: sanLogId.value,
      loading: true,
    })
    .then((res) => {
      const resData = res.data
      qrcode.value = resData.voucher_image
      qrcodeValid.value = resData.consume_status
      sanLogId.value = resData.san_log_id
      sanRuleId.value = resData.san_rule_id
      orderSn.value = resData.order_sn
      const sn = resData.consume_sn
      consumeSn.value = `${sn.substring(0, 4)}  ${sn.substring(4, 8)}  ${sn.substring(8, 12)}`

      // order.value = {
      //   bus_name: resData.bus_name,
      //   validity_time: resData.validity_time,
      //   space_name: resData.space_name,
      //   max_join_people: resData.max_join_people,
      //   return_status: resData.status,
      //   ticket_type: resData.ticket_type,
      //   cancel_member: resData.san_setting && resData.san_setting.cancel_member ? resData.san_setting.cancel_member : false,
      // }
      order.value = resData
      available.value = resData.ticket_type !== 1 && resData.share !== 1

      canShare.value = resData.can_share
      ticketDetail.value = resData
    })
    .catch((err) => {
      console.log(err)
      if(err.errorcode === 80000) {
        setTimeout(() => {
          canShare.value = false;
          uni.navigateBack()
        }, 1000);
      }
    })
}
function getInformation() {
  http
    .get('/Santicket/receiveInfo', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      order_sn: orderSn.value,
      san_log_id: sanLogId.value,
      loading: true,
    })
    .then((res) => {
      order.value = res.data
      hasTicket.value = res.data.receive == 1

      qrcode.value = res.data.voucher_image
    })
}
</script>
<style lang="scss" scoped>
.banner {
  height: 180rpx;
  width: 670rpx;
  margin: 70rpx 0 80rpx -10rpx;
  .banner-img {
    width: 100%;
    height: 100%;
  }
}

.form-items {
  padding-bottom: 10rpx;
}
.success-tips {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-weight: bold;
  color: #000;
}

.link {
  height: 40rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: bold;
  color: $theme-text-color-other;
  text-decoration: underline;
}

.qrcode {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 60rpx;
  position: relative;

  .subtitle {
    font-size: 30rpx;
    font-weight: bold;
    color: #313131;
  }

  .code {
    margin-top: 40rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #03080e;
  }

  .picture {
    width: 375rpx;
    height: 375rpx;
    margin-top: 30rpx;
    margin-bottom: 60rpx;
  }

  .pic-false {
    width: 375rpx;
    height: 375rpx;
    margin-top: 30rpx;
    margin-bottom: 60rpx;
    opacity: 0.2;
  }

  .text-false {
    position: absolute;
    right: 130rpx;
    top: 160rpx;
    transform: rotate(-30deg);
    font-size: 24rpx;
    font-weight: bold;
    color: #ca2e53;
    border-radius: 50%;
    border: 1rpx solid #ca2e53;
    width: 100rpx;
    height: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .label {
    display: flex;
    justify-content: center;
    align-items: center;
    letter-spacing: 4rpx;
    margin: 30rpx;
    width: 330rpx;
    height: 80rpx;
    background: #e7e7e7;
    border-radius: 40rpx;
    font-size: 26rpx;
    font-weight: bold;
    color: #b3b3b3;
  }

  .btn-height {
    margin: 30rpx 0 120rpx 0;
    width: 330rpx;
    height: 80rpx;
  }
}
</style>
