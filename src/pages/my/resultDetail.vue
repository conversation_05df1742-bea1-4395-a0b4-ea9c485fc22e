<template>
  <view>
    <web-view v-if="canIUse" :src="src"></web-view>
    <view wx:else> 请升级微信版本！ </view>
  </view>
</template>

<script setup lang="ts" name="resultDetail">
import env from '@/config/env'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const canIUse = ref(false)
const src = ref('')

onLoad((options) => {
  canIUse.value = uni.canIUse('web-view')

  const url = `${env.webViewUrl}/static/health/dimension.html`
  const param = encodeURIComponent(
    `body=-1_vis_${options.userId}&busId=${userStore.userInfoBusId}&userId=${userStore.userInfoUserId}`
  )
  src.value = url + '?' + param
})
</script>
