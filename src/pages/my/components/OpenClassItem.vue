<template>
  <view class="record-item theme-bg" @tap="goReserveDetail">
    <view v-if="info.class_mark_id" class="status-text" :class="info.status == 1 ? 'theme-bg' : ''">
      {{ info.status == 1 ? '已约' : info.status == 2 ? '完成' : info.status == 3 ? '取消' : '爽约' }}
    </view>
    <view
      v-if="info.class_waitting_id && info.status != 4"
      class="status-text"
      :class="
        info.status == 1 || info.status == 5
          ? 'org-text'
          : info.status == 2
          ? 'org-bg'
          : info.status == 3
          ? 'theme-status'
          : ''
      "
    >
      {{
        info.status == 1 || info.status == 5
          ? '候补中'
          : info.status == 2
          ? '待确认'
          : info.status == 3
          ? '已约'
          : '候补失败'
      }}
    </view>
    <view class="left">
      <view class="img-view">
        <image class="pic-thum" :src="info.thumb" />
        <view v-if="info.status == 2 && info.confirmed_end_time" class="pic-bot">
          剩余时间
          <view>
            <CountDown v-if="info.confirmed_end_time" :size="10" :time="info.confirmed_end_time" />
          </view>
        </view>
      </view>
      <view class="item-info">
        <view class="name">
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/tuan.png" class="icon icon-mr icon-text" />
          <text style="{{info.class_level? 'maxWidth:210rpx':'maxWidth:320rpx'}}">
            {{ info.class_name }}
          </text>
          <view v-if="info.class_level" class="star-wrap">
            <uni-rate class="rate" :readonly="true" :value="info.class_level" :max="info.class_level" :size="12" />
          </view>
        </view>
        <view class="item">
          <view class="des-lef">
            {{ info.coach_name }}
          </view>
          | 时长
          <view class="des-num">
            {{ info.class_duration }}
          </view>
          分钟
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" />
          {{ info.date_time }} {{ info.beg_time }}-{{ info.end_time }}
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-jiaoshi1" />
          {{ info.classroom_name || '无' }}
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-changguan" />
          {{ info.bus_name }}
        </view>
      </view>
    </view>
    <view class="reserve">
      <view
        v-if="info.class_mark_id && info.status == 1"
        class="cancel-btn"
        @tap.stop="cancelReserve(info.class_mark_id, refreshPage, info.bus_id)"
        >取消预约</view
      >
      <view
        v-if="info.class_waitting_id && info.status == 2"
        class="normal-btn confirm-btn"
        @tap.stop="confirmWait(info.class_waitting_id, refreshPage, { bus_id: info.bus_id, user_id: info.user_id })"
        >确认</view
      >
      <view
        v-if="info.class_waitting_id && (info.status == 1 || info.status == 2)"
        class="cancel-btn"
        @tap.stop="cancelWait(info.class_waitting_id, refreshPage, { bus_id: info.bus_id, user_id: info.user_id })"
        >取消</view
      >
      <view
        v-if="info.class_waitting_id && (info.status == 3 || info.status == 4)"
        class="wait-txt"
        :class="info.status == 3 ? 'theme-color-other' : ''"
        >{{ info.status == 3 ? '候补成功' : '候补失败' }}</view
      >
    </view>
  </view>
</template>

<script setup lang="ts">
import CountDown from '@/components/CountDown.vue'
import { useOpenClass } from '@/hooks/useOpenClass'
import { goUrlPage } from '@/utils/urlMap'
const { cancelReserve, confirmWait, cancelWait } = useOpenClass()
const props = defineProps({
  info: {
    type: Object as any,
  },
})
function goReserveDetail() {
  goUrlPage(
    `/pages/class/openClassReserveDetail?class_mark_id=${props.info.class_mark_id || ''}&bus_id=${
      props.info.bus_id
    }&class_waitting_id=${props.info.class_waitting_id || ''}`
  )
}
function refreshPage() {
  uni.$emit('refresh-class-record')
}
</script>
<style lang="scss" scoped>
.pic-bot {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 20rpx;
  margin-top: 15rpx;
}
.cancel-btn,
.confirm-btn {
  width: 120rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  border-radius: 10rpx;
  font-size: 24rpx;
}
.confirm-btn {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.cancel-btn {
  border: 1px solid #ff7427;
  color: #ff7427;
}
.reserve {
  position: absolute;
  top: 50%;
  right: 10rpx;
  transform: translateY(-50%);
}
</style>
