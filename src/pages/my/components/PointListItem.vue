<template>
  <view>
    <view v-if="type === 1" class="item theme-bg">
      <view class="point-info">
        <text class="title">{{ item.remark }}</text>
        <text>发放时间: {{ item.create_time }}</text>
        <text>过期时间: {{ item.end_time }}</text>
      </view>
      <text class="ponit-num">+{{ item.all_num }}</text>
    </view>
    <view v-if="type === 2" class="item theme-bg">
      <view class="point-info">
        <text class="title">{{ item.remark }}</text>
        <text>操作时间: {{ item.create_time }}</text>
      </view>
      <text class="ponit-num">{{ item.num }}</text>
    </view>
    <view v-if="type === 3" class="item theme-bg">
      <text class="point-info">过期时间: {{ item.end_time }}</text>
      <text class="ponit-num">-{{ item.last_num }}</text>
    </view>
  </view>
</template>

<script setup lang="ts" name="PointListItem">
const props = defineProps({
  type: {
    type: Number,
    default: 1,
  },
  item: Object as any,
})
</script>
<style lang="scss">
.item {
  display: flex;
  justify-content: space-between;
  margin: 20rpx auto 0;
  padding: 30rpx;
  width: 710rpx;
  height: 180rpx;
  border-radius: 10rpx;
  background-color: #fff;
  box-sizing: border-box;
  .point-info {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    font-size: 24rpx;
    .title {
      font-size: 30rpx;
      height: 30rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }
  }

  .ponit-num {
    display: flex;
    align-items: center;
    font-size: 36rpx;
    font-weight: bold;
    color: $theme-text-color-other;
  }
}
</style>
