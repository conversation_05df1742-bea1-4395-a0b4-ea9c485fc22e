<template>
  <view class="item-box" :class="isAvailable ? '' : 'invalid'">
    <view class="item-left">
      <view class="item-title">{{ ticket.name }}</view>
      <view class="item-subtitle">
        <text class="name">{{ ticket.space_name && ticket.space_name.length ? ticket.space_name : '入场' }}</text>
        <text v-if="ticket.ticket_type == 1" style="margin-left: 20rpx"
          >{{ ticket.base_duration }} {{ ticket.duration_unit == 1 ? '小时' : '分钟' }}</text
        >
        <text v-else style="margin-left: 20rpx">{{ ticket.base_duration }}</text>
      </view>
      <view v-if="ticket.bus_name" class="item-subtitle">
        门店：
        {{ ticket.bus_name }}
      </view>
      <view v-if="ticket.ticket_type == 1" class="item-subtitle">{{ ticket.valid_data }} 前可核销</view>
      <view v-else class="item-subtitle">场次结束前可核销</view>
    </view>
    <view
      class="item-right"
      @tap="goUrlPage(`/pages/my/ticketDetail?sanLogId=${ticket.san_log_id}&bus_id=${ticket.bus_id}`)"
    >
      <image
        v-if="isAvailable"
        class="qrcode"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/qrcode-oth.png"
        alt="qrcode"
      />
      <image
        v-else
        class="qrcode"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/my-qrcode.png"
        alt="qrcode"
      />
      <text class="arrow-oth">查看入场凭证</text>
    </view>
    <image v-if="ticket.status == 2" src="/static/img/admission.png" class="admission"></image>
  </view>
</template>

<script setup lang="ts" name="MyTicketItem">
import { goUrlPage } from '@/utils'

const props = defineProps({
  isAvailable: {
    type: Boolean,
    default: false,
  },
  ticket: Object as any,
})
</script>
<style lang="scss">
.item-box {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  width: 690rpx;
  min-height: 130rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 20rpx auto;
  padding: 35rpx 30rpx;
  box-sizing: border-box;
  position: relative;

  .item-left {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    width: 100%;

    .item-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #313131;
    }

    .item-subtitle {
      font-size: 24rpx;
      color: #313131;
      margin-top: 24rpx;

      .name {
        display: inline-block;
        vertical-align: middle;
        max-width: 400rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background: rgba(255, 116, 39, 0.1);
        font-size: 24rpx;
        line-height: 44rpx;
        color: #313131;
        padding: 2rpx 4rpx;
      }
    }
  }

  .item-right {
    align-self: flex-end;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: absolute;
    font-size: 26rpx;
    color: $theme-text-color-other;

    .qrcode {
      width: 30rpx;
      height: 30rpx;
    }

    .arrow-oth {
      position: relative;
      margin-left: 10rpx;
      padding-right: 35rpx;
      &::after {
        content: ' ';
        position: absolute;
        right: -15rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border: 10rpx solid transparent;
        border-left: 10rpx solid $theme-text-color-other;
      }
    }
  }
}

.invalid {
  background: #d9d9d9;
  border-radius: 10rpx;

  .item-right {
    color: #898989;
    .arrow-oth::after {
      border-left-color: #898989;
    }
  }
}

.admission {
  width: 82rpx;
  height: 72rpx;
  position: absolute;
  top: 35rpx;
  right: 30rpx;
}
</style>
