<template>
  <view class="content">
    <z-paging
      ref="paging"
      v-model="dataList"
      :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
      :fixed="false"
      :auto="false"
      @query="loadList"
    >
      <template #top>
        <view v-if="currentIndex === 0 && waitFirst.class_waitting_id" class="wait-first" @tap="goReserveDetail">
          <view class="first-top">
            <view>{{ waitFirst.status === 1 ? '候补中' : '待确认' }}</view>
            <view v-if="waitFirst.status === 2">
              剩余时间：
              <CountDown v-if="waitFirst.confirmed_end_time" :time="waitFirst.confirmed_end_time" />
            </view>
          </view>
          <view class="first-bottom">
            <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/tuan.png" class="icon icon-text" />
            {{ waitFirst.date_time }} {{ waitFirst.beg_time }} {{ waitFirst.class_name }}
          </view>
        </view>
      </template>
      <template v-for="(item, index) in dataList" :key="index">
        <OpenClassItem v-if="tabIndex == 0" :info="item" />
        <PtItem v-if="tabIndex == 1 || tabIndex == 2" :info="item" />
        <BookingSpaceItem v-if="tabIndex == 3" :info="item" />
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import CountDown from '@/components/CountDown.vue'
import PtItem from './PtItem.vue'
import BookingSpaceItem from './BookingSpaceItem.vue'
import OpenClassItem from './OpenClassItem.vue'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils/urlMap'

const paging = ref()
const dataList = ref([])
const firstLoaded = ref(false)
const props = defineProps({
  tabIndex: {
    type: Number,
    default: 0,
  },
  //当前swiper切换到第几个index
  currentIndex: {
    type: Number,
    default: 0,
  },
})
const useMerchantStore = useMerchant()

const waitFirst = reactive({
  status: 0,
  date_time: '',
  bus_id: '',
  confirmed_end_time: '',
  beg_time: '',
  class_waitting_id: '',
  class_name: '',
})
uni.$on('refresh-class-record', () => {
  paging.value.reload()
})

onShow(() => {
  if (firstLoaded.value && props.currentIndex === props.tabIndex) {
    getList()
  }
})
onUnload(() => {
  uni.$off('refresh-class-record')
})

watchEffect(() => {
  const currentIndex = props.currentIndex
  const busId = useMerchantStore.userInfoBusId
  const userId = useMerchantStore.userInfoUserId
  //懒加载，当滑动到当前的item时，才去加载
  if (currentIndex === props.tabIndex) {
    if (!userId) {
      paging.value && paging.value.complete([])
      return
    }
    if ((!busId && userId) || (userId && busId)) {
      getList()
    }
  }
})
function getList() {
  // if (props.tabIndex === 0) {
  //   getClassWaitingIndex()
  // }
  setTimeout(() => {
    paging.value.reload()
  }, 100)
}

// function getClassWaitingIndex() {
//   http
//     .get('/Classwaiting/getClassWaitingList', {
//       bus_id: useMerchantStore.userInfoBusId,
//       user_id: useMerchantStore.userInfoUserId,
//       is_index: true,
//     })
//     .then((res) => {
//       const resList = res.data.list
//       if (resList && resList.length) {
//         const confirmTime = resList[0].confirmed_end_time.split(':')
//         Object.assign(waitFirst, {
//           ...resList[0],
//           h: confirmTime[0] ? +confirmTime[0] : '',
//           m: confirmTime[1] ? +confirmTime[1] : '',
//           s: confirmTime[2] ? +confirmTime[2] : '',
//         })
//       } else {
//         Object.assign(waitFirst, {
//           class_waitting_id: '',
//           class_name: '',
//         })
//       }
//     })
// }
function loadList(pageNo, pageSize) {
  if (!useMerchantStore.userInfoUserId) {
    paging.value && paging.value.complete([])
    return
  }
  //组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  if (props.tabIndex === 3) {
    getBookingList(pageNo, pageSize)
  } else {
    http
      .get('/classMark/getClassMarkList', {
        bus_id: useMerchantStore.userInfoBusId,
        user_id: useMerchantStore.userInfoUserId,
        type: props.tabIndex === 2 ? 4 : props.tabIndex + 1, //1团课 2私教 3场地 4泳教
        page_no: pageNo,
        page_size: pageSize,
      })
      .then((res) => {
        paging.value.complete(res.data.list)
        firstLoaded.value = true
      })
      .catch((res) => {
        //如果请求失败写paging.value.complete(false);
        //注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
        //在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
        paging.value.complete(false)
      })
  }
}
function getBookingList(pageNo, pageSize) {
  http
    .get('/Booking/getOrderList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      status: '',
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      /* 订场邀约首次取消支付后，再次取消支付不会有新的待支付订单了
        如果使用了储值卡组合支付,取消支付跳转过来时,需要立即返还余额
         */
      if (res.errorcode === 0 && Array.isArray(res.data.list) && !firstLoaded.value) {
        const pages = getCurrentPages()
        const page = pages[pages.length - 1] as any
        const options = page.options
        // revoke_space_order_id有该参数说明需要处理 type=3
        if (options.revoke_space_order_id) {
          const item = res.data.list.find((v) => v.id === +options.revoke_space_order_id)
          // status 5 已取消, mix_pay_list长度大于1并且有储值卡代表是储值卡组合支付
          if (item && item.status === 5 && Array.isArray(item.mix_pay_list) && item.mix_pay_list.length > 1) {
            const mixPayItem = item.mix_pay_list.find((v) => v.pay_type === 8)
            if (mixPayItem) {
              http.post('Booking/storedCardChangeRevoke', {
                bus_id: useMerchantStore.userInfoBusId,
                user_id: useMerchantStore.userInfoUserId,
                card_user_id: mixPayItem.card_user_id,
                business_id: item.id,
                business_type: '2', // 业务类型，1--散场票，2--订场，3--购卡
              })
            }
          }
        }
      }

      paging.value.complete(res.data.list)
      firstLoaded.value = true
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
function goReserveDetail() {
  goUrlPage(
    `/pages/class/openClassReserveDetail?class_waitting_id=${waitFirst.class_waitting_id}&bus_id=${waitFirst.bus_id}`
  )
}
</script>

<style lang="scss" scoped>
/* 注意:父节点需要固定高度，z-paging的height:100%才会生效 */
.content {
  height: 100%;
}
.right-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  line-height: 90rpx;
  color: $theme-text-color-other;
}
.top-bus {
  display: flex;
  justify-content: space-between;
  padding: 0 25rpx;
}
.wait-first {
  padding: 18rpx 50rpx;
  font-size: 24rpx;
  background: rgba(255, 116, 39, 0.1);

  .first-top {
    display: flex;
    justify-content: space-between;
    color: $theme-text-color-other;
    margin-bottom: 10rpx;
  }
  .first-bottom {
    white-space: nowrap;
    overflow: hidden;
  }
}
.line-count {
  display: inline-block;
}
</style>
