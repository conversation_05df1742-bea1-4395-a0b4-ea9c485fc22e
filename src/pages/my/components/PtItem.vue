<template>
  <view class="record-item theme-bg" @tap="goReserveDetail">
    <view class="status-text" :class="info.status == 1 ? 'theme-text' : ''">
      {{
        info.status == 1
          ? '已约'
          : info.status == 2
          ? '已上'
          : info.status == 3
          ? '爽约'
          : info.status == 4
          ? '完成'
          : '取消'
      }}
    </view>
    <view class="left">
      <image class="pic" :src="info.avatar" />
      <view class="item-info">
        <view class="name">{{ info.class_name }}</view>
        <view class="item">
          <view class="des-lef">
            {{ info.coach_name }}
          </view>
          | 时长
          <view class="des-num">
            {{ info.class_duration }}
          </view>
          分钟 <text v-if="info.user_no > 1" style="margin-left: 6rpx"> | 1对{{ info.user_no }}</text>
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" />
          {{ info.date_time }} {{ info.beg_time }}-{{ info.end_time }}
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-changguan" />
          {{ info.bus_name }}
        </view>
      </view>
    </view>
    <view class="reserve">
      <view v-if="info.status == 1" class="cancel-btn" @tap.stop="cancelPtReserve(info.id, refreshPage, info?.appt_type, info?.refund_rate, info?.offline_pay)">取消预约</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { usePtClass } from '@/hooks/usePtClass'
import { goUrlPage } from '@/utils/urlMap'
const { cancelPtReserve } = usePtClass()
const props = defineProps<{
  info: Record<string, any>
}>()
function goReserveDetail() {
  goUrlPage(`/pages/class/ptReserveDetai?pt_id=${props.info.id}&bus_id=${props.info.bus_id}`)
}
function refreshPage() {
  uni.$emit('refresh-class-record')
}
</script>
<style lang="scss" scoped>
.cancel-btn {
  width: 120rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  border: 1px solid #ff7427;
  border-radius: 10rpx;
  font-size: 24rpx;
  color: #ff7427;
}
</style>
