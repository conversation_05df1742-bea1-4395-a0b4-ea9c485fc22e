<template>
  <view class="data-list">
    <z-paging
      ref="paging"
      v-model="dataList"
      :fixed="false"
      :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
      @query="loadList"
    >
      <template v-for="item in dataList" :key="item.id">
        <PointListItem :item="item" :type="type" />
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts" name="PointList">
import http from '@/utils/request'

import { useUserStore } from '@/store/user'
import PointListItem from './PointListItem.vue'
const props = defineProps({
  type: {
    type: Number,
    default: 1,
  },
})

const userStore = useUserStore()
const dataList = ref([])
const paging = ref()

function loadList(pageNo, pageSize) {
  http
    .get('/Point/getPointList', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      type: props.type,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
</script>
<style lang="scss">
.data-list {
  height: calc(100vh - 394rpx);
}
</style>
