<template>
  <view class="record-item theme-bg">
    <!-- 订单状态，0-未支付，1-已支付，2-已到场，3-已离场，4-已退款，5-已取消（未付款） -->
    <view
      class="booking-status"
      :class="
        info.status == 1
          ? 'org-text'
          : info.status == 0
          ? 'red-text'
          : info.status == 4 || info.status == 5
          ? 'gray-bg'
          : ''
      "
    >
      {{ info.status_desc }}
    </view>
    <view class="left">
      <view class="img-view">
        <image
          class="pic-thum"
          :src="info.pic_url || 'https://imagecdn.rocketbird.cn/minprogram/member/image/category-1.png'"
        />
        <view v-if="(info.status === 1 || info.status === 2) && info.end_time > today" class="pic-bot green-bot">
          邀请成员进场
          <view>
            剩余<text class="num">{{ info.invitable_num }}</text
            >位
          </view>
        </view>
      </view>
      <view class="item-info">
        <view class="name">
          <image
            :src="`https://imagecdn.rocketbird.cn/minprogram/uni-member/${info.is_half ? 'ban' : 'quan'}.png`"
            class="icon icon-mr icon-text"
          />
          <text style="'maxWidth:320rpx'">
            {{ info.space_name }}
          </text>
        </view>
        <view class="item">
          <view class="des-lef">
            {{ info.space_type_name }}
          </view>
          | 时长
          <view class="des-num">
            {{ (info.end_time - info.start_time) / 60 }}
          </view>
          分钟
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-shijian" />
          {{ info.date_desc }} {{ info.time_desc }}
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-tuikuanqiankuan" />
          ￥{{ info.amount }}
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-changguan" />
          {{ info.bus_name }}
        </view>
      </view>
    </view>

    <view
      v-if="(info.status === 0 || info.status === 1 || info.status === 2) && info.end_time > today"
      class="item-btns"
    >
      <button
        v-if="(info.status === 0 || info.status === 1) && info.type === 1 && info.cancel_button"
        class="cancel-btn"
        @tap.stop="cancelReserve(info, refreshPage)"
      >
        取消订场
      </button>
      <button
        v-if="info.status === 0 && info.confirmed_end_time && !timeUpFlage && info.reservation_type === null"
        class="normal-btn confirm-btn"
        @tap.stop="continuePay(info, refreshPage)"
      >
        支付
      </button>
      <!-- 约球的订场记录 -->
      <button
        v-if="info.status === 0 && info.sports_mark_order_id"
        class="normal-btn confirm-btn"
        @tap.stop="goPagePay"
      >
        支付
      </button>
      <button
        v-if="info.status !== 0 && info.invitable_num > 0 && info.end_time > today"
        class="normal-btn confirm-btn"
        open-type="share"
        data-action="space"
        :data-sn="info.order_sn"
        :data-bus-id="info.bus_id"
        :data-user-id="info.user_id"
      >
        邀请
      </button>
    </view>
    <view v-if="info.ssl_status !== 1" class="item-btns" style="bottom: 150rpx;">
      <button class="normal-btn confirm-btn" style="border: none; background-color: transparent; margin-right: 0" @tap.stop="handleDetail">约课信息</button>
      <div class="arrow">
        <div class="arrow-top"></div>
        <div class="arrow-bottom"></div>
      </div>
    </view>
    <!-- 约球的订场记录 -->
    <view v-if="info.status === 0 && info.sports_mark_order_id && info.end_time > today" class="time-down">
      <view>订单即将关闭</view>
      请尽快付款！
    </view>
    <view v-if="info.status === 0 && info.confirmed_end_time && !timeUpFlage" class="time-down">
      <view>剩余支付时间</view>
      <CountDown
        v-if="info.confirmed_end_time"
        :size="10"
        color="#ff0000"
        :time="info.confirmed_end_time"
        @timeup="timeup"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import CountDown from '@/components/CountDown.vue'
import { useSpace } from '@/hooks/useSpace'
import { useUserStore } from '@/store/user'
import { goUrlPage } from '@/utils'
const userStore = useUserStore()
const { cancelReserve, continuePay } = useSpace()
const timeUpFlage = ref(false)
const props = defineProps({
  info: {
    type: Object as any,
  },
})
const today = ref(new Date().getTime() / 1000)

function refreshPage() {
  today.value = new Date().getTime() / 1000
  uni.$emit('refresh-class-record')
}
function timeup() {
  timeUpFlage.value = true
}
function goPagePay() {
  const bus_id = props.info.bus_id || userStore.userInfoBusId
  const user_id = props.info.user_id || userStore.userInfoUserId
  const params = {
    space_id: props.info.space_id,
    space_name: props.info.space_name,
    space_type_id: props.info.space_type_id,
    date: props.info.date_desc,
    bus_id,
    user_id,
    card_user_id: '',
    is_half: props.info.is_half,
    position: props.info.position,
    pay_type: '',
    schedules: props.info.schedule_detail || [
      {
        start_time: props.info.beg_date,
        end_time: props.info.end_date,
      },
    ],
    amount: props.info.price,
    datetime: props.info.beg_date + '-' + props.info.end_date,
    max_join_people: props.info.max_join_people,
  }

  uni.setStorageSync('sbPost', params)
  goUrlPage(
    `/pages/stadium/buySpace?sports_mark_id=${props.info.sports_mark_id}&sports_mark_order_id=${props.info.sports_mark_order_id}&bus_id=${bus_id}`
  )
}
const handleDetail = () => {
  goUrlPage(`/pages/class/ptReserveDetai?pt_id=${props.info.pt_schedule_id}&bus_id=${props.info.bus_id}`)
}
</script>
<style lang="scss" scoped>
.pic-bot {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 20rpx;
  margin-top: 15rpx;
}
.green-bot {
  background: rgba(var(--THEME-RGB), 0.1);
  width: 160rpx;
  .num {
    color: $theme-text-color-other;
  }
}
.booking-status {
  position: absolute;
  right: 0;
  top: 0;
  width: 110rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  border-radius: 0 20rpx 0 20rpx;
  background-color: rgba(255, 116, 39, 0.2);
  font-size: 20rpx;
  &.gray-bg {
    background: #e7e7e7;
    color: #000000;
  }
  &.org-text {
    color: $theme-text-color-other;
  }
  &.red-text {
    color: #ff0000;
  }
}
.cancel-btn,
.confirm-btn {
  width: 120rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  border-radius: 10rpx;
  font-size: 24rpx;
}
.cancel-btn {
  border: 1px solid #ff7427;
  color: #ff7427;
}
.reserve {
  position: absolute;
  top: 50%;
  right: 10rpx;
  transform: translateY(-50%);
}
.item-btns {
  position: absolute;
  bottom: 10rpx;
  right: 0;
  width: 320rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  button {
    margin-right: 20rpx;
  }
}
.time-down {
  position: absolute;
  top: 40rpx;
  right: 10rpx;
  color: #ff0000;
  font-size: 20rpx;
}

/* From Uiverse.io by Nawsome */ 
.arrow {
  cursor: pointer;
  height: 15rpx;
  transform: translateX(-50%) translateY(-50%);
  transition: transform 0.1s;
  width: 13rpx;
  margin-top: 10rpx;
}

.arrow-top, .arrow-bottom {
  background-color: #666;
  height: 4rpx;
  left: -5rpx;
  position: absolute;
  top: 50%;
  width: 100%;
}

.arrow-top:after, .arrow-bottom:after {
  background-color: #fff;
  content: "";
  height: 100%;
  position: absolute;
  top: 0;
  transition: all 0.15s;
}

.arrow-top {
  transform: rotate(45deg);
  transform-origin: bottom right;
}

.arrow-top:after {
  left: 100%;
  right: 0;
  transition-delay: 0s;
}

.arrow-bottom {
  transform: rotate(-45deg);
  transform-origin: top right;
}

.arrow-bottom:after {
  left: 0;
  right: 100%;
  transition-delay: 0.15s;
}

.arrow:hover .arrow-top:after {
  left: 0;
  transition-delay: 0.15s;
}

.arrow:hover .arrow-bottom:after {
  right: 0;
  transition-delay: 0s;
}

.arrow:active {
  transform: translateX(-50%) translateY(-50%) scale(0.9);
}
</style>
