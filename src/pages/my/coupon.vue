<template>
  <custom-tabs :value="0">
    <template #right>
      <MerchantBusPick v-model="busId" @change="handleBusChange" />
    </template>
    <custom-tab-pane label="可用">
      <view class="card-page">
        <template v-if="usableCouponList.length">
          <view v-for="bus in usableCouponList" :key="bus.bus_id" class="bus-tit-item">
            <view class="bus-name">
              <ThemeIcon class="icon-mr" type="t-icon-changguan" />
              {{ bus.bus_name }}
            </view>
            <CouponItem
              v-for="couponItem in bus.coupon_list"
              :key="couponItem.coupon_receive_id"
              :coupon="couponItem"
              is-show-btn
            />
          </view>
        </template>
        <view v-else class="nodata">暂无对应折扣券数据</view>
      </view>
    </custom-tab-pane>
    <custom-tab-pane label="不可用">
      <view class="card-page">
        <template v-if="disabledCouponList.length">
          <view v-for="bus in disabledCouponList" :key="bus.bus_id" class="bus-tit-item">
            <view class="bus-name">
              <ThemeIcon class="icon-mr" type="t-icon-changguan" />
              {{ bus.bus_name }}
            </view>
            <CouponItem
              v-for="couponItem in bus.coupon_list"
              :key="couponItem.coupon_receive_id"
              :coupon="couponItem"
              :is-show-btn="false"
            />
          </view>
        </template>
        <view v-else class="nodata">暂无对应折扣券数据</view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts" name="coupon">
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import CouponItem from '@/components/couponItem.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useMerchant } from '@/store/merchant'
import { useLogin } from '@/hooks/useLogin'

const { checkLogin } = useLogin()
const busId = ref('')
const userStore = useUserStore()
const list = ref<Record<string, any>[]>([])
const usableCouponList = computed<Record<string, any>[]>(() => {
  return list.value
    .map((v) => {
      return {
        ...v,
        coupon_list: v.coupon_list?.filter((i) => i.status === 1) || [],
      }
    })
    .filter((v) => v.coupon_list.length)
})
const disabledCouponList = computed<Record<string, any>[]>(() => {
  return list.value
    .map((v) => {
      return {
        ...v,
        coupon_list: v.coupon_list?.filter((i) => i.status !== 1) || [],
      }
    })
    .filter((v) => v.coupon_list.length)
})

const useMerchantStore = useMerchant()

function handleBusChange({ bus_id, user_id }) {
  getCoupon()
}
onShow(() => {
  checkLogin()
})
function getCoupon() {
  if (!useMerchantStore.userInfoUserId) {
    list.value = []
    return
  }
  http
    .get('Personalcenter/getCouponList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        list.value = res.data?.list || []
      }
    })
}
onShareAppMessage((options) => {
  if (options.from === 'button') {
    return {
      title: options.target.dataset.couponName + '请查收',
      path: `/pages/bonuses/giftCoupon?from=share&busId=${options.target.dataset.busId}&couponId=${options.target.dataset.couponId}&openId=${userStore.userInfo.openid}`,
      imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/member/image/share_gift_card.png',
    }
  }
})
</script>

<style lang="scss" scoped>
.bus-tit-item {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  padding: 0 20rpx;
  overflow: hidden;
  .bus-name {
    display: flex;
    align-items: center;
    line-height: 70rpx;
    font-size: 24rpx;
  }
}
.card-page {
  overflow: hidden;
  box-sizing: border-box;
  padding: 46rpx 30rpx 0;
  min-height: 100%;
}
</style>
