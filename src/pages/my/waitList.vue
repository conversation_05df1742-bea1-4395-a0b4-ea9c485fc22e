<template>
  <view class="content">
    <z-paging
      ref="paging"
      v-model="dataList"
      :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
      @query="loadList"
    >
      <template v-for="(item, index) in dataList" :key="index">
        <OpenClassItem :info="item" />
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import OpenClassItem from './components/OpenClassItem.vue'
import { useMerchant } from '@/store/merchant'

const useMerchantStore = useMerchant()
const dataList = ref([])
const paging = ref()
onLoad(() => {
  uni.$on('refresh-class-record', () => {
    paging.value.reload()
  })
})
onUnmounted(() => {
  uni.$off('refresh-class-record')
})
function loadList(pageNo, pageSize) {
  http
    .get('/Classwaiting/getClassWaitingList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
</script>

<style lang="scss">
/* 注意:父节点需要固定高度，z-paging的height:100%才会生效 */
.content {
  height: 100%;
}
.record-item {
  position: relative;
  box-sizing: border-box;
  width: 690rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin: 25rpx auto 0;
  overflow: hidden;
  font-size: 24rpx;
  .left {
    display: flex;
    align-items: flex-start;
  }
  .pic {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: 26rpx;
  }
  .pic-thum {
    width: 161rpx;
    height: 94rpx;
    border-radius: 6rpx;
    margin-right: 14rpx;
  }
  .item-info {
    .name {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      > text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.1;
      }
    }
    .des {
      margin-top: 20rpx;
      display: flex;
      justify-items: center;
    }
    .des-lef {
      font-weight: bold;
      margin-right: 8rpx;
      max-width: 200rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .des-num {
      color: #ff7427;
    }
    .item {
      display: flex;
      align-items: center;
      font-size: 23rpx;
      margin-top: 15rpx;
    }
  }
}
</style>
