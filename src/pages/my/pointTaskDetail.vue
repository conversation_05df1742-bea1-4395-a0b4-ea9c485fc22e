<template>
  <view class="box-body">
    <scroll-view v-if="list.length" class="list" scroll-y="true" enable-flex>
      <view v-for="(item, index) in list" :key="index" class="item theme-bg">
        <text class="title">{{ item.name }}</text>
        <view class="content-box">
          <view class="point-row">
            <image
              class="icon-point"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/point/point.png"
            ></image>
            <text class="point">+{{ item.rule_num }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
    <view v-else class="nodata"></view>
  </view>
</template>

<script setup lang="ts" name="point">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const action = ref()
const list = ref([])
onLoad((options) => {
  action.value = options.action
  loadList()
})
function loadList() {
  http
    .get('/Point/getPointRuleInfo', {
      bus_id: userStore.userInfoBusId,
      action: action.value,
    })
    .then((res) => {
      list.value = res.data?.list
    })
}
</script>
<style lang="scss" scoped>
.box-body {
  .list {
    display: flex;
    flex-direction: column;
    padding: 27rpx 0;
    width: 100%;
    height: auto;
    box-sizing: border-box;
  }
  .item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0 20rpx 20rpx;
    padding: 24rpx;
    height: 140rpx;
    border-radius: 10rpx;
    box-sizing: border-box;
    .title {
      font-size: 30rpx;
      font-weight: bold;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .content-box {
      .point-row {
        display: flex;
        align-items: center;
      }
      .icon-point {
        margin-right: 12rpx;
        width: 24rpx;
        height: 24rpx;
      }
      .point {
        font-size: 24rpx;
      }
    }
  }
}
</style>
