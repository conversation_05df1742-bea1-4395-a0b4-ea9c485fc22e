<template>
  <view class="box">
    <view class="h-up-txt">正在分析您的体测结果，大约需要30秒，请等待！</view>
    <view class="gif-box">
      <image
        class="bg-image"
        src="https://imagecdn.rocketbird.cn/minprogram/member/image/a-body.png"
      />
      <image
        class="gif"
        src="https://imagecdn.rocketbird.cn/minprogram/member/image/analysis.gif"
      />
    </view>
    <view class="h-down-txt">不想等待？点击下方按钮后，即可关闭此页面</view>
    <form class="form-btn" @submit="formSubmit" report-submit>
      <button form-type="submit">用微信消息通知我</button>
    </form>
    <view class="notice" v-if="notice">{{ notice }}...</view>
  </view>
</template>

<script setup lang="ts" name="result">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { useUserStore } from '@/store/user'

const { checkLogin } = useLogin()
const userStore = useUserStore()
const canIUse = ref(false)
const sceneId = ref('')
const userId = ref('')
const openId = ref('')
const busId = ref('')
const userInfoId = ref('')
const notice = ref('')

function formSubmit(e) {
  const formId = e.detail.formId
  return http
    .post(
      '/Mobile/Vis/visDataNotice',
      {
        scene: sceneId.value,
        form_id: formId,
        bus_id: busId.value,
        user_id: userId.value,
      },
      'saasUrl'
    )
    .then((res) => {
      uni.showToast({
        title: '消息预订成功',
        icon: 'none',
        image: '',
        duration: 1500,
        mask: false,
        success: (result) => {
          setTimeout(() => {
            uni.switchTab({ url: '/pages/index/index' })
          }, 1500)
        },
        fail: () => {},
        complete: () => {},
      })
    })
    .catch((error) => {
      uni.showToast({ title: error.errormsg })
    })
}

const repeatBrid = () => {
  return http
    .post(
      '/Mobile/Vis/getVisBodyPoint',
      {
        scene: sceneId.value,
        bus_id: busId.value,
        user_id: userId.value,
        showToast: false,
      },
      'saasUrl'
    )
    .then(() => {
      uni.navigateTo({ url: '/pages/my/resultDetail?userId=' + userInfoId.value })
    })
    .catch((error) => {
      if (error.errorcode == 49024) {
        notice.value = error.errormsg || ''
        setTimeout(() => {
          repeatBrid()
        }, 3000)
      } else {
        uni.showToast({ title: error.errormsg })
      }
    })
}

const bindBird = () => {
  return http
    .post(
      '/Mobile/Vis/bindData',
      {
        scene: sceneId.value,
        bus_id: busId.value,
        user_id: userId.value,
        m_openid: openId.value,
      },
      'saasUrl'
    )
    .then((res) => {
      repeatBrid()
    })
    .catch((error) => {
      uni.showToast({ title: error.errormsg })
    })
}

const checkInfo = () => {
  return http
    .post(
      '/Mobile/Vis/checkUserInfo',
      {
        scene: sceneId.value,
        openid: openId.value,
        showToast: false,
      },
      'saasUrl'
    )
    .then((res) => {
      userInfoId.value = res.data.user_id
      userId.value = res.data.en_user_id
      busId.value = res.data.bus_id
      checkLogin(true, busId.value || '').then((info) => {
        userId.value = info.user_id || ''
        openId.value = userStore.userInfo.openid
        busId.value = info.bus_id
        bindBird()
      })
    }).catch((error) => {
      checkLogin(true, error.data.bus_id || '').then(() => {
        uni.navigateTo({ url: `/pages/train/extraInfo?type=bodyTest&scene=${sceneId.value}` })
      })
    })
}

onLoad((options) => {
  canIUse.value = uni.canIUse('web-view')
  sceneId.value = options.scene || ''
})

onShow(() => {
  userId.value = userStore.userInfo.user_id
  openId.value = userStore.userInfo.openid
  busId.value = userStore.userInfo.bus_id
  checkInfo()
})
</script>

<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  color: black;
}

.gif-box {
  width: 750rpx;
  height: 808rpx;
  display: flex;
  justify-content: center;

  .bg-image {
    width: 750rpx;
    height: 808rpx;
  }

  .gif {
    position: absolute;
    width: 364rpx;
    height: 350rpx;
    margin-top: 182rpx;
  }
}

.notice {
  margin-top: 40rpx;
  font-size: 28rpx;
  color: #333333;
}

.h-up-txt {
  font-size: 26rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 40rpx;
}

.h-down-txt {
  font-size: 26rpx;
  font-weight: 400;
  color: #000000;
  margin-top: 90rpx;
}

.form-btn {
  margin-top: 30rpx;

  button {
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 570rpx;
    height: 82rpx;
    border: 2rpx solid #a1ea2b;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: #0f0f0f;
  }
}
</style>
