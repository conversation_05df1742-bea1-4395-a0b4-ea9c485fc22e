<template>
  <custom-tabs v-model="current" class="record-tab">
    <template #right>
      <MerchantBusPick v-model="busId" />
    </template>

    <custom-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label">
      <view class="swiper">
        <PaginationList :tab-index="index" :current-index="current"></PaginationList>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import PaginationList from './components/PaginationList.vue'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
const busId = ref('')
const current = ref(0)
const tabList = ref([
  {
    value: 1,
    label: '团课',
  },
  {
    value: 2,
    label: '私教课',
  },
  {
    value: 4,
    label: '泳教课',
  },
  {
    value: 3,
    label: '场地',
  },
])

onLoad((options) => {
  //type 1团课 2私教
  current.value = options.type === '2' ? 1 : options.type === '4' ? 2 : options.type === '3' ? 3 : 0
})
onShareAppMessage((e) => {
  const dataset = e.target.dataset
  if (e.from === 'button' && dataset.action === 'space') {
    const userInfo = uni.getStorageSync('userInfo')
    return {
      title: `${userInfo?.username || ''} 邀请你领取入场凭证`,
      path: `/pages/my/ticketDetail?orderSn=${dataset.sn}&fromShare=1&bus_id=${dataset.busId}`,
      imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/share-ticket.png',
    }
  }
})
</script>

<style lang="scss">
.record-tab {
  .people-img {
    margin-right: 10rpx;
    width: 33rpx;
    height: 25rpx;
  }

  .swiper {
    height: 100%;
  }
  .record-item {
    position: relative;
    box-sizing: border-box;
    width: 690rpx;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    margin: 25rpx auto 0;
    overflow: hidden;
    font-size: 24rpx;
    .left {
      display: flex;
      align-items: flex-start;
    }
    .pic {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 26rpx;
    }
    .pic-thum {
      width: 161rpx;
      height: 94rpx;
      border-radius: 6rpx;
      margin-right: 14rpx;
    }
    .item-info {
      .name {
        display: flex;
        align-items: center;
        font-size: 30rpx;
        font-weight: bold;
        > text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.1;
        }
      }
      .des {
        margin-top: 20rpx;
        display: flex;
        justify-items: center;
      }
      .des-lef {
        font-weight: bold;
        margin-right: 8rpx;
        max-width: 200rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .des-num {
        color: #ff7427;
      }
      .item {
        display: flex;
        align-items: center;
        font-size: 23rpx;
        margin-top: 15rpx;
      }
    }
  }
}
</style>
