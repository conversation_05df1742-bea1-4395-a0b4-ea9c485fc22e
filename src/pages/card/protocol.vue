<template>
  <view class="bus-detail">
    <view class="rich-text">
      <rich-text :nodes="busInfo" type="text"></rich-text>
    </view>
  </view>
</template>

<script setup lang="ts" name="protocol">
import http from '@/utils/request'
import { unescapeHTML } from '@/utils/shared'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const busInfo = ref('')

onLoad(() => {
  getBusinessInfo()
})

const getBusinessInfo = () => {
  const params = {
    bus_id: userStore.userInfoBusId,
    loading: true,
  }
  http.get('Business/getBuycardProtocol', params).then((res) => {
    if (res.errorcode === 0) {
      busInfo.value = unescapeHTML(res.data.info.protocol_content || '暂无数据')
    }
  })
}
</script>
<style lang="scss" scoped>
text {
  color: #898989;
}
.bus-detail {
  overflow-y: scroll;
  flex: 1;
  box-sizing: border-box;
  padding: 20rpx;
  width: 100%;
  height: 100%;
  background-color: #fff;
  color: #000;
  .rich-text {
    padding: 0;
  }
  .title {
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #f5f7f9;
    height: 92rpx;
    font-size: 26rpx;
    text {
      padding-left: 16rpx;
      color: #313131;
    }
  }

}
</style>
