import { ComputedRef } from 'vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
interface CardItem {
  name: string
  card_user_id: string | number
  [propName: string]: any
}

interface PayTypeItem {
  pay_name: string
  pay_type: number
  icon?: string
}

export function usePayType(activityId: ComputedRef<string>, card: ComputedRef<CardItem>, bill: ComputedRef<string>) {
  const userStore = useUserStore()

  const payTypeState = reactive({
    payType: 1, // 1微信 8储值卡
    payTypeIdx: '0',
    payTypeList: [
      { pay_name: '微信', pay_type: 1 },
      { pay_name: '储值卡', pay_type: 8 },
    ] as PayTypeItem[],
    valueCardList: [] as CardItem[],
    valueCardIdx: 0,
    cardUserId: '', // 如果使用储值卡
    balance: '0', // 没有使用储值卡则为'0'
  })
  const isUseValueCard = computed(() => {
    // 是否使用储值卡
    return payTypeState.payType === 8
  })
  const useValueCardData = computed(() => {
    // 使用储值卡支付，需要展示的数据
    if (!isUseValueCard.value) {
      return {
        usage: 0, // 储值卡抵扣金额
        wxPayAmount: 0, // 抵扣后还需微信支付的部分，大于0则代表储值卡余额不足
        valueText: '', // 余额文本
        usageText: '', // 抵扣文本
      }
    }

    const theBill = +(activityId.value ? card.value.activity_card_info.curr_cost : +bill.value > 0 ? +bill.value : 0) // 活动卡售价 : 普通卡定金、折扣券后售价
    const valueCard = payTypeState.valueCardList[payTypeState.valueCardIdx]
    const usage = valueCard.balance >= theBill ? theBill : +valueCard.balance
    const wxPayAmount = +(theBill - usage).toFixed(2)
    const valueText = `(余额${valueCard.balance}${wxPayAmount > 0 ? '，还需' + wxPayAmount.toFixed(2) : ''})`
    const usageText = `储值卡已抵${usage}元`
    return {
      usage,
      wxPayAmount,
      valueText,
      usageText,
    }
  })

  function getUserValueCardList() {
    if (!userStore.userInfoUserId) {
      return
    }
    return http
      .get('Personalcenter/getValueCardPayList', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
      })
      .then((res) => {
        const resData = res.data
        payTypeState.valueCardList = resData.list || []
        // payTypeState.payTypeList = resData.pay_type_arr

        if (payTypeState.valueCardList.length && payTypeState.cardUserId) {
          const index = payTypeState.valueCardList.findIndex((v) => v.card_user_id === payTypeState.cardUserId)
          if (index !== -1) {
            payTypeState.valueCardIdx = index
            payTypeState.balance = payTypeState.valueCardList[index].balance
          }
        }
      })
  }

  const handlePayTpeChange = (data) => {
    payTypeState.payTypeIdx = data.index
    payTypeState.payType = data.payType
    if (payTypeState.payType === 8 && data.choseCard) {
      payTypeState.cardUserId = data.choseCard.card_user_id
      payTypeState.balance = data.choseCard.balance
    } else {
      payTypeState.cardUserId = ''
      payTypeState.balance = '0'
    }
  }

  const handleValueCardChange = (data) => {
    payTypeState.valueCardIdx = data.index
    if (data.choseCard) {
      payTypeState.cardUserId = data.choseCard.card_user_id
      payTypeState.balance = data.choseCard.balance
    }
  }

  return {
    payTypeState,
    isUseValueCard,
    useValueCardData,
    getUserValueCardList,
    handlePayTpeChange,
    handleValueCardChange,
  }
}
