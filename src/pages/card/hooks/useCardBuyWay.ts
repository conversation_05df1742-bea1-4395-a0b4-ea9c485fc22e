import { formatDate } from '@/utils/shared'

export function useCardBuyWay(cardRef: Ref<T> | T) {
  const card = computed(() => unref(cardRef))
  /* 开卡方式相关 */
  const state = reactive({
    buyIdx: 1,
    buyMethods: ['立即开卡', '到场后再激活'],
    endayStr: '',
    purchaseVolume: 0,
  })
  // 有效天数
  const validDays = computed(() => {
    const isActive = card.value.activity_card_info && card.value.activity_card_info.card_id
    const cardInfo = isActive ? card.value.activity_card_info : card.value
    const { end_time, card_type_id, is_pt_time_limit_card, gift_number, mc_gift_number } = cardInfo
    let buyNum = state.purchaseVolume
    if (isActive) {
      buyNum = cardInfo.pt_class_num
    }
    if ((card_type_id === 4 || card_type_id === 5) && is_pt_time_limit_card !== 1) {
      return end_time * (buyNum + mc_gift_number)
    }
    const otherNum = card_type_id === 1 ? gift_number : is_pt_time_limit_card === 1 ? mc_gift_number : 0
    return end_time + otherNum
  })

  const activeTime = computed(() => {
    const { end_time, card_type_id, is_pt_time_limit_card, gift_number, mc_gift_number } = card.value
    if (end_time === 0 && card_type_id !== 1 && is_pt_time_limit_card !== 1) {
      // forever of the card.
      state.endayStr = 'forever'
      return '永久有效'
    }
    // time of the card.
    const today = new Date(),
      todayStr = formatDate(today, 'yyyy.MM.dd')
    if (state.buyIdx === 0) {
      state.endayStr = formatDate(new Date(today.getTime() + 60 * 60 * 24 * 1000 * (validDays.value - 1)), 'yyyy.MM.dd')
      return todayStr + ' ~ ' + state.endayStr
    }
    state.endayStr = 'forever'
    return todayStr + ' ~ 未知'
  })
  // 处理变更开卡方式
  const handleBuyMethodsChange = (value) => {
    state.buyIdx = value
  }

  return {
    buyWayState: state,
    validDays,
    activeTime,
    handleBuyMethodsChange,
  }
}
