import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { Ref } from 'vue'
interface userFrontMoneyItem {
  front_id: number,
  amount: number,
  purpose: number,
  status: number,
}

interface couponItem {
  begin_date: string,
  end_date: string,
  limit_card: number,
  limit_amount: string | number,
  status: number,
  can_choose: number,
  coupon_receive_id: number,
  specify_card: [],
  use_limit: string
}

export function useCardDeduction(cardId: Ref<T> | T) {
  const userStore = useUserStore()
  const state = reactive({
    userFrontMoneyList: [] as Array<userFrontMoneyItem>, // 定金
    checkedBoxs: [], // 用于存放定金勾选状态
    deposit_ids: '' as string | number[], // 已勾选的定金id
    depositValue: '0', // 已勾选的定金总金额
    originalDepositValue: '0', // 初始勾选的定金金额
    isFromCouponPage: false,
    couList: [] as Array<couponItem>, // 折扣券
    // originalCouponValue: 0, // 初始的折扣券金额
    hasUse: false, // 是否有可用折扣券
    couponReceiveId: 0, // 折扣券id
    couponAmount: 0, // 折扣金额
  })

  const cardIdRef = computed(() => unref(cardId))

  // 获取定金折扣券数据
  const getDeductionData = () => {
    if (!userStore.userInfoUserId) {
      return false
    }
    const params = {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_id: cardIdRef.value || 0,
    }
    http.get<any>('Card/getDeductionAmount', params).then((res) => {
      if (res.errorcode === 0) {
        const resData = res.data.list

        /* 定金的初始化处理 */
        const frontIds: Array<number> = []
        let frontAmount = 0
        let originaAmount = 0

        Array.isArray(resData.user_front_money_list) &&
          resData.user_front_money_list.forEach((item) => {
            if (item.status == 1) {
              frontIds.push(item.front_id)
              frontAmount += +item.amount
            }
            originaAmount += +item.amount
          })
        state.userFrontMoneyList = resData.user_front_money_list || []
        state.deposit_ids = frontIds
        state.depositValue = frontAmount.toFixed(2)
        state.originalDepositValue = originaAmount.toFixed(2)

        /* 折扣券的初始化 */
        if (resData.coupon_list.length && resData.card_type_id != 6) {
          // state.originalCouponValue = resData.coupon_amount
          state.couponReceiveId = resData.coupon_receive_id
          state.hasUse = +resData.coupon_receive_id !== 0
          state.couponAmount = resData.coupon_amount
          state.couList = resData.coupon_list
        }
        // 部分折扣券处理见watch(total
      }
    })
  }

  // 处理定金选择
  const handleDepositChange = (e) => {
    const frontIds = []
    let frontAmount = 0

    state.checkedBoxs = e.detail.value
    state.userFrontMoneyList.forEach((item) => {
      if (state.checkedBoxs.indexOf(item.front_id + '') != -1) {
        item.status = 1
        frontIds.push(item.front_id)
        frontAmount += +item.amount
      } else {
        item.status = 0
      }
    })
    state.deposit_ids = frontIds
    state.depositValue = frontAmount.toFixed(2)
  }

  // 跳转到折扣券选择页面 并监听折扣券选择
  const handleToDiscountPage = () => {
    uni.navigateTo({
      url: '/pages/card/couponList',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        'confirm-coupon': ({ couponReceiveId, couponAmount }) => {
          if (couponReceiveId !== undefined) {
            state.couponReceiveId = couponReceiveId
            state.couponAmount = couponAmount
          } else {
            state.couponReceiveId = 0
            state.couponAmount = 0
          }
        },
      },
      success(res) {
        state.isFromCouponPage = true
        // 通过eventChannel向被打开页面传送数据
        const { couList, couponReceiveId, couponAmount } = state
        res.eventChannel.emit('init-coupon', {
          couList,
          couponReceiveId,
          couponAmount,
          showNotUse: true,
        })
      },
    })
  }

  return {
    deductionState: state,
    getDeductionData,
    handleDepositChange,
    handleToDiscountPage,
  }
}
