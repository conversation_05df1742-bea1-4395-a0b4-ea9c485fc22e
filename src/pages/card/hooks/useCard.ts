import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import { useCardDeduction } from './useCardDeduction'
import { useCardBuyWay } from './useCardBuyWay'
import { usePayType } from './usePayType'
import { unescapeHTML, getCardTypeUnit } from '@/utils/shared'

const { checkLogin } = useLogin()

interface CardItem {
  name: string
  card_user_id: string | number
  card_type_id: number
  [propName: string]: any
}

export function useCard() {
  // 购卡确认门店弹窗
  const showConfirmBus = ref(false)
  // 是否已经确认过购卡门店
  const hasCheckedBuyBus = ref(false)
  // 签署合同弹窗信息
  const signContractInfo = reactive({
    // card_disable 0 未签合同 会员卡/课可以使用 1未签合同 会员卡/课不可使用
    // sign_type 0免签 1仅线下签署 2会员端与线下签署
    card_disable: 1,
    sign_type: 0,
    order_id: '',
  })
  const indexKey = ref(0)
  const cardId = ref(0)
  const packageId = ref('')
  const activityId = ref('')
  const card = ref<CardItem>({}) // 卡课信息
  const saleIdx = ref(0)
  const coachId = ref()
  const saleId = ref('')
  const saleList = ref([{ id: '', name: '请选择' }])
  onLoad((options) => {
    indexKey.value = options.index
    coachId.value = options.coach_id
  })
  const userStore = useUserStore()

  const { deductionState, getDeductionData, handleDepositChange, handleToDiscountPage } = useCardDeduction(cardId)

  const { buyWayState, validDays, activeTime, handleBuyMethodsChange } = useCardBuyWay(card)

  const {
    payTypeState,
    isUseValueCard,
    useValueCardData,
    getUserValueCardList,
    handlePayTpeChange,
    handleValueCardChange,
  } = usePayType(
    computed(() => activityId.value),
    computed(() => card.value),
    computed(() => bill.value),
  )

  // 获取卡课数据
  function getCardData(shouldGetSaleList = false) {
    const url = cardId.value ? 'Card/getDetail' : 'Card/getPackageDetail'
    const params = {
      bus_id: userStore.userInfoBusId,
      index_key: indexKey.value || '',
      card_id: cardId.value,
      package_id: packageId.value,
      activity_id: activityId.value,
    }
    http.get<any>(url, params).then((res) => {
      if (res.errorcode === 0) {
        const { info } = res.data

        if (info.card_type_id == 6) {
          if (Array.isArray(info.card)) {
            info.card.forEach((v) => {
              // 合计
              v.customSum =
                (v.is_pt_time_limit_card == 1 || v.card_type_id == 1
                  ? v.all_days
                  : v.card_type_id == 3
                    ? v.total
                    : v.all_num) + getCardTypeUnit(v.card_type_id, v.is_pt_time_limit_card)
              // 单节有效期
              v.customValidSingle =
                [4, 5].includes(+v.card_type_id) && v.is_pt_time_limit_card != 1
                  ? v.all_days == 0
                    ? '永久'
                    : v.all_days / v.all_num + '天/节'
                  : '-'
              // 总有效期
              v.customValidTime = v.all_days != 0 ? v.all_days + '天' : '永久'
              // 适用门店
              v.customSupportBus = v.universal_card == 1 ? v.support_bus_count + '家可用' : '本店可用'
            })
          } else {
            info.card = []
          }
        }

        if (shouldGetSaleList) {
          saleList.value = [{ id: '', name: '请选择' }]
          if (info.sale_type && Array.isArray(info.sale_type)) {
            // sale_type  1-会籍  2-私教  3-泳教
            if (info.sale_type.indexOf(1) !== -1) {
              getSaleList()
            }
            const arr: Array<0 | 1 | 2> = []
            if (info.sale_type.indexOf(2) !== -1) {
              arr.push(1)
            }
            if (info.sale_type.indexOf(3) !== -1) {
              arr.push(2)
            }
            if (arr.length > 0) {
              getCoachList(arr.length > 1 ? 0 : arr[0]) // 0全部、1私教、2泳教
            }
          } else if (info.card_type_id === 4 || info.card_type_id === 5) {
            // private coach of the card.
            const type = info.card_type_id === 5 ? 2 : 1 // 1私教、2泳教
            getCoachList(type)
          } else {
            getSaleList()
            // any else card.
          }
        }
        card.value = {
          ...info,
          description: info.description ? unescapeHTML(info.description) : '暂无描述',
          activity_card_info: unref(activityId) && info.activity_card_info ? info.activity_card_info : {},
        }

        buyWayState.purchaseVolume = info.buy_min_value
        handleBuyMethodsChange(info.activation_restriction === 1 ? 0 : 1) // 1 默认到场后激活 activation_restriction === 1表示仅支持立即开卡
      }
    })
  }

  // 销售人员
  function getCoachList(type: 0 | 1 | 2) {
    return http
      .get('Coach/coachList', {
        bus_id: userStore.userInfoBusId,
        type, // 0全部、1私教、2泳教
      })
      .then((res) => {
        const coachList = res.data.list.map((item, index) => {
          if (item.id == coachId.value) {
            saleId.value = 'c' + item.id
            saleIdx.value = saleList.value.length + index
          }
          return {
            ...item,
            id: 'c' + item.id,
          }
        })
        saleList.value = saleList.value.concat(coachList)
      })
  }
  // 销售人员 会籍
  function getSaleList() {
    return http
      .get('Marketers/msList', {
        bus_id: userStore.userInfoBusId,
      })
      .then((res) => {
        saleList.value = saleList.value.concat(res.data.list)
      })
  }

  function handleSaleChange(event) {
    saleIdx.value = event.detail.value
    saleId.value = saleList.value[saleIdx.value].id || ''
  }

  // 精确加法
  function customAdd(num1, num2) {
    const num1Digits = (num1.toString().split('.')[1] || '').length
    const num2Digits = (num2.toString().split('.')[1] || '').length
    const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits))
    return (num1 * baseNum + num2 * baseNum) / baseNum
  }

  const total = computed(() => {
    if (card.value.activity_card_info?.card_id) {
      return parseFloat(card.value.activity_card_info.curr_cost).toFixed(2)
    }
    // 私教泳教课 但不是私教包月时
    if (card.value.is_pt_time_limit_card != 1 && (card.value.card_type_id == 4 || card.value.card_type_id == 5)) {
      let all = 0
      for (let i = 0; i < buyWayState.purchaseVolume; i++) {
        all = customAdd(all, parseFloat(card.value.single_price))
      }
      return all.toFixed(2)
    }

    return parseFloat(card.value.current_price).toFixed(2)
  })

  const bill = computed(() => {
    const val = customAdd(
      customAdd(total.value, -parseFloat(deductionState.depositValue)),
      -parseFloat(deductionState.couponAmount),
    )
    return val.toFixed(2)
  })

  watch(total, (val) => {
    if (card.value.is_pt_time_limit_card != 1 && (card.value.card_type_id == 4 || card.value.card_type_id == 5)) {
      // show coupon
      const today = new Date().getTime()
      let count = 0
      deductionState.couList.forEach((item) => {
        const begin = new Date(item.begin_date).getTime()
        // 需要加一天，防止当天到期的优惠券失效
        const end = new Date(item.end_date).getTime() + 24 * 60 * 60 * 1000
        if (item.status == 1 && begin < today && today < end) {
          if (item.limit_card == 4 && item.specify_card.findIndex((cd) => cd.id == cardId.value) === -1) {
            return false
          }
          if (
            item.limit_amount === null ||
            item.limit_amount === undefined ||
            Number(item.limit_amount) > Number(val) ||
            !item.use_limit.includes(1)
          ) {
            item.can_choose = 0
            if (deductionState.couponReceiveId === item.coupon_receive_id) {
              deductionState.couponReceiveId = 0
              deductionState.couponAmount = 0
            }
          } else {
            item.can_choose = 1
            count++
          }
        }
      })
      if (count > 0) {
        deductionState.hasUse = true
      } else {
        deductionState.hasUse = false
        deductionState.couponReceiveId = 0
        deductionState.couponAmount = 0
      }
    }
  })

  // watch(bill, (val) => {
  // submitState.isSubmit = val >= 0
  // getBuyCardPoint()
  // })

  /* 提交 */
  const submitState = reactive({
    isLogin: false,
    checked: 1,
    loading: false,
  })

  function setIsLogin(isLogin: boolean) {
    submitState.isLogin = isLogin
  }

  // 勾选是否同意协议
  const handleCheckboxChange = (checked) => {
    submitState.checked = checked
  }

  function setLoading(loading: boolean) {
    submitState.loading = loading
  }

  // 设置-签署合同弹窗信息
  function setSignContractInfo(card_disable: number, sign_type: number, order_id: any) {
    signContractInfo.card_disable = card_disable
    signContractInfo.sign_type = sign_type
    signContractInfo.order_id = order_id
  }

  function navigateToMyCard(order_sn: any) {
    uni.showLoading({
      title: '正在生成订单',
      mask: true,
    })
    if (order_sn) {
      const params = {
        order_sn: order_sn,
        showToast: false,
        loading: false,
      }
      const nowTime = new Date().getTime()
      // 轮训获取合同状态 超过十秒算失败 结束轮训
      const timer = setInterval(() => {
        if (new Date().getTime() - nowTime > 10000) {
          clearInterval(timer)
          uni.showToast({
            title: '请稍后到我的页面查看',
            icon: 'none',
          })
          setLoading(false)
          uni.hideLoading()
          return
        }
        http
          .post('ContractSetting/getContractSettingByOrderSn', params)
          .then((res) => {
            // signContractModal.value = true
            setSignContractInfo(Number(res.data.card_disable), Number(res.data.sign_type), res.data.order_id)
            uni.navigateTo({
              url: `/pages/card/buyCardSuccess?orderSn=${order_sn}&orderId=${res.data.order_id}`,
            })
            setLoading(false)
            uni.hideLoading()
            clearInterval(timer)
          })
          .catch(() => {
            uni.showLoading({
              title: '正在生成订单',
              mask: true,
            })
          })
      }, 2000)
    } else {
      setTimeout(() => {
        uni.redirectTo({
          url: '/pages/my/card',
          success() {
            setLoading(false)
            uni.hideLoading()
          },
        })
      }, 3000)
    }
  }

  function pay(info: UniApp.RequestPaymentOptions, order_sn) {
    uni.requestPayment({
      timeStamp: info.timeStamp,
      nonceStr: info.nonceStr,
      package: info.package,
      signType: info.signType,
      paySign: info.paySign,
      provider: 'wxpay',
      orderInfo: info.orderInfo || '',
      success: () => {
        navigateToMyCard(order_sn)
      },
      fail: () => {
        // fail
        uni.showToast({
          title: '取消支付',
          icon: 'none',
        })

        // 如果是储值卡组合支付，返还扣除的储值卡余额
        if (payTypeState.payType === 8 && useValueCardData.value.wxPayAmount > 0) {
          if (order_sn) {
            const params = {
              bus_id: userStore.userInfoBusId,
              user_id: userStore.userInfoUserId,
              card_user_id: payTypeState.cardUserId,
              business_id: order_sn, // 业务id，购卡和活动购卡传order_sn
              business_type: '3', // 业务类型，1--散场票，2--订场，3--购卡
              loading: true,
            }
            http
              .post('Booking/storedCardChangeRevoke', params)
              .then((res) => {
                if (res.errorcode != 0) {
                  uni.showToast({ title: res.data.errormsg, icon: 'none' })
                }
              })
              .finally(() => {
                setLoading(false)
              })
          }
        } else {
          setLoading(false)
        }
      },
    })
  }

  async function buyCard() {
    if (card.value.check_bus_buy_card === 1 && !hasCheckedBuyBus.value && card.value.universal_card == 1) {
      showConfirmBus.value = true
      setLoading(false)
      return
    }
    try {
      await checkLogin(true)
    } catch (error) {
      setLoading(false)
      return
    }
    if (submitState.isLogin === false) {
      submitState.isLogin = true
      await getDeductionData()
    }

    if (+bill.value < 0) {
      uni.showToast({
        title: '售价不能小于定金和折扣劵之和！',
        icon: 'none',
        duration: 2500,
      })
      setLoading(false)
      return
    }

    if (submitState.checked !== 1) {
      uni.showToast({ title: '请先确认协议', icon: 'none' })
      setLoading(false)
      return
    }

    let variable = 0
    switch (card.value.card_type_id) {
      case 4:
      case 5:
        // variable = card.value.buy_min_value
        // private coach of the card.
        // if (!!saleId) {
        variable = buyWayState.purchaseVolume
        // } else {
        //   wx.showToast({ title: '请选择教练！' });
        //   return false;
        // }
        break
      case 6:
        variable = 1
        break
      default:
        variable = card.value.number
        break
    }

    try {
      const params = {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        card_id: cardId.value,
        package_id: packageId.value,
        active_type: buyWayState.buyIdx + 1, // 激活类型 1立即激活，2到店激活
        end_date: buyWayState.endayStr.replace(/\./g, '-'),
        agree_buycard_protocol: submitState.checked,
        // amount: bill.value,
        amount: payTypeState.payType === 8 ? useValueCardData.value.wxPayAmount : bill.value,
        purchase_volume: variable, // 购买量
        ms_id: saleId.value || 0,
        deposit_amount: deductionState.depositValue,
        coupon_receive_id: deductionState.couponReceiveId,
        deposit_ids: Array.isArray(deductionState.deposit_ids) ? deductionState.deposit_ids.join() : '',
        coupon_amount: deductionState.couponAmount,
        pay_type: payTypeState.payType,
        card_user_id: isUseValueCard.value ? payTypeState.cardUserId : '',
        balance: isUseValueCard.value ? payTypeState.balance : '0',
      }

      const res = await http.post('Card/buyCard', params)
      if (res.errorcode == 0) {
        if (res.data?.info?.appId) {
          const order_sn = res.data?.info?.order_sn
          pay(res.data.info, order_sn)
        } else {
          setLoading(false)
          navigateToMyCard(res.data?.info?.order_sn || '')
        }
      }
    } catch (error) {
      setLoading(false)
    }
  }

  function buyActivityCard() {
    const params = {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      activity_id: activityId.value,
      card_id: cardId.value,
      ms_id: saleId.value || 0,
      index_key: indexKey.value,
      active_type: buyWayState.buyIdx + 1, // 激活类型 1立即激活，2到店激活
      pay_type: payTypeState.payType,
      card_user_id: isUseValueCard.value ? payTypeState.cardUserId : '',
      balance: isUseValueCard.value ? payTypeState.balance : '0',
    }

    http
      .post('Activity/activityBuyCard', params)
      .then((res) => {
        if (res.errorcode == 0) {
          if (res.data?.info?.appId) {
            const order_sn = res.data?.info?.order_sn
            pay(res.data.info, order_sn)
          } else {
            setLoading(false)
            navigateToMyCard(res.data?.info?.order_sn || '')
          }
        }
      })
      .catch(() => {
        setLoading(false)
      })
  }
  //积分相关
  const givePointObj = reactive({
    point: 0, // 购卡赠送积分
    extra_point: 0, // 活动购卡额外赠送积分
  })
  const pointPrice = computed(() => {
    const val = customAdd(total.value, -parseFloat(deductionState.couponAmount))
    return val > 0 ? val : 0
  })
  watch(pointPrice, () => getBuyCardPoint())
  // 获取购卡展示赠送积分
  function getBuyCardPoint() {
    http
      .get('Card/getBuyCardPoint', {
        bus_id: userStore.userInfoBusId,
        price: pointPrice.value,
        card_id: cardId.value,
        package_id: packageId.value,
      })
      .then((res) => {
        Object.assign(givePointObj, res.data.info)
      })
  }

  const handleSubmitClick = () => {
    setLoading(true)
    ;(activityId.value ? buyActivityCard : buyCard)()
  }

  // 点击支付-确认门店弹窗-确定门店后
  const handleBusConfirm = () => {
    hasCheckedBuyBus.value = true
    setLoading(true)
    buyCard()
  }

  return {
    /* 卡 */
    coachId,
    indexKey,
    validDays,
    activeTime,
    cardId: readonly(cardId),
    packageId: readonly(packageId),
    activityId: readonly(activityId),
    card,
    givePointObj,
    setCardId(id) {
      cardId.value = id ?? 0
    },
    setPackageId(id) {
      packageId.value = id ?? ''
    },
    setActivityId(id) {
      activityId.value = id ?? ''
    },
    getCardData,
    /* 折扣券、定金 */
    deductionState,
    getDeductionData,
    handleDepositChange,
    handleToDiscountPage,
    /* 开卡方式 */
    buyWayState,
    handleBuyMethodsChange,
    /* 销售人员 */
    saleIdx,
    saleId,
    saleList,
    handleSaleChange,
    /* 支付方式 */
    payTypeState,
    isUseValueCard,
    useValueCardData,
    getUserValueCardList,
    handlePayTpeChange,
    handleValueCardChange,
    /* 提交相关 */
    total,
    bill,
    submitState,
    setIsLogin,
    handleCheckboxChange,
    handleSubmitClick,
    // 签署合同信息
    signContractInfo,
    showConfirmBus,
    handleBusConfirm,
  }
}
