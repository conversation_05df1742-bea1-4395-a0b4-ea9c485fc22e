<template>
  <custom-tabs v-if="isConfigGet && theme8.list && theme8.list.length && theme8.setting.type==1" v-model="tabIndex">
    <custom-tab-pane v-for="(item, index) in theme8.list" :key="index" :label="item.name">
      <MonthPayList style="padding-top:30rpx" v-if="item.temp_type==5" :bus-id="busId" />
      <CardList
        v-if="busId && canShowList && item.temp_type != 5"
        :bus-id="busId"
        :is-show-tags="arrIndexOf(item.show_content, '1')"
        :type="+item.temp_type - 1"
        :card-group-prop="hasGroupIndex === index ? cardGroupId : ''"
      />
    </custom-tab-pane>
  </custom-tabs>
  <scroll-view class="cardlist-page" v-if="isConfigGet && theme8.list && theme8.list.length && theme8.setting.type==2" :scroll-into-view="intoViewId" scroll-y>
    <view class="theme-bg" v-for="(item, index) in theme8.list" :id="'item'+ item.temp_type" :key="index">
      <view class="box-tit">{{ item.name }}</view>
      <MonthPayList v-if="item.temp_type==5" :bus-id="busId" @on-success="handleSuccess" />
      <CardList
        v-if="busId && canShowList && item.temp_type != 5"
        :bus-id="busId"
        :temp-type="+item.temp_type"
        :is-show-tags="arrIndexOf(item.show_content, '1')"
        :type="+item.temp_type - 1"
         @on-success="handleSuccess" 
        :card-group-prop="hasGroupIndex === index ? cardGroupId : ''"
      />
    </view>
  </scroll-view>
  <view v-if="!(theme8.list && theme8.list.length)" class="nodata">暂未设置</view>
</template>

<script setup lang="ts" name="class">
import { arrIndexOf } from '@/utils/shared'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import CardList from './components/CardList.vue'
import MonthPayList from './components/MonthPayList.vue'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import { useThemeStore } from '@/store/theme'
import { Success } from '@icon-park/vue-next'
const themeStore = useThemeStore()
const { getParam } = useLogin()
const tabIndex = ref(0)
const hasGroupIndex = ref(0)
const busId = ref('')
const cardGroupId = ref('')
const loadOptions = ref()
const canShowList = ref(false)
const userStore = useUserStore()
const isConfigGet = ref(false)
const intoViewId = ref('')
const theme8 = computed(() => themeStore.theme8)
onLoad((options) => {
  loadOptions.value = options
})
onShow(async () => {
  const curOptions = await getParam(loadOptions.value.scene || '')
  busId.value = curOptions.bus_id || loadOptions.value.bus_id || userStore.userInfoBusId
  cardGroupId.value = curOptions.card_group_id || loadOptions.value.card_group_id || ''
  themeStore.getConfig({ type: 8, bus_id: busId.value }).then(() => {
    isConfigGet.value = true
    // 外部传入的type值 表示选中当前分类  1私教 2泳教 3套餐包 其它为会员卡
    // temp_type pc会员端装修页面后端定义的 1会员卡、2私教课、3泳教课、4套餐包
    if (curOptions.type || loadOptions.value.type) {
      themeStore.theme8.list.forEach((item, index) => {
        if (+item.temp_type === +(curOptions.type || loadOptions.value.type || 0) + 1) {
          tabIndex.value = index
          hasGroupIndex.value = index
          canShowList.value = true
        }
      })
    }
    canShowList.value = true
  })
})
const hasLoadSuccessNum = ref(0);
function handleSuccess(info) {
  const curTheme = theme8.value 
  hasLoadSuccessNum.value++
  if(curTheme.setting?.type==2 && hasLoadSuccessNum.value === curTheme.list.length) {
    setTimeout(() => {
      intoViewId.value = `item${curTheme.list[tabIndex.value].temp_type}`
    }, 1000);
  }
}
onShareAppMessage((options) => {
  return {
    path: `/pages/card/list?bus_id=${busId.value}&from=share`,
  }
})
</script>
<style lang="scss" scoped>
.cardlist-page {
  height: 100%;
  :deep(.card-list-wrap) {
    padding: 0 30rpx 10rpx;
  }
  .box-tit {
    margin: 10rpx 30rpx;
  }
}
</style>
