<template>
  <custom-tabs>
    <custom-tab-pane :label="`可用(${usableCouponList.length})`">
      <view class="card-page footer-hasfixed">
        <template v-if="usableCouponList.length">
          <view v-if="state.showNotUse" class="not-use-wrap" @tap="handleSelectCoupon(notUseItem)">
            <text>不使用折扣券</text>
            <uni-icons
              v-show="state.couponReceiveId === 0"
              type="checkbox-filled"
              size="20"
              color="#000000"
            ></uni-icons>
          </view>
          <CouponItem
            v-for="couponItem in usableCouponList"
            :key="couponItem.coupon_receive_id"
            :coupon="couponItem"
            @click-item="handleSelectCoupon"
          >
            <template #right>
              <uni-icons
                v-show="couponItem.coupon_receive_id == state.couponReceiveId"
                type="checkbox-filled"
                size="20"
                color="#000000"
              />
            </template>
          </CouponItem>
        </template>
        <view v-else class="nodata">暂无对应折扣券数据</view>
        <view class="fixed-bottom-wrap theme-bg">
          <button class="normal-btn" @tap="handleConfirm">确认</button>
        </view>
      </view>
    </custom-tab-pane>
    <custom-tab-pane :label="`不可用(${disabledCouponList.length})`">
      <view class="card-page">
        <template v-if="disabledCouponList.length">
          <CouponItem
            v-for="couponItem in disabledCouponList"
            :key="couponItem.coupon_receive_id"
            :is-show-btn="false"
            :coupon="couponItem"
          />
        </template>
        <view v-else class="nodata">暂无对应折扣券数据</view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts" name="couponList">
import customTabs from '@/components/custom-tabs/custom-tabs'
import customTabPane from '@/components/custom-tabs/custom-tab-pane'
import CouponItem from '@/components/couponItem.vue'
// import { useUserStore } from '@/store/user'
// const userStore = useUserStore()
const instanceEventChannel = ref()
const state = reactive({
  couponReceiveId: 0,
  couponAmount: 0,
  couponList: [] as Record<string, any>[],
  showNotUse: false,
})

const usableCouponList = computed(() => {
  return state.couponList.filter((v) => v.can_choose === 1)
})
const disabledCouponList = computed(() => {
  return state.couponList.filter((v) => v.can_choose === 0)
})
// 在test.vue页面，向起始页通过事件传递数据
onLoad((option) => {
  instanceEventChannel.value = getCurrentInstance().proxy.getOpenerEventChannel()

  // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
  instanceEventChannel.value.once('init-coupon', function ({ couList, couponReceiveId, couponAmount, showNotUse }) {
    state.couponReceiveId = couponReceiveId
    state.couponAmount = couponAmount
    state.couponList = couList
    state.showNotUse = !!showNotUse
  })
})

const notUseItem = ref({
  coupon_receive_id: 0,
  discount_amount: 0,
  can_choose: 1,
})

const handleSelectCoupon = (item) => {
  if (item.can_choose === 1) {
    state.couponReceiveId = item.coupon_receive_id
    state.couponAmount = +item.discount_amount
  }
}

const handleConfirm = () => {
  instanceEventChannel.value.emit('confirm-coupon', {
    couponReceiveId: state.couponReceiveId,
    couponAmount: state.couponAmount,
  })
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.card-page {
  overflow: hidden;
  box-sizing: border-box;
  padding: 46rpx 30rpx 0;
  min-height: 100%;
  .not-use-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    margin-bottom: 30rpx;
    padding: 0 28rpx;
    border-radius: 20rpx;
    width: 100%;
    height: 100rpx;
    background: var(--THEME-COLOR);
    color: #03080e;
  }
}
</style>
