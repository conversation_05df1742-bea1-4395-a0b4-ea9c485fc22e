<template>
  <view class="card-detail-container footer-hasfixed theme-bg">
    <image
      class="card-thumb-img"
      :src="card.thumb || 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png'"
      mode="aspectFill"
    />

    <view v-if="card.card_type_id" class="card-detail-wrap">
      <view class="card-name">
        {{ card.card_type_id == 6 ? card.card_name : card.name }}
        <view v-if="card.is_point_activity === 1" class="poins-tips">
          <uni-icons type="star-filled" size="10" color="#FF7427"></uni-icons>
          额外赠送积分
          <uni-icons type="star-filled" size="10" color="#FF7427"></uni-icons>
        </view>
      </view>
      <view v-if="card.card_type_id != 6" class="card-type-row">
        <text>{{ cardTypeName }}</text>
        <text> | {{ card.universal_card == 1 ? '多店' : '单店' }}</text>
        <text>
          |
          {{
            card.end_time
              ? `${
                  (card.card_type_id === 4 || card.card_type_id === 5) && card.is_pt_time_limit_card !== 1 ? '单节' : ''
                }有效期` +
                card.end_time +
                '天'
              : '永久有效'
          }}</text
        >
      </view>

      <!-- 课程/套餐内容 -->
      <view v-if="card.card_type_id == 6" style="margin-top: 38rpx">
        <CardPackageTable :cards="card.card" link />
      </view>
      <view v-else class="card-info-box">
        <view class="box-tit">购买详情</view>
        <view class="info-table">
          <!-- 私教包月 -->
          <template v-if="card.is_pt_time_limit_card == 1">
            <view class="item">
              <view class="label">实际有效天数</view>
              <view class="value">{{ card.end_time + card.mc_gift_number }} 天</view>
            </view>
            <view class="item">
              <view class="label">原有效天数</view>
              <view class="value">{{ card.end_time }} 天</view>
            </view>
            <view class="item">
              <view class="label">赠送</view>
              <view class="value">{{ card.mc_gift_number }} 天</view>
            </view>
            <view class="item">
              <view class="label">单节时长</view>
              <view class="value">{{ card.class_duration }} 分钟</view>
            </view>
            <view class="item">
              <view class="label">每日最多上课节数</view>
              <view class="value">{{ card.per_day_pt_class_num }} 节</view>
            </view>
            <view class="item">
              <view class="label">课程方式</view>
              <view class="value">1对{{ card.user_no || 1 }}</view>
            </view>
          </template>
          <!-- 私教/泳教 -->
          <template v-else>
            <template v-if="activityId">
              <view class="item">
                <view class="label">售价</view>
                <view class="value">{{ card.activity_card_info.curr_cost }} 元</view>
              </view>
              <view class="item">
                <view class="label">课时数量</view>
                <view class="value">{{ card.activity_card_info.pt_class_num }} 节</view>
              </view>
            </template>
            <template v-else>
              <view class="item">
                <view class="label">单节售价</view>
                <view class="value">
                  {{ card.single_price ? card.single_price + ' 元' : '价格面议' }}
                </view>
              </view>
              <view class="item">
                <view class="label">起售节数</view>
                <view class="value">{{ card.buy_min_value }} 节</view>
              </view>
            </template>
            <view v-if="card.mc_gift_number" class="item">
              <view class="label">赠送节数</view>
              <view class="value">{{ card.mc_gift_number }} 节</view>
            </view>
            <view class="item">
              <view class="label">单节时长</view>
              <view class="value">{{ card.class_duration }} 分钟</view>
            </view>
            <view class="item">
              <view class="label">{{
                card.card_type_id === 4 || card.card_type_id === 5 ? '单节有效期' : '有效期'
              }}</view>
              <view class="value">{{ card.end_time ? card.end_time + ' 天' : '永久有效' }}</view>
            </view>
            <view class="item">
              <view class="label">课程方式</view>
              <view class="value">1对{{ card.user_no || 1 }}</view>
            </view>
          </template>
        </view>
      </view>

      <view class="card-desc-box">
        <view class="box-tit">{{ card.card_type_id == 6 ? '套餐' : '课程' }}描述</view>
        <view class="info-des rich-text">
          <rich-text :nodes="card.description"></rich-text>
        </view>
      </view>
    </view>
  </view>

  <CardBottomWrap
    v-if="card.card_type_id"
    :card="card"
    :bill="bill"
    :total="total"
    :is-activity-card="!!activityId"
    @to-order="handleToBuyCardPage"
  />
</template>

<script setup lang="ts" name="cardDetail">
import CardPackageTable from './components/CardPackageTable'
import CardBottomWrap from './components/CardBottomWrap'
import { useCard } from './hooks/useCard.ts'
import { useLogin } from '@/hooks/useLogin.ts'

const { checkLogin } = useLogin()
const {
  activityId,
  coachId,
  indexKey,
  card, // 卡课数据
  total, // 总金额
  bill, // 优惠后
  setCardId,
  setPackageId,
  setActivityId,
  setIsLogin,
  getCardData,

  deductionState, // 定金折扣券相关数据
  getDeductionData,

  buyWayState, // 开卡方式相关
  submitState, // 提交相关
} = useCard()

onLoad((options) => {
  uni.setNavigationBarTitle({
    title: options.package_id ? '套餐详情' : '课程详情',
  })

  setCardId(options.card_id)
  setPackageId(options.package_id)
  setActivityId(options.activity_id)

  checkLogin(false).then(async () => {
    getCardData()
    // !options.activity_id && getDeductionData()
  })
})

const cardTypeName = computed(() => {
  const { card_type_id: type, is_pt_time_limit_card } = unref(card)
  const typeNames = {
    1: '期限卡',
    2: '次卡',
    3: '储值卡',
    4: '私教',
    5: '泳教',
  }
  return typeNames[type] + (is_pt_time_limit_card ? '包月' : '')
})

const handleToBuyCardPage = () => {
  const { card_id, package_id } = unref(card)

  uni.navigateTo({
    url: `/pages/card/buyCardDetail?card_id=${+card_id || ''}&package_id=${+package_id || ''}&activity_id=${
      activityId.value
    }&index=${indexKey.value}&coach_id=${coachId.value || ''}`,
  })
}
</script>

<style lang="scss" scoped>
.card-detail-container {
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20rpx 30rpx 178rpx;
  height: 100%;
  .box-tit {
    font-size: 30rpx;
  }
}
.card-thumb-img {
  display: block;
  margin: 0 auto;
  border-radius: 20rpx;
  width: 690rpx;
  height: 400rpx;
}
.card-name {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
  font-weight: bold;
  font-size: 36rpx;
}
.poins-tips {
  display: flex;
  font-size: 20rpx;
  margin-left: 10rpx;
  color: $theme-text-color-other;
}
.card-type-row {
  margin-top: 28rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: $theme-text-color-grey;
}
.card-info-box {
  margin-top: 38rpx;
  border-top: 1rpx solid #e8e8e8;
  border-bottom: 1rpx solid #e8e8e8;
  .info-table {
    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 28rpx;
      &:last-child {
        margin-bottom: 32rpx;
      }
    }
  }
}
.card-desc-box {
  .info-des {
    line-height: 1.7;
    font-size: 24rpx;
  }
}

[data-theme='dark'] {
  .card-detail-container {
    background-color: #000 !important;
    .card-info-box {
      border: 0;
    }
  }
}
</style>
