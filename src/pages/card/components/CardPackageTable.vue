<template>
  <view class="package-item">
    <view class="box-tit">套餐详情</view>
    <view class="table-box">
      <view class="package-header">
        <view class="th">课程名称</view>
        <view class="th">合计</view>
        <view class="th">单节有效期</view>
        <view class="th">总有效期</view>
        <view class="th">适用门店</view>
      </view>
      <view class="package-body">
        <view v-for="(item, index) in props.cards" :key="index" class="package-body-item">
          <view class="td td-tips-wrap">
            <view>{{ item.name }}</view>
            <view v-if="isShowTips" class="td-tips"
              >{{
                !item.activation_restriction
                  ? '任意时间到场可激活'
                  : item.activation_restriction == 1 ?'仅支持立即开卡':item.activation_restriction > 45
                  ? `${item.activation_restriction / 30}个月(${
                      item.activation_restriction
                    }天)未到场将自动激活`
                  : `${item.activation_restriction}天未到场将自动激活`
              }}
            </view>
          </view>
          <view class="td">{{ item.customSum }}</view>
          <view class="td">{{ item.customValidSingle }}</view>
          <view class="td">{{ item.all_days != 0 ? item.all_days + '天' : '永久' }}</view>
          <view class="td">
            <navigator
              v-if="item.universal_card == 1 && link"
              class="to-bus-btn"
              :url="`/pages/card/supportBusList?cardId=${item.card_id}`"
              hover-class="none"
            >
              {{ item.customSupportBus }}
            </navigator>
            <text v-else> {{ item.customSupportBus }}</text>
          </view>
        </view>
        <view></view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="CardPackageTable">
const props = defineProps({
  cards: {
    type: Array,
    default: () => [],
  },
  link: {
    type: Boolean,
    default: false,
  },
  showTips: {
    type: Boolean,
    default: false,
  },
})
const isShowTips = computed(() => {
  const arr = props.cards.filter(
    (item: any) => item.activation_restriction && item.activation_restriction > 0
  )
  return arr.length > 0 && props.showTips
})
</script>

<style lang="scss" scoped>
.package-item {
  padding: 0 20rpx 30rpx;
  border-radius: 10rpx;
  background: #f6f6f8;
  .box-tit {
    line-height: 80rpx;
    font-size: 30rpx;
  }
  .table-box {
    border-left: 1rpx solid #e5e5e5;
    border-top: 1rpx solid #e5e5e5;
    background: #fff;
    font-size: 20rpx;
    .package-header,
    .package-body-item {
      display: flex;
      .th:first-child,
      .td:first-child {
        min-width: 200rpx;
      }
    }
    .th,
    .td {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      border-right: 1rpx solid #e5e5e5;
      border-bottom: 1rpx solid #e5e5e5;
    }
    .td-tips-wrap {
      flex-direction: column;
    }
    .td-tips {
      font-size: 18rpx;
      color: $theme-text-color-other;
    }
  }
  .package-header {
    height: 78rpx;
  }
  .package-body {
    font-weight: bold;
    color: #1b1b1b;
  }
  .package-body-item {
    min-height: 70rpx;
  }
  .to-bus-btn {
    color: #06befc;
    text-decoration: underline;
  }
}

[data-theme='dark'] {
  .package-item {
    background: #0f0f0f;
    .table-box {
      background: #000;
      .package-body {
        color: #fff;
      }
    }
  }
}
</style>
