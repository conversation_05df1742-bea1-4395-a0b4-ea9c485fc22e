<template>
  <NavBar />
  <view class="experience-card-page footer-hasfixed theme-bg">
    <view class="experience-card-container">
      <view class="top-info">
        <view class="be-from">【{{ position }} {{ info.userName }} {{ info.ms_phone }}】</view>
        <view class="title">赠送给您一张体验卡</view>
      </view>

      <view class="card-body">
        <view class="card-item-box">
          <view class="card-title">
            <text class="name text-overflow">{{ info.cardName }}</text>
            <text class="surplus">
              ({{ info.number + (info.card_type_id == 1 ? '天' : '次') }})
            </text>
          </view>
        </view>

        <view class="card-info-box">
          <view class="box-tit">体验卡信息</view>
          <view class="info-table">
            <view class="item">
              <text class="label">有效期</text>
              <text class="value">{{ info.validity }}</text>
            </view>
            <view class="item">
              <text class="label">会员卡描述</text>
              <rich-text class="value rich-text nopt" :nodes="info.description"></rich-text>
            </view>
            <view class="item">
              <text class="label">备注</text>
              <text class="value">凭此卡可免费至{{ info.busName }}体验</text>
            </view>
          </view>
        </view>
        <GiftBusInfo :gym-info="info" />
      </view>
    </view>
  </view>
  <view class="fixed-bottom-wrap theme-bg">
    <view class="buttons">
      <button
        class="normal-btn"
        :disabled="position === '您的好友' && info.status != 0"
        @tap="emits('get-card')"
      >
        <blockquote v-if="position !== '您的好友' || info.status == 0">点击领取</blockquote>
        <blockquote v-else-if="info.status == 1">已领取</blockquote>
        <blockquote v-else-if="info.status == 99">已被领取</blockquote>
        <blockquote v-else>已赠送</blockquote>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts" name="pageExperienceCard">
import NavBar from '@/components/NavBar'
import GiftBusInfo from '@/pages/bonuses/components/GiftBusInfo.vue'

const props = defineProps({
  cardInfo: {
    type: Object,
    required: true,
  },
  position: {
    type: String,
    required: true,
  },
})
const emits = defineEmits(['get-card'])

// 统一渲染参数
const info = computed(() => {
  const {
    ms_name,
    nickname,
    ca_name,
    card_name,
    name,
    bu_name,
    address,
    bu_address,
    phone,
    bu_phone,
    ...rest
  } = props.cardInfo

  return {
    ...rest,
    userName: ms_name || nickname,
    cardName: ca_name || card_name,
    busName: name || bu_name,
    busAddress: address || bu_address,
    busPhone: phone || bu_phone,
  }
})
</script>

<style lang="scss" scoped>
.experience-card-page {
  // height: 100%;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/experience-card-page-top-bg.png')
    top left/contain no-repeat;

  .experience-card-container {
    overflow-y: auto;
    box-sizing: border-box;
    padding: 180rpx 30rpx 30rpx;
    // height: 100%;
  }

  .top-info {
    margin-bottom: 350rpx;
    text-align: center;
    font-size: 30rpx;
    .title {
      margin-top: 12rpx;
      font-weight: bold;
    }
  }

  .card-item-box {
    position: relative;
    padding: 30rpx 25rpx 0;
    width: 673rpx;
    height: 271rpx;
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/experience-card-item-bg.png')
      top left/cover no-repeat;
    border-radius: 22rpx;
    box-sizing: border-box;
    &::before {
      position: absolute;
      top: 25rpx;
      right: 38rpx;
      content: '免费';
      font-size: 36rpx;
      font-weight: bold;
      color: $theme-text-color-other;
    }
    &::after {
      position: absolute;
      bottom: 25rpx;
      right: 30rpx;
      content: '到店后激活使用';
      width: 221rpx;
      height: 60rpx;
      line-height: 58rpx;
      text-align: center;
      font-size: 24rpx;
      font-weight: bold;
      color: #fff;
      background-color: $theme-text-color-other;
      border-radius: 30rpx;
    }
    .card-title {
      margin-top: 80rpx;
      text-align: center;
      color: #03080e;
    }
    .name {
      display: inline-block;
      max-width: 300rpx;
      font-weight: bold;
      font-size: 40rpx;
      vertical-align: bottom;
    }
    .surplus {
      font-size: 24rpx;
    }
  }

  .card-info-box {
    margin-top: 30rpx;
    .box-tit {
      font-size: 30rpx;
    }
    .info-table {
      .item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 28rpx;
        &:last-child {
          margin-bottom: 32rpx;
        }
      }
    }
  }
}
</style>
