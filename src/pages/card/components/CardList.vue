<template>
  <view class="card-list-wrap theme-bg">
    <CardTags
      v-if="(type == 1 || type == 2) && isShowTags"
      :type="type == 2 ? 3 : 1"
      :card-group-prop="cardGroupProp"
      @on-change="tagChange"
    />
    <view class="card-list">
      <view v-for="item in cardList" :key="item.card_id || item.package_id">
        <CardItem
          :key="item.card_id || item.package_id"
          :item="item"
          :is-show-tags="isShowTags"
          link
        />
      </view>
    </view>
    <view v-if="!cardList.length" class="nodata">暂无数据</view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import CardItem from './CardItem'
import CardTags from './CardTags'
const props = defineProps({
  type: Number,
  tempType: Number,
  isShowTags: Boolean,
  activityId: String,
  cardGroupProp: String,
  busId: String,
})
const cardGroupId = ref('')
const cardList = ref([])
watchEffect(() => {
  const busId = props.busId
  if (busId) {
    getInfo(props.cardGroupProp)
  }
})
// watch(
//   () => props.cardGroupProp,
//   (val) => {
//     getInfo(props.cardGroupProp)
//   },
//   { immediate: true }
// )
const emit = defineEmits(['on-success'])
function getInfo(propsId) {
  const url = props.activityId ? 'Card/getActivityPointList' : 'Card/getList'
  const postData = {
    bus_id: props.busId,
  }
  if (props.activityId) {
    postData.activity_id = props.activityId
  } else {
    postData.type = props.type
    postData.card_group_id = propsId || cardGroupId.value
  }
  http.get(url, postData).then((res) => {
    cardList.value = res.data?.list || []
    emit('on-success', {
      cardList: cardList.value,
      tempType: props.tempType,
    })
  })
}
function tagChange(value) {
  cardGroupId.value = value
  getInfo()
}
</script>
<style lang="scss">
.card-list-wrap {
  min-height: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}
</style>
