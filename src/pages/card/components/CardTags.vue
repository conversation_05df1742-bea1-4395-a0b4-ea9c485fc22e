<template>
  <view v-if="tagList && tagList.length > 1" class="tag-view">
    <uni-data-checkbox
      v-model="tagCheckValue"
      mode="tag"
      :selected-color="themeColor"
      :localdata="tagList"
      :map="{ text: 'title', value: 'id' }"
      @change="taChange"
    ></uni-data-checkbox>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
const themeStore = useThemeStore()
const tagCheckValue = ref()
const themeColor = ref(`#${themeStore.theme1.fashion_color}`)

const props = defineProps({
  type: Number,
  cardGroupProp: String,
})
const userStore = useUserStore()
const tagList = ref<Record<string, any>>([])
watchEffect(() => {
  getTags()
})

const emits = defineEmits(['on-change'])
function getTags() {
  http
    .get('Card/getCardGroupList', {
      bus_id: userStore.userInfoBusId,
      type: props.type,
    })
    .then((res) => {
      let arr = [{ title: '全部', id: '' }]
      arr = arr.concat(res.data?.list)
      tagList.value = arr
      if (props.cardGroupProp) {
        tagCheckValue.value = +props.cardGroupProp
      }
    })
}

function taChange(info) {
  emits('on-change', info.detail.value)
}
</script>

<style lang="scss" scoped>
.tag-view {
  margin-top: -20rpx;
  margin-bottom: 30rpx;
}
</style>
