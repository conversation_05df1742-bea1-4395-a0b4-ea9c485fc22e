<template>
  <view class="fixed-bottom-wrap theme-bg">
    <view v-if="isNotBuyOnline" class="buttons">
      <button class="normal-btn" @tap="handleMakePhoneCall">电话咨询场馆</button>
    </view>
    <view v-else class="submit-row">
      <view v-if="props.card.is_open_buycard_protocol && isOrderPage" class="protocol-col theme-bg">
        <checkbox-group class="checkbox-group" @change="handleCheckboxChange">
          <checkbox :value="1" :checked="checked" />
          我已阅读并同意
          <navigator class="link-text" url="/pages/card/protocol" hover-class="none"> 《会员卡服务条款》 </navigator>
        </checkbox-group>
      </view>
      <view class="price-row">
        <view class="left-price">
          <view class="bill"><text class="unit">￥</text>{{ priceData.bill }}</view>
          <view class="mark-box">
            <text v-if="priceData.showTotal" class="total"> ￥{{ priceData.total }} </text>
            <text v-if="priceData.showUsageText" class="usage-text">{{ priceData.usageText }}</text>
          </view>
        </view>
        <view class="buttons custom">
          <button
            class="normal-btn"
            :loading="props.loading"
            :disabled="card.is_open_buycard_protocol && !checked && isOrderPage"
            @tap="handleSubmitClick"
          >
            {{ isOrderPage ? '立即支付' : '立即购买' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="CardDetailBottom">
import _ from 'lodash'

const props = defineProps({
  card: {
    // 被买的卡信息
    type: Object,
    required: true,
  },
  isSale: {
    // 是否使用了定金、折扣券
    type: Boolean,
    default: false,
  },
  bill: {
    // 普通卡定金、折扣券后售价
    type: String,
    required: true,
  },
  total: {
    // 普通卡原价
    type: [Number, String],
    required: true,
  },
  useValueCardData: {
    // 使用储值卡支付需要展示的数据
    type: Object,
    default: () => ({}),
  },
  isUseValueCard: {
    // 是否储值卡支付
    type: Boolean,
    default: false,
  },
  isOrderPage: {
    type: Boolean,
    default: false,
  },
  isActivityCard: {
    // 是否购卡活动会员卡
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['checked', 'to-order', 'submit'])

const checked = ref(0)
// 是否价格面议，是则按钮为电话咨询
const isNotBuyOnline = computed(() => {
  if (props.isActivityCard) {
    return false
  }
  const { card_type_id, is_pt_time_limit_card, current_price, single_price } = props.card
  return (
    ((is_pt_time_limit_card == 1 || !'4,5'.includes(card_type_id)) && current_price == 0) ||
    (is_pt_time_limit_card != 1 && '4,5'.includes(card_type_id) && single_price == 0)
  )
})

const priceData = computed(() => {
  const { curr_cost, ori_cost } = props.card.activity_card_info // 购卡活动 活动价/原价
  const { wxPayAmount, usageText } = props.useValueCardData // 储值卡支付时还需微信补款金额
  const isA = props.isActivityCard // 是否购卡活动会员卡
  const isV = props.isUseValueCard // 是否储值卡支付
  const isS = props.isSale // 是否使用了定金、折扣券
  const bill = +props.bill > 0 ? props.bill : 0

  return {
    bill: isV ? wxPayAmount : isA ? curr_cost : bill,
    total: isA ? ori_cost : props.total,
    showTotal: isA ? !!ori_cost : isS,
    showUsageText: isV,
    usageText,
  }
})

const handleCheckboxChange = (e) => {
  checked.value = +e.detail.value[0] || 0
  emits('checked', checked.value)
}

// 点击电话咨询按钮
const handleMakePhoneCall = () => {
  uni.makePhoneCall({
    phoneNumber: props.card.phone,
  })
}

const handleSubmitClick = _.throttle(() => {
  if (!props.loading) {
    emits(props.isOrderPage ? 'submit' : 'to-order')
  }
}, 2000)
</script>

<style lang="scss" scoped>
.fixed-bottom-wrap {
  .submit-row {
    width: 100%;
  }
  .protocol-col {
    display: flex;
    position: absolute;
    left: 20rpx;
    top: 0;
    flex-direction: row;
    width: 100%;
    height: 50rpx;
    line-height: 50rpx;
    font-size: 24rpx;
    color: $theme-text-color-grey;
    transform: translateY(-100%);
    // navigator {
    //   color: #4a68a4;
    //   display: inline;
    // }
  }
  .price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .left-price {
    display: flex;
    align-items: center;
    .bill {
      margin-right: 7px;
      font-weight: bold;
      font-size: 36rpx;
      color: $theme-text-color-other;
      .unit {
        font-size: 24rpx;
      }
    }
    .total {
      text-decoration: line-through;
      font-size: 20rpx;
      color: $theme-text-color-grey;
    }

    .mark-box {
      display: flex;
      flex-direction: column;
    }
    .usage-text {
      align-self: flex-end;
      font-size: 24rpx;
      color: $theme-text-color-other;
    }
  }
  .buttons.custom {
    width: 296rpx;
  }
}
</style>
