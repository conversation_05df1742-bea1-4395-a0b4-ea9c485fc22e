<template>
  <view>
    <uni-popup ref="msgPopup" @change="handleStatusChange">
      <view class="invitation-container">
        <view class="invitation-box">
          <!-- 通用 -->
          <image src="/static/img/success.png"></image>
          <view class="title">购买成功</view>
          <!-- 1.开启签署显示 -->
          <view v-if="signContractInfo.sign_type !== 0 && signContractInfo.card_disable === 1" class="desc"
            >会员卡/课程需要在合同签署完成后才能使用</view
          >
          <!-- 2.开启签署并仅线下签署显示 -->
          <view v-if="signContractInfo.sign_type === 1 && signContractInfo.card_disable === 1" class="desc"
            >请联系工作人员完成合同签署</view
          >
        </view>
        <view class="invitation-btn buttons custom">
          <!-- 允许会员端签署后显示 -->
          <button v-if="signContractInfo.sign_type === 2" class="normal-btn normal-btn-min" @tap="handleToSignContract">
            进行合同签署
          </button>
          <navigator url="/pages/my/card" class="normal-btn normal-btn-min outer-org nav-btn">查看会员卡</navigator>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { goUrlPage } from '@/utils/urlMap'
import { useUserStore } from '@/store/user'

const emits = defineEmits(['closeModal'])

const props = defineProps({
  // 是否展示弹窗
  show: {
    type: Boolean,
  },
  // 签署的合同信息
  signContractInfo: {
    type: Object,
  },
})

const userStore = useUserStore()

const msgPopup = ref()

watch(
  () => props.show,
  (val) => {
    if (val) {
      showPopup()
    }
    if (!val) {
      closePopup()
    }
  }
)

async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}

async function closePopup() {
  if (msgPopup.value) {
    msgPopup.value.close()
  } else {
    await nextTick()
    msgPopup.value.close()
  }
}

// 弹窗关闭切换事件
function handleStatusChange(info) {
  if (!info.show) {
    emits('closeModal')
  }
}

// 进行签署-跳转至对应合同的签署
function handleToSignContract() {
  const orderId = props?.signContractInfo?.order_id || ''
  if (orderId) {
    goUrlPage(`/packageMy/my/contractDetail?orderId=${orderId}&isSignContract=1&bus_id=${userStore.userInfoBusId}`)
  }
}
</script>

<style lang="scss" scoped>
.invitation-container {
  padding: 36rpx;
  padding-top: 52rpx;
  background: #fff;
  border-radius: 20rpx;
  .invitation-box {
    width: 600rpx;
    margin-bottom: 52rpx;
    color: #000000;
    display: flex;
    flex-direction: column;
    align-items: center;

    image {
      width: 260rpx;
      height: 260rpx;
    }

    .title {
      font-size: 48rpx;
      line-height: 50rpx;
      margin-top: 60rpx;
      font-weight: bold;
    }

    .desc {
      font-size: 30rpx;
      line-height: 50rpx;
      margin-top: 30rpx;
    }
  }
  .invitation-btn {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;

    .normal-btn {
      width: 520rpx;
    }

    .nav-btn {
      border: 0;
      margin-top: 30rpx;
      background: none;
      border-radius: 0;
      color: #7d7d7d;
    }
  }
}
</style>
