<template>
  <view class="monthpay-list theme-bg">
    <div class="item" v-for="item in cardList" :key="item" @click="handleDetail(item.id)">
        <div class="album">
          <img class="photo" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/payscore-card-bg.png" />
          <!-- 卡种类型(1期限卡，2次数卡，3储值卡,4私教卡 5泳教卡) -->
          <div class="card-type" > {{ getCardTypeName(item) }}</div>
        </div>
        <div class="info">
          <div class="title">{{ item.member_card_name }}</div>
          <div class="desc" v-if="item.dec_term != -1">期限 {{ item.dec_term }} 个月</div>
          <div class="desc" v-else>无期限</div>
        </div>
        <div class="price">
          <div class="amount">¥{{ item.dec_price }}</div>
        </div>
      </div>
    <view v-if="!cardList.length" class="nodata">暂无数据</view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { goUrlPage } from '@/utils/urlMap'
import CardItem from './CardItem'
import CardTags from './CardTags'
const props = defineProps({
  busId: String,
})
const cardGroupId = ref('')
const cardList = ref([])
const emit = defineEmits(['on-success'])
watchEffect(() => {
  const busId = props.busId
  if (busId) {
    getInfo(props.cardGroupProp)
  }
})
const handleDetail = (id) => {
  goUrlPage(`/pages/payscore/detail?bus_id=${props.busId}&id=${id}`)
}
function getCardTypeName(info) {
  const typeName =
    info.card_type_id == 1
      ? '期限卡'
      : info.card_type_id == 2
      ? '次卡'
      : info.card_type_id == 3
      ? '储值卡'
      : info.card_type_id == 4
      ? '私教'
      : info.card_type_id == 5 ? '泳教':'健身卡'
  const timeName = info.is_pt_time_limit_card ? '包月' : ''
  return typeName + timeName
}
function getInfo(propsId) {
  const postData = {
    bus_id: props.busId,
    limit: 999,
    page: 1,
    loading: true,
  }
  http.post('/Contract/queryBusProgrammeList', postData).then((res) => {
    const first10 = res.data.slice(0, 10)
    cardList.value = first10
    setTimeout(() => {
      cardList.value = res.data
      emit('on-success', {
        cardList: cardList.value,
        tempType: 5,
      })
    }, 500)
  })
}
</script>
<style lang="scss" scoped>
 .monthpay-list {
    padding: 0 30rpx 30rpx;
    .item {
      height: 98rpx;
      padding: 30rpx;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      border-radius: 10rpx;
      margin-bottom: 20rpx;
      display: flex;
      flex-direction: row;

      .album {
        position: relative;
        width: 161rpx;
        height: 94rpx;

        .photo {
          width: 161rpx;
          height: 94rpx;
        }

        .card-type {
          font-size: 20rpx;
          font-weight: 800;
          color: #795733;
          position: absolute;
          margin-top: -60rpx;
          margin-left: 13rpx;
        }
      }

      .info {
        margin-left: 20rpx;

        .title {
          font-size: 26rpx;
          font-weight: bold;
          color: #000000;
          width: 333rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 8rpx;
        }

        .desc {
          font-size: 24rpx;
          font-weight: 400;
          color: #7d7d7d;
          margin-top: 10rpx;
        }
      }

      .price {
        margin-left: auto;
        display: flex;
        justify-content: center;
        align-items: center;

        .amount {
          font-size: 36rpx;
          font-weight: bold;
          color: $theme-text-color-other;
        }
      }
    }
  }
</style>
