<template>
  <view>
    <uni-popup ref="msgPopup" :is-mask-click="false" @change="handleStatusChange">
      <view class="distance-container">
        <view class="distance-box">
          <view class="title">
            <image class="icon" mode="aspectFit" src="/static/img/address.png" />
            当前门店
          </view>
          <view class="desc-wrap">
            <view class="desc">{{userBusName}}</view>
            <view class="distance-desc"><text v-if="distance">距你约 {{distance}}，</text>确认订单后将无法修改。</view>
          </view>
          
        </view>
        <view class="distance-btn buttons custom">
          <!-- 允许会员端签署后显示 -->
          <button class="normal-btn normal-btn-min outer-gray" @tap="handleToSwitchBus">
            更换门店
          </button>
          <button class="normal-btn normal-btn-min" @tap="handleStoreConfirm">
            确定门店
          </button>
        </view>
      </view>
    </uni-popup>
  </view>

  <!-- 位置权限弹窗 -->
  <!-- <LocationPermissionDialog :show="showLocationDialog" direction="down" :distance="80" @close="showLocationDialog = false" @enable="() => {
    getDistance()
    showPopup()
  }" /> -->
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { goUrlPage } from '@/utils/urlMap'
import { useUserStore } from '@/store/user'
// import LocationPermissionDialog from '@/components/LocationPermissionDialog.vue'

const emits = defineEmits(['closeModal','busConfirm'])

const props = defineProps({
  // 是否展示弹窗
  show: {
    type: Boolean,
  },
  // 签署的合同信息
  signContractInfo: {
    type: Object,
  },
})

const userStore = useUserStore()
const userBusName = computed(() => {
  return userStore.userInfo.bus_name
})
const msgPopup = ref()
const distance = ref(0)
// const showLocationDialog = ref(false)
function getDistance() {
  const { latitude, longitude } = userStore.locationInfo
  if(!latitude || !longitude) {
    distance.value = 0
    return
  }
  http
    .get('Personalcenter/checkBusAdd', {
      bus_id: userStore.userInfo.bus_id,
      lng: longitude,
      lat: latitude,
      loading: true,
    })
    .then((res) => {
      distance.value = res.data?.distance || 0
    })
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      userStore.getLocationInfo().then((res) => {
        // showLocationDialog.value = userStore.getShowLocationDialog()
        getDistance()
        showPopup()
      }).catch((err) => {
        console.log(err)
        showPopup()
      })
    }
    if (!val) {
      closePopup()
    }
  }
)

async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}

async function closePopup() {
  if (msgPopup.value) {
    msgPopup.value.close()
  } else {
    await nextTick()
    msgPopup.value.close()
  }
}

// 弹窗关闭切换事件
function handleStatusChange(info) {
  if (!info.show) {
    emits('closeModal')
  }
}
// 确定门店
function handleStoreConfirm(info) {
  closePopup()
  emits('busConfirm')
}

function handleToSwitchBus() {
  uni.navigateTo({
    url: '/pages/busSelect',
  })
}
</script>

<style lang="scss" scoped>
.distance-container {
  padding: 48rpx 30rpx;
  background: #fff;
  border-radius: 20rpx;
  .distance-box {
    width: 100%;
    margin-bottom: 48rpx;
    color: #000;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      line-height: 50rpx;
      .icon {
        width: 30rpx;
        height: 30rpx;
      }
    }
    .desc-wrap {
      box-sizing: border-box;
      width: 100%;
      padding: 48rpx 28rpx;
      margin-top: 48rpx;
      background: #F6F6F8;
    }
    .desc {
      font-size: 36rpx;
      font-weight: bold;
    }

    .distance-desc {
      font-size: 26rpx;
      line-height: 30rpx;
      margin-top: 40rpx;
      color: #333;
    }
  }
  .distance-btn {
    position: relative;
    width: 100%;
    display: flex;
    .outer-gray {
      margin-right: 20rpx;
    }
  }
}
</style>
