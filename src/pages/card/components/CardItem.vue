<template>
  <view class="card-item" @tap="goDetail">
    <view class="lef">
      <view class="img-view">
        <image class="card-img" :src="item.thumb" mode="scaleToFill" />
        <view v-if="item.is_point_activity === 1" class="pic-bot">
          <uni-icons type="star-filled" size="10" color="#FF7427"></uni-icons>
          额外赠送积分
        </view>
      </view>
      <view class="lef-rig">
        <view class="tit">
          <text
            v-if="
              isShowTags &&
              (item.card_type_id == 4 || item.card_type_id == 5) &&
              item.card_group_title
            "
            >[{{ item.card_group_title }}]</text
          >
          {{ item.name || item.card_name }}
        </view>
        <slot name="middle">
          <view v-if="item.card_type_id != 6" class="des">
            {{ getCardTypeName(item) }}
            <text>| {{ item.universal_card == 1 ? '多店' : '单店' }}</text>
            <text> | {{ item.end_time ? '有效期' + item.end_time + '天' : '永久有效' }}</text>
          </view>
        </slot>
        <slot name="bottom">
          <view v-if="item.card_type_id == 4 || item.card_type_id == 5" class="item">
            时长: {{ item.class_duration }}分钟
          </view>
          <view v-if="item.card_type_id != 4 && item.card_type_id != 5" class="bot">
            <text class="price">{{
              item.current_price ? `￥${item.current_price}` : '价格面议'
            }}</text>
            <text v-if="item.card_type_id != 6" class="tag">
              {{ item.card_type_id == 1 ? item.end_time : item.number }}
              {{ getCardTypeUnit(item.card_type_id, item.is_pt_time_limit_card) }}
              <text v-if="item.gift_number">
                +赠送{{ item.gift_number }}{{ getCardTypeUnit(item.card_type_id, item.is_pt_time_limit_card) }}
              </text>
            </text>
          </view>
        </slot>
      </view>
    </view>
    <slot name="right">
      <view v-if="item.card_type_id == 4 || item.card_type_id == 5" class="rig">
        <view v-if="!item.is_pt_time_limit_card && item.single_price" class="price">
          <text>￥{{ item.single_price }}/</text> <text class="unit">节</text>
        </view>
        <view v-else-if="item.is_pt_time_limit_card && item.current_price" class="price">
          <text>￥{{ item.current_price }}</text>
        </view>
        <view v-else class="price">价格面议</view>
        <view v-if="item.is_pt_time_limit_card && item.mc_gift_number" class="tag">
          赠送{{ item.mc_gift_number }}天
        </view>
      </view>
    </slot>
  </view>
</template>

<script setup lang="ts">
import { arrIndexOf, getCardTypeUnit } from '@/utils/shared'
const props = defineProps({
  item: Object,
  isShowTags: {
    type: Boolean,
    default: true,
  },
  link: {
    type: Boolean,
    default: false,
  },
  coachId: {
    type: [String, Number],
    default: '',
  },
})

const emits = defineEmits(['click-card'])

function getCardTypeName(info) {
  const typeName =
    info.card_type_id == 1
      ? '期限卡'
      : info.card_type_id == 2
      ? '次卡'
      : info.card_type_id == 3
      ? '储值卡'
      : info.card_type_id == 4
      ? '私教'
      : '泳教'
  const timeName = info.is_pt_time_limit_card ? '包月' : ''
  return typeName + timeName
}
function goDetail() {
  const { link, item } = props
  if (props.link) {
    const { card_id, package_id } = item
    uni.navigateTo({
      url: `/pages/card/${
        [1, 2, 3].includes(+item.card_type_id) ? 'buyCardDetail' : 'cardDetail'
      }?card_id=${+card_id || ''}&package_id=${+package_id || ''}&coach_id=${props.coachId || ''}`,
    })
  } else {
    emits('click-card', props.item)
  }
}
</script>

<style lang="scss">
.card-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  border: 1rpx solid var(--THEME-COLOR);
  border-radius: 20rpx;
  .bot {
    display: flex;
    align-items: center;
    .tag {
      margin-left: 10rpx;
    }
  }
  .lef {
    display: flex;
    overflow: hidden;
    align-items: flex-start;
    flex: 1;
    .lef-rig {
      flex: 1;
      min-width: 0;
    }
  }
  .tag {
    padding: 0 14rpx;
    border: 1rpx solid $theme-text-color-other;
    border-radius: 14rpx;
    height: 29rpx;
    line-height: 29rpx;
    text-align: center;
    font-size: 20rpx;
    color: $theme-text-color-other;
  }
  .rig {
    .tag {
      margin-top: 12rpx;
    }
  }
  .tit {
    overflow: hidden;
    margin-bottom: 13rpx;
    text-overflow: ellipsis;
    font-weight: bold;
    font-size: 30rpx;
    white-space: nowrap;
  }
  .des {
    margin-bottom: 13rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: $theme-text-color-grey;
  }
  .price {
    font-weight: bold;
    font-size: 30rpx;
    color: $theme-text-color-other;
    .unit {
      font-weight: normal;
      font-size: 26rpx;
    }
  }
  .img-view {
    flex-shrink: 0;
    margin-right: 20rpx;
    width: 161rpx;
    .pic-bot {
      font-size: 20rpx;
      margin-top: 15rpx;
      color: $theme-text-color-other;
    }
    .card-img {
      width: 161rpx;
      height: 94rpx;
    }
  }
}
</style>
