<template>
  <CardList v-if="busId && activityId" :busId="busId" :activityId="activityId" />
</template>

<script setup lang="ts" name="class">
import CardList from './components/CardList'
import { useUserStore } from '@/store/user'
const busId = ref('')
const activityId = ref('')
const userStore = useUserStore()
onLoad((options) => {
  busId.value = options.bus_id || userStore.userInfoBusId
  activityId.value = options.activityId
})

onShareAppMessage((options) => {
  return {
    path: `/pages/card/pointCard?bus_id=${busId.value}&activityId=${activityId.value}&from=share`
  }
})
</script>
<style lang="scss">
.switch-wrap {
  padding: 10rpx;
}
</style>



