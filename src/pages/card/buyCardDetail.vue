<template>
  <view class="card-detail-container footer-hasfixed theme-bg">
    <view v-if="[1, 2, 3].includes(card.card_type_id)" class="card-detail-top">
      <CardItem :item="card" />
    </view>

    <view v-if="card.card_type_id" class="card-detail-wrap" :class="{ 'padding-b50': card.is_open_buycard_protocol }">
      <view class="form-items">
        <!-- 课程/套餐名称 4 5 6 -->
        <view v-if="[4, 5, 6].includes(card.card_type_id)" class="item name-item">
          <text class="label">{{ card.card_type_id === 6 ? '套餐名称' : '课程名称' }}</text>
          <view class="value">{{ card.card_type_id === 6 ? card.card_name : card.name }}</view>
        </view>

        <!-- 适用门店 -->
        <view v-if="card.card_type_id != 6" class="item">
          <text class="label">购买门店</text>
          <view>
            <text>
              {{ userBusName }}
            </text>
          </view>
        </view>
        <view v-if="card.card_type_id != 6" class="item">
          <text class="label">适用门店</text>
          <view>
            <text>
              {{ card.universal_card == 1 ? card.support_bus_count + '家' : card.bus_name }}
            </text>
            <navigator
              v-if="card.universal_card == 1"
              class="rig-text"
              :url="`/pages/card/supportBusList?cardId=${card.card_id}`"
              hover-class="none"
            >
              <view>查看适用门店</view>
            </navigator>
          </view>
        </view>

        <!-- 适用教练 4 5 -->
        <!-- <view v-if="[4, 5].includes(card.card_type_id)" class="item">
          <text class="label">适用教练</text>
          <view>
            <text class="value">{{ card.support_coach_count }}名</text>
            <navigator
              v-if="card.support_coach_count > 0"
              class="rig-text"
              :url="`/pages/bus/coachList?card_id=${card.card_id}`"
              hover-class="none"
            >
              <view>查看适用教练</view>
            </navigator>
          </view>
        </view> -->

        <!-- 开卡方式 -->
        <view class="item" v-if="card.activation_restriction !== 1">
          <text class="label">开卡方式</text>
          <picker
            :value="buyWayState.buyIdx"
            :range="buyWayState.buyMethods"
            @change="
              (event) => {
                handleBuyMethodsChange(+event.detail.value)
              }
            "
          >
            <view class="value rig-sel"> {{ buyWayState.buyMethods[buyWayState.buyIdx] }}</view>
          </picker>
        </view>
        <view v-if="buyWayState.buyIdx === 1 && card.activation_restriction && card.activation_restriction !== 1" class="item-tips">
          {{
            card.activation_restriction > 45
              ? `${card.activation_restriction / 30}个月(${card.activation_restriction}天)`
              : `${card.activation_restriction}天`
          }}未到场，将自动激活
        </view>

        <!-- 会员卡数值 12345 -->
        <template v-if="card.card_type_id != 6">
          <!-- 有效天数 v-if="card.card_type_id == 1 || card.is_pt_time_limit_card == 1" -->
          <view class="item">
            <view class="label">有效天数</view>
            <view v-if="card.end_time != 0" class="value">
              {{ validDays }}
              天
            </view>
            <view v-else class="value">永久有效</view>
          </view>

          <!-- 可用次数 2 -->
          <view v-if="card.card_type_id == 2" class="item">
            <view class="label">可用次数</view>
            <view class="value">{{ card.number + card.gift_number }} 次</view>
          </view>

          <!-- 可用金额 3 -->
          <view v-if="card.card_type_id == 3" class="item">
            <view class="label">可用金额</view>
            <view class="value">{{ card.number + card.gift_number }} 元</view>
          </view>

          <!-- 购买节数 4 5 -->
          <template v-if="[4, 5].includes(card.card_type_id) && card.is_pt_time_limit_card != 1">
            <view class="item">
              <view class="label">购买节数</view>
              <view class="value">
                <text v-if="activityId">{{ card.activity_card_info.pt_class_num }} 节</text>
                <uni-number-box v-else v-model="buyWayState.purchaseVolume" :min="card.buy_min_value" :max="9999" />
              </view>
            </view>
            <view v-if="card.mc_gift_number" class="item">
              <view class="label">赠送节数</view>
              <view class="value">{{ card.mc_gift_number }} 节</view>
            </view>
          </template>

          <!-- 有效期 -->
          <view class="item">
            <view class="label">有效期</view>
            <view class="value">{{ activeTime }}</view>
          </view>
        </template>

        <!-- 会员卡描述 123 -->
        <view
          v-if="[1, 2, 3].includes(card.card_type_id)"
          class="desc-item"
          :class="{ item: card.description === '暂无描述' }"
        >
          <view class="label">会员卡描述</view>
          <view v-if="card.description === '暂无描述'" class="value">{{ card.description }}</view>
          <view v-else class="info-des rich-text">
            <rich-text :nodes="card.description"></rich-text>
          </view>
        </view>

        <!-- 定金 -->
        <template v-if="!activityId && deductionState.userFrontMoneyList.length">
          <view class="item deposit-item">
            <view class="label">定金</view>
            <view class="value">¥ {{ deductionState.depositValue }}</view>
          </view>
          <view class="item deposit-item">
            <checkbox-group @change="handleDepositChange">
              <label v-for="item in deductionState.userFrontMoneyList" :key="item.front_id" class="check-box-row">
                <view>
                  <checkbox :value="item.front_id" :checked="!!item.status" />
                  {{
                    `启用定金(${item.purpose == 0 ? '会员卡' : item.purpose == 1 ? '教练课' : '其他'})${item.amount}元`
                  }}
                </view>
              </label>
            </checkbox-group>
          </view>
        </template>

        <!-- 折扣券 12345 -->
        <view v-if="card.card_type_id != 6 && !activityId" class="item coupon-item">
          <view class="label">
            <view class="prefix">券</view>
            <text>折扣券抵扣</text>
          </view>
          <view class="value">
            <text v-if="!deductionState.hasUse" class="no-coupon">暂无可用折扣券</text>
            <view v-else class="rig-text" @tap="handleToDiscountPage">
              {{ deductionState.couponReceiveId == 0 ? '不使用折扣券' : '¥ ' + deductionState.couponAmount }}
            </view>
          </view>
        </view>
        <view v-if="givePointObj.point" class="item">
          <view class="label">赠送积分</view>
          <view class="value">{{ givePointObj.point }}</view>
        </view>
        <view v-if="givePointObj.extra_point" class="item">
          <view class="label">活动额外赠积分</view>
          <view class="value">{{ givePointObj.extra_point }}</view>
        </view>
        <view class="item need-b-border">
          <text class="label">销售人员</text>
          <!-- <picker :range="saleList" :value="saleIdx" range-key="name" @change="handleSaleChange">
            <view class="value rig-sel"> {{ saleList[saleIdx].name }}</view>
          </picker> -->
          <SaleListDrawer
            :list="saleList"
            :selected-id="saleId"
            @change="handleSaleChangeByDrawer"
          />
        </view>
      </view>

      <PayTypeSelect
        class="pay-type-item"
        :pay-type-list="payTypeState.payTypeList"
        :pay-type-index="payTypeState.payTypeIdx"
        :card-list="payTypeState.valueCardList"
        :card-index="payTypeState.valueCardIdx"
        :value-card-text="useValueCardData.valueText"
        @change-type="handlePayTpeChange"
        @change-card="handleValueCardChange"
        @refresh-page="initPage"
      />

      <!-- 套餐内容 6 -->
      <view v-if="card.card_type_id == 6" class="package-item">
        <CardPackageTable :cards="card.card" link :show-tips="buyWayState.buyIdx === 1" />
      </view>
    </view>
  </view>

  <CardBottomWrap
    v-if="card.card_type_id"
    :card="card"
    :is-sale="deductionState.couponAmount != 0 || deductionState.depositValue != 0"
    :bill="bill"
    :total="total"
    :is-use-value-card="isUseValueCard"
    :use-value-card-data="useValueCardData"
    :loading="submitState.loading"
    :is-activity-card="!!activityId"
    is-order-page
    @checked="handleCheckboxChange"
    @submit="handleSubmitClick"
  />

  <!-- <PayResult :show="signContractModal" :sign-contract-info="signContractInfo" @closeModal="handleCloseModal" /> -->
  <LocationBusModal :show="showConfirmBus" @busConfirm="handleBusConfirm"/>
</template>

<script setup lang="ts" name="buyCardDetail">
import CardItem from './components/CardItem.vue'
import CardPackageTable from './components/CardPackageTable.vue'
import CardBottomWrap from './components/CardBottomWrap.vue'
import PayTypeSelect from '@/components/PayTypeSelect.vue'
// import PayResult from './components/PayResult.vue'
import LocationBusModal from './components/LocationBusModal.vue'
import { useLogin } from '@/hooks/useLogin'
import { useCard } from './hooks/useCard'
import { useUserStore } from '@/store/user'
import SaleListDrawer from './components/SaleListDrawer.vue'

const { checkLogin } = useLogin()
const {
  activityId,
  card, // 卡课数据
  validDays,
  activeTime,
  givePointObj, //积分
  setCardId,
  setPackageId,
  setActivityId,
  getCardData,
  showConfirmBus,
  handleBusConfirm,
  /* 定金折扣券 */
  deductionState,
  getDeductionData,
  handleDepositChange, // 处理定金选择
  handleToDiscountPage, // 跳转到折扣券选择页面 并监听折扣券选择
  /* 开卡方式 */
  buyWayState,
  handleBuyMethodsChange, // 处理变更开卡方式
  /* 销售人员 */
  saleList,
  saleIdx,
  saleId,
  handleSaleChange,
  /* 支付方式 */
  payTypeState,
  isUseValueCard,
  useValueCardData,
  getUserValueCardList,
  handlePayTpeChange,
  handleValueCardChange,
  /* 提交相关 */
  total, // 总金额
  bill, // 优惠后
  submitState,
  setIsLogin,
  handleCheckboxChange,
  handleSubmitClick,
  /* 签署合同信息 */
  signContractInfo,
} = useCard()

onLoad((options) => {
  setCardId(options.card_id)
  setPackageId(options.package_id)
  setActivityId(options.activity_id)
  initPage()
})

function initPage() {
  checkLogin(false).then(async () => {
    getCardData(true)
    !activityId.value && getDeductionData()
    getUserValueCardList()
    setIsLogin(true)
  })
}

// 用户-关闭弹窗(未选择其他操作)
function handleCloseModal() {
  uni.redirectTo({
    url: '/pages/my/card',
  })
}

const userStore = useUserStore()
const userBusName = computed(() => {
  return userStore.userInfo.bus_name
})

const saleName = ref('')
const handleSaleChangeByDrawer = (data) => {
  saleId.value = data.id
  saleName.value = data.name
}
</script>

<style lang="scss" scoped>
.card-detail-container {
  overflow-y: auto;
  box-sizing: border-box;
  padding-top: 20rpx;
  height: 100%;
}
.card-detail-top {
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
}
.card-detail-wrap {
  &.padding-b50 {
    padding-bottom: 50rpx;
  }
  .item {
    .label {
      margin-right: 26rpx;
      white-space: nowrap;
    }
    &.need-b-border {
      border-bottom: 1rpx solid #f6f6f8;
    }
  }
  .name-item,
  .desc-item {
    align-items: flex-start;
    box-sizing: border-box;
    padding: 28rpx 0;
    height: auto;
    min-height: 92rpx;
  }
  .desc-item {
    border-bottom: 1rpx solid #f6f6f6;
    .rich-text {
      padding-bottom: 0;
    }
    .info-des {
      line-height: 1.7;
      font-size: 24rpx;
    }
  }
  .deposit-item {
    box-sizing: border-box;
    padding: 28rpx 0;
    height: auto;
    min-height: 92rpx;
    .check-box-row {
      display: block;
      margin-bottom: 12rpx;
      padding: 5rpx 0;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .value {
      color: $theme-text-color-other;
    }
  }
  .coupon-item {
    .label {
      display: flex;
      align-items: center;
      .prefix {
        margin-right: 12rpx;
        border-radius: 6rpx;
        width: 32rpx;
        height: 32rpx;
        background-color: var(--THEME-COLOR);
        line-height: 32rpx;
        text-align: center;
        font-weight: bold;
        font-size: 21rpx;
        color: #000;
      }
    }
    .value {
      padding: 15rpx;
      padding-right: 0;
      min-width: 50%;
      text-align: right;
      // .no-coupon,
      // .rig-text {
      //   width: 100%;
      //   text-align: right;
      // }
    }
  }
  .pay-type-item :deep(.rig-sel)::after {
    right: 14rpx;
  }
  .package-item {
    margin: 0 20rpx;
  }
}

[data-theme='dark'] {
  .card-detail-container {
    background-color: #000 !important;
  }

  .form-items {
    background-color: #0f0f0f;

    .item {
      border-color: #000;
    }
  }
}
.item-tips {
  text-align: right;
  color: $theme-text-color-other;
  font-size: 20rpx;
  margin-top: -30rpx;
}
</style>
