<template>
  <view class="bus-page theme-bg">
    <view class="bus-wrap">
      <view
        v-for="item in busList"
        :key="item.bus_id"
        class="bus-item"
        :class="{ 'before-star': item.user_id }"
      >
        <view class="tit">
          <view class="con">{{ item.bus_name }}</view>
          <!-- <view class="tit-rig">{{ item.dist }}</view> -->
        </view>
        <view class="address">
          <uni-icons class="icon-mr" type="location-filled" size="12" color="#7d7d7d"></uni-icons>
          {{ item.address }}
        </view>
        <view class="phone">
          <uni-icons class="icon-mr" type="phone-filled" size="12" color="#7d7d7d"></uni-icons>
          {{ item.phone }}
        </view>
        <!-- <view class="limit">
          <uni-icons class="icon-mr" type="info-filled" size="12" color="#7d7d7d"></uni-icons>
          {{ item.useTimeStr }}
        </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="supportBusList">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const busList = ref([])

onLoad((option) => {
  getSupportBusList(option.cardId)
})

const getSupportBusList = (cardId) => {
  http
    .get('Card/getSupportBusList', {
      card_id: cardId,
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      if (res.errorcode == 0) {
        const { list } = res.data
        // list.forEach((item) => {
        //   item.useTimeStr = '使用时段:'
        //   if (item.weekCopy.length) {
        //     item.weekCopy.forEach((timeItem) => {
        //       item.useTimeStr += timeItem.weeksCopy.join('、')
        //       item.useTimeStr += ` ${timeItem.start_time}-${timeItem.end_time} `
        //     })
        //   } else {
        //     item.useTimeStr += '无限制'
        //   }
        // })
        busList.value = list
      }
    })
}
</script>

<style lang="scss">
.bus-item {
  overflow: hidden;
  position: relative;
  margin-bottom: 30rpx;
  padding: 28rpx;
  border-radius: 20rpx;
  background: rgba(var(--THEME-RGB), 0.2);
  font-size: 24rpx;
  .tit {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    font-weight: bold;
    font-size: 30rpx;
    justify-items: center;
    .con {
      overflow: hidden;
      max-width: 480rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      // color: #03080e;
    }
  }
  .address {
    margin-top: 4rpx;
    margin-bottom: 20rpx;
  }
  .address,
  .phone {
    color: $theme-text-color-grey;
  }
}
.bus-page {
  padding: 30rpx 20rpx;
}
</style>
