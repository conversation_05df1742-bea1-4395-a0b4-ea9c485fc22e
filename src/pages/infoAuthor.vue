<template>
  <view class="info-box">
    <view class="info-tips">需要您开启对应授权，才能进行后续操作！</view>
    <view class="info-main">
      <button class="btn-info" open-type="openSetting" @opensetting="navigateBack">去开启</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="infoAuthor"></script>

<style lang="scss" scoped>
.info-box {
  padding-top: 188rpx;
  // background: #161823 url('https://imagecdn.rocketbird.cn/minprogram/member/image/info-bg.png')
  background-size: cover;
  font-size: 28rpx;
  // color: #fff;
  position: relative;
  .info-main {
    width: 580rpx;
    margin: 0 auto;
  }
  .info-tips {
    widows: 700rpx;
    text-align: center;
  }
}

.btn-info {
  width: 522rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: var(--THEME-COLOR);
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 52rpx;
  margin: 78rpx auto 0;
  color: #fff;
}
</style>
