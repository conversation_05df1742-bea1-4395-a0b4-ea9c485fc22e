<template>
  <div class="box">
    <div class="succeed" v-if="succeed">
      <div class="icon">
        <img class="nike" src="../../static/img/success.png" />
      </div>
      <div class="title">恭喜您，签约成功！</div>
      <div class="btn btn-light" style="margin-top: 100rpx;" @click="handleHome">返回首页</div>
    </div>
    <div class="failed" v-else>
      <div class="icon">
        <img class="nike" src="../../static/img/warning.png" />
      </div>
      <div class="title">签约失败！</div>
      <div class="btn btn-dark" style="margin-top: 100rpx;" @click="handleReSign">重新签约</div>
      <div class="btn btn-light" style="margin-top: 60rpx;" @click="handleHome">返回首页</div>
    </div>
  </div>
</template>

<script setup lang="ts" name="payscore-succeed">
const succeed = ref(false)
const handleHome = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
const handleReSign = () => {
  uni.navigateTo({
    url: '/pages/payscore/detail?id=' + id.value,
  })
}

// life cycle
const id = ref('')
onLoad((options: any) => {
  succeed.value = options.flag == 1
  id.value = options.id
})
</script>

<style lang="scss" scoped>
.box {
  min-height: 100%;
  background-color: white;

  .succeed, .failed {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 100rpx;

    .icon {
      width: 172rpx;
      height: 172rpx;

      .nike {
        width: 172rpx;
        height: 172rpx;
      }
    }

    .title {
      font-size: 48rpx;
      font-weight: bold;
      color: #000000;
      margin-top: 44rpx;
    }

    .btn {
      width: 352rpx;
      height: 82rpx;
      border-radius: 40rpx;
      font-size: 30rpx;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .btn-dark {
      background: $theme-text-color-other;
      color: white;
    }

    .btn-light {
      background-color: white;
      border: 1px solid $theme-text-color-other;
      color: $theme-text-color-other;
    }
  }
}
</style>
