<template>
  <div class="box">
    <rich-text :nodes="nodes" v-if="nodes"></rich-text>
    <div class="empty" v-else>暂无协议</div>
  </div>
</template>

<script setup lang="ts" name="payscore-protocol">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

const nodes = ref('')
const getProtocol = () => {
  return http
    .post('/Contract/queryBusAgreementInfo', {
      bus_id: userStore.userInfoBusId,
      loading: false,
    })
    .then((res) => {
      nodes.value = res.data.content
    })
}

onShow(() => {
  getProtocol()
})
</script>

<style lang="scss" scoped>
.box {
  min-height: 100%;
  background-color: white;

  .empty {
    text-align: center;
    font-size: 30rpx;
    color: #999;
    padding-top: 200rpx;
  }
}
</style>
