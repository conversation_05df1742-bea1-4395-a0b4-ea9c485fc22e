<template>
  <div class="box">
    <div class="list" v-if="loading">
      <div class="item" v-for="i in 6" :key="i">
        <div class="album">
          <div class="photo" style="background-color: #eeeeee"></div>
        </div>
        <div class="info"></div>
        <div class="price">
          <div class="amount" style="color: #eeeeee">{{ i }}</div>
        </div>
      </div>
    </div>
    <z-paging
      ref="listRef"
      v-model="list"
      class="list"
      :show-loading-more-no-more-view="list.length > 10 ? true : false"
      empty-view-text="暂无数据"
      :fixed="false"
      :auto="false"
      @query="getList"
    >
      <div class="item" v-for="item in list" :key="item" @click="handleDetail(item.id)">
        <div class="album">
          <img class="photo" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/payscore-card-bg.png" />
          <!-- 卡种类型(1期限卡，2次数卡，3储值卡,4私教卡 5泳教卡) -->
          <div class="card-type" v-if="item.member_card_type == 1">期限卡</div>
          <div class="card-type" v-else-if="item.member_card_type == 2">次数卡</div>
          <div class="card-type" v-else-if="item.member_card_type == 3">储值卡</div>
          <div class="card-type" v-else-if="item.member_card_type == 4">私教卡</div>
          <div class="card-type" v-else-if="item.member_card_type == 5">泳教卡</div>
          <div class="card-type" v-else>健身卡</div>
        </div>
        <div class="info">
          <div class="title">{{ item.member_card_name }}</div>
          <div class="desc" v-if="item.dec_term != -1">期限 {{ item.dec_term }} 个月</div>
          <div class="desc" v-else>无期限</div>
        </div>
        <div class="price">
          <div class="amount">¥{{ item.dec_price }}</div>
        </div>
      </div>
    </z-paging>
  </div>
</template>

<script setup lang="ts" name="payscore-list">
import http from '@/utils/request'
import { goUrlPage } from '@/utils/urlMap'
import { useLogin } from '@/hooks/useLogin'
import { useUserStore } from '@/store/user'

// loading
const loading = ref(false)

const busId = ref('')
const pageSize = 999
const { checkLogin } = useLogin()
const userStore = useUserStore()

// list variables
const listRef = ref(null) as any
const list = ref([]) as any
// list request
const getList = (pageNo, pageSize) => {
  return http
    .post('/Contract/queryBusProgrammeList', {
      bus_id: busId.value,
      // shop_id,
      limit: pageSize,
      page: pageNo,
      loading: true,
    })
    .then((res) => {
      listRef.value.complete(res.data)
    })
}
// list event
const handleDetail = (id) => {
  goUrlPage(`/pages/payscore/detail?bus_id=${busId.value}&id=${id}`)
}

// life cycle
onLoad((options) => {
  if (options.bus_id) {
    busId.value = options.bus_id
  } else {
    busId.value = userStore.userInfoBusId
  }
})
onShow(async () => {
  loading.value = true
  await checkLogin()
  await getList(1, pageSize)
  loading.value = false
})
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  min-height: 100%;
  background-color: white;

  .list {
    position: fixed;
    top: 20rpx;
    left: 30rpx;
    right: 30rpx;
    bottom: 60rpx;

    .item {
      width: 634rpx;
      height: 98rpx;
      padding: 28rpx 20rpx;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      border-radius: 10rpx;
      margin-bottom: 20rpx;
      display: flex;
      flex-direction: row;

      .album {
        width: 161rpx;
        height: 94rpx;

        .photo {
          width: 161rpx;
          height: 94rpx;
        }

        .card-type {
          font-size: 20rpx;
          font-weight: 800;
          color: #795733;
          position: absolute;
          margin-top: -60rpx;
          margin-left: 13rpx;
        }
      }

      .info {
        margin-left: 20rpx;

        .title {
          font-size: 26rpx;
          font-weight: bold;
          color: #000000;
          width: 333rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 8rpx;
        }

        .desc {
          font-size: 24rpx;
          font-weight: 400;
          color: #7d7d7d;
          margin-top: 10rpx;
        }
      }

      .price {
        margin-left: auto;
        display: flex;
        justify-content: center;
        align-items: center;

        .amount {
          font-size: 36rpx;
          font-weight: bold;
          color: $theme-text-color-other;
        }
      }
    }
  }
}
</style>
