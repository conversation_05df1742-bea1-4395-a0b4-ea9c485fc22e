<template>
  <div class="box">
    <div class="year" v-for="(monthList, year) in yearSet" :key="year">
      <div class="title">{{ year }}年</div>
      <div class="month-list">
        <div
          class="month-item"
          v-for="(month, index) in monthList"
          :key="index"
          :style="index === 0 ? 'border-top:1px solid #F6F6F8' : ''"
        >
          <div class="label">{{ month }}</div>
          <div class="value">¥{{ price }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="payscore-subscribe">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

const id = ref('')
const yearSet = ref({})
const price = ref('')
const getSubscribe = () => {
  return http
    .post('/Contract/queryBusProgrammeInfoPlan', {
      bus_id: userStore.userInfoBusId,
      programme_id: id.value,
      loading: false,
    })
    .then((res) => {
      price.value = res.data.amount
      yearSet.value = res.data.mon_list
    })
}

// life cycle
onLoad((options) => {
  id.value = options.id as string
})

onShow(() => {
  getSubscribe()
})
</script>

<style lang="scss" scoped>
.box {
  min-height: 100%;
  background-color: white;
  padding: 40rpx;

  .year {
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #000000;
      line-height: 30rpx;
      margin: 30rpx 0;
    }

    .month-list {
      .month-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 90rpx;
        border-bottom: 1rpx solid #f6f6f8;

        .label {
          font-size: 26rpx;
          font-weight: 400;
          color: #000000;
          line-height: 30rpx;
        }

        .value {
          font-size: 26rpx;
          font-weight: bold;
          color: #000000;
          line-height: 30rpx;
        }
      }
    }
  }
}
</style>
