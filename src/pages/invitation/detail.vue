<template>
  <view class="invitation-activities-page">
    <view class="activities-banner-box">
      <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/top-bg.png" mode="widthFix" />
      <view class="activities-rule-tips-tag" @tap="showPopup">规则说明</view>
    </view>
    <view class="activities-body">
      <button v-if="!isLogin" class="share-image-btn" @tap="goLogin">
        <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/btn.png" mode="widthFix" />
      </button>
      <button v-else class="share-image-btn" open-type="share" :disabled="!activityData.id">
        <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/btn.png" mode="widthFix" />
      </button>
      <view class="step-wrap">
        <view class="title-row">
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/left.png" mode="heightFix" />
          <text>参与步骤</text>
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/rig.png" mode="heightFix" />
        </view>
        <view class="step-content-row">
          <view class="step-item">
            <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/1.png" mode="widthFix" />
            <text>邀请好友</text>
          </view>
          <view class="arrow-icon"></view>
          <view class="step-item">
            <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/2.png" mode="widthFix" />
            <text>好友注册</text>
            <text>并登录</text>
          </view>
          <view class="arrow-icon"></view>
          <view class="step-item">
            <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/3.png" mode="widthFix" />
            <text>好友完成</text>
            <text>指定任务</text>
          </view>
          <view class="arrow-icon"></view>
          <view class="step-item">
            <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/4.png" mode="widthFix" />
            <text>任务达成</text>
            <text>{{ activityData.invitees_award ? '双方' : '您可' }}获得奖励</text>
          </view>
        </view>
      </view>
      <view class="conditions-wrap">
        <view class="title-row">
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/left.png" mode="heightFix" />
          <text>任务和奖励</text>
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/rig.png" mode="heightFix" />
        </view>
        <view class="conditions-list">
          <template v-if="activityData.reach_condition_txt">
            <view v-for="(item, index) in activityData.reach_condition_list" :key="index" class="conditions-item">
              <text class="serial-num">{{ index + 1 }}</text>
              <text class="conditions-content">{{ item }}</text>
            </view>
          </template>
        </view>
        <view class="gift-content">
          <template v-if="activityData.id">
            <text class="complete-label">{{ activityData.reach_rule }}:</text>
            <text class="gift-text">
              <template v-if="activityData.inviter_award">
                您可获得 <text class="gift-name">【{{ activityData.inviter_award }}】</text>一张
              </template>
              <template v-if="activityData.invitees_award">
                ；好友可获得<text class="gift-name">【{{ activityData.invitees_award }}】</text>一张
              </template>
            </text>
          </template>
          <text v-else>本次活动已结束</text>
        </view>
      </view>
      <view class="record-wrap">
        <view class="title-row">
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/left.png" mode="heightFix" />
          <text>邀请记录</text>
          <image src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/rig.png" mode="heightFix" />
        </view>
        <view class="record-sum-row">
          <view class="sum-item-box">
            <view class="num-text">
              <text class="num">{{ statisticsData.all_count || 0 }}</text
              >人
            </view>
            <view class="num-label">邀请人数</view>
          </view>
          <view class="sum-item-box">
            <view class="num-text">
              <text class="num">{{ statisticsData.done_count || 0 }}</text>
              人
            </view>
            <view class="num-label">完成任务</view>
          </view>
        </view>
        <view class="record-list">
          <template v-if="statisticsData.list && statisticsData.list.length">
            <view v-for="(item, index) in statisticsData.list" :key="index" class="record-item">
              <image :src="item.avatar" />
              <view class="user-info">
                <text class="nickname">{{ item.nickname }}</text>
                <text class="complete-date">{{ item.create_time }}</text>
              </view>
              <text v-if="item.status == 2" class="desc">完成任务</text>
            </view>
          </template>
        </view>
      </view>
    </view>
    <view class="fixed-footer">
      <button v-if="!isLogin" @tap="goLogin">立即邀请</button>
      <button v-else open-type="share" :disabled="!activityData.id">立即邀请</button>
    </view>
    <uni-popup ref="msgPopup">
      <view class="rules-modal">
        <view class="rules-box">
          <text class="title">规则说明</text>
          <text class="rules-content">1.您邀请的好友，需是新用户，即不在门店已有的用户中</text>
          <text class="rules-content">2.您邀请的好友完成任务后，即可获得奖励。邀请多人都达成任务，可获得多份奖励</text>
          <text class="rules-content">3.您的好友同一手机号码仅可领取一次邀请奖励</text>
          <text class="rules-content">4.您可以在本页面点击“立即邀请”，或在团课预约页面点击“约朋友”，来参加此活动</text>
          <text class="rules-content">5.活动时间 {{ activityData.begin_time }} ~ {{ activityData.end_time }}</text>
          <text class="rules-content">6.如有疑问可到【{{ activityData.bus_name || '场馆' }}】咨询</text>
        </view>
        <view class="rules-btn-row">
          <button class="center-btn" @tap="closePopup">关闭</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts" name="resultDetail">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
const activityData = ref({}) as any
const statisticsData = ref({}) as any
const msgPopup = ref()
const isLogin = ref(false)
onLoad(() => {
  setTimeout(() => {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#FF7427',
    })
  }, 1000)
  getDoingActivity()
})
onShow(() => {
  if (userStore.userInfoUserId) {
    isLogin.value = true
  }
})
onShareAppMessage((options) => {
  // 新用户在注册页面注册时需要传activity_id和inviter_user_id
  const { id } = activityData.value
  const obj = {
    path: `/pages/index/index?bus_id=${userStore.userInfoBusId}`,
    title: '健身组队！快乐加倍！～',
    imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/share.jpg',
  }
  if (id) {
    obj.path = obj.path + `&activity_id=${id}&inviter_user_id=${userStore.userInfoUserId}&from=share`
  }
  return obj
})
async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}
function closePopup() {
  msgPopup.value.close()
}
function getDoingActivity() {
  http
    .get('ActivityGuests/getDoingActivity', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      const data = res.data
      if (!(data && data.id && data.reach_condition_txt)) {
        uni.showToast({
          title: '本次活动已结束',
          image: '/static/img/danger.png',
        })
      } else {
        data.reach_condition_list = Object.values(data.reach_condition_txt) // 需要索引
        activityData.value = data
        getStatistics(data.id)
      }
    })
}
function getStatistics(activity_id) {
  if (!userStore.userInfoUserId) {
    return
  }
  http
    .get('ActivityGuests/getUserStatistics', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      activity_id,
    })
    .then((res) => {
      statisticsData.value = res.data
    })
}
function goLogin() {
  uni.navigateTo({
    url: '/pages/login?hasChecked=true&navigateBack=true',
  })
}
</script>

<style lang="scss" scoped>
.invitation-activities-page {
  overflow-y: auto;
  position: relative;
  height: 100vh;
  background-color: #ff7427;
  .activities-banner-box {
    position: relative;
    padding-top: 14rpx;
    margin: 0 auto;
    width: 694rpx;
    image {
      width: 100%;
    }
    .activities-rule-tips-tag {
      position: absolute;
      right: 8rpx;
      bottom: 85rpx;
      width: 106rpx;
      height: 40rpx;
      text-align: center;
      line-height: 40rpx;
      font-size: 20rpx;
      font-weight: bold;
      color: #000000;
      border-radius: 20rpx;
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
  .activities-body {
    margin-top: 53rpx;
    margin: -75rpx auto 221rpx;
    padding-top: 53rpx;
    padding-bottom: 32rpx;
    width: 674rpx;
    z-index: 999;
    background-color: #ffc093;
    border: 2rpx solid #181818;
    border-radius: 10rpx;

    .title-row {
      display: flex;
      justify-content: center;
      align-items: center;
      image {
        height: 24rpx;
      }
      text {
        padding: 0 22rpx;
        font-size: 34rpx;
        font-weight: bold;
        color: #181818;
      }
    }
  }

  .fixed-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    height: 168rpx;
    background: #ff7427;
    box-shadow: 0rpx -3rpx 11rpx 0rpx rgba(0, 0, 0, 0.15);
    button {
      margin: 22rpx auto 0;
      width: 695rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 34rpx;
      font-weight: bold;
      background-color: #f9f9b1;
      border-radius: 2rpx;
    }
  }

  .share-image-btn {
    margin: 0 auto;
    padding: 0;
    width: 373rpx;
    line-height: unset;
    background: none !important;
    border: none;
    outline: none;
    &::after {
      border: none;
    }
    // &[disabled]:not([type]) {
    //   background-color: none;
    // }
    image {
      width: 100%;
    }
  }

  .step-wrap {
    padding-top: 26rpx;
    .step-content-row {
      display: flex;
      justify-content: space-between;
      padding: 28rpx 58rpx 0 52rpx;
      .step-item {
        text-align: center;
        flex: 1;
      }
      image {
        display: block;
        margin: 0 auto;
        margin-bottom: 10rpx;
        width: 94rpx;
        height: 94rpx;
      }
      text {
        display: block;
        font-size: 20rpx;
        line-height: 24rpx;
        color: #181818;
      }
      .arrow-icon {
        margin-top: 35rpx;
        width: 28rpx;
        height: 24rpx;
        background: url('https://imagecdn.rocketbird.cn/minprogram/member/image/invitation-step-arrow.png')
          center/contain no-repeat;
      }
    }
  }

  .conditions-wrap {
    margin: 29rpx auto 0;
    padding: 33rpx;
    padding-bottom: 40rpx;
    width: 613rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-sizing: border-box;
    .conditions-list {
      padding-top: 42rpx;
    }
    .conditions-item {
      display: flex;
      align-items: center;
      margin-bottom: 27rpx;
      color: #181818;
      &:last-child {
        margin-bottom: 40rpx;
      }
      .serial-num {
        margin-right: 13rpx;
        width: 28rpx;
        height: 28rpx;
        text-align: center;
        line-height: 28rpx;
        font-size: 24rpx;
        border-radius: 50%;
        background-color: #ffcfae;
      }
      .conditions-content {
        font-size: 26rpx;
      }
    }
    .gift-content {
      padding-top: 34rpx;
      border-top: 2rpx dashed #7481f7;
      font-size: 30rpx;
      line-height: 48rpx;
      .complete-label {
        display: block;
        color: #181818;
      }
      .gift-text {
        margin-top: 23rpx;
        font-weight: bold;
      }
      .gift-name {
        color: #f86b24;
      }
    }
  }

  .record-wrap {
    padding-top: 36rpx;
    padding-bottom: 27rpx;
    .record-sum-row {
      display: flex;
      justify-content: space-around;
      margin: 35rpx auto 0;
      padding-top: 31rpx;
      padding-bottom: 27rpx;
      width: 613rpx;
      background-color: #fff;
      border-radius: 10rpx;
      .sum-item-box {
        text-align: center;
        &:nth-child(2) .num {
          font-weight: bold;
          color: #f86b24;
        }
      }
      .num-text {
        margin-bottom: 5rpx;
      }
      .num {
        font-size: 68rpx;
      }
      .num-label {
        font-size: 26rpx;
      }
    }
    .record-list {
      margin: 35rpx auto 0;
      width: 613rpx;
      .record-item {
        display: flex;
        align-items: center;
        padding-left: 7rpx;
        padding-right: 2rpx;
        height: 92rpx;
        color: #181818;
      }
      image {
        margin-right: 19rpx;
        width: 92rpx;
        height: 92rpx;
      }
      .nickname {
        font-weight: bold;
      }
      .nickname,
      .desc {
        display: block;
        line-height: 40rpx;
        font-size: 30rpx;
      }
      .complete-date {
        font-size: 26rpx;
      }
      .desc {
        margin-left: auto;
        color: #2a7a6e;
      }
    }
  }

  .rules-modal {
    background: #fff;
    border-radius: 10rpx;
    padding: 35rpx;
    .rules-box {
      padding-bottom: 35rpx;
      width: 540rpx;
      display: flex;
      flex-direction: column;
      color: #1b1b1b;
      .title {
        margin-bottom: 32rpx;
        font-size: 36rpx;
        font-weight: bold;
      }
      .rules-content {
        margin-bottom: 8rpx;
        font-size: 30rpx;
        line-height: 46rpx;
      }
    }

    .rules-btn-row {
      position: relative;
      width: 100%;

      .center-btn {
        margin: 0 auto 49rpx;
        width: 310rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        font-weight: bold;
        font-size: 26rpx;
        color: #ffffff;
        background: #6cc6bf;
        border-radius: 35rpx;
      }
    }
  }
}
</style>
