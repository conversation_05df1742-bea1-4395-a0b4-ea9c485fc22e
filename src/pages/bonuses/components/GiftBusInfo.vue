<template>
  <view v-if="gymInfo" class="gym">
    <view class="info">
      <view class="avatar">
        <image :src="gymInfo.thumb || gymInfo.bus_thumb" />
      </view>
      <view class="desc">
        <view class="name">{{ gymInfo.name || gymInfo.busName }}</view>
        <view class="addition" @tap="findMe">
          <uni-icons class="icon-mr" type="location-filled" size="12" color="#7d7d7d"></uni-icons>
          <text>{{ gymInfo.address || gymInfo.busAddress }}</text>
        </view>
        <view class="addition" @tap="callMe">
          <uni-icons class="icon-mr" type="phone-filled" size="12" color="#7d7d7d"></uni-icons>
          <text>{{ gymInfo.phone || gymInfo.busPhone }}</text>
        </view>
      </view>
    </view>
    <view class="scene">
      <scroll-view class="gym-scene" scroll-x>
        <image
          v-for="(item, index) in gymInfo.images"
          :key="index"
          class="gym-bg"
          mode="aspectFill"
          :src="item"
        />
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts" name="GiftBusInfo">
const props = defineProps({
  gymInfo: {
    type: Object,
    required: true,
  },
})

function callMe() {
  uni.makePhoneCall({
    phoneNumber: props.gymInfo.phone || props.gymInfo.busPhone,
  })
}

function findMe() {
  uni.openLocation({
    latitude: parseFloat(props.gymInfo.lat),
    longitude: parseFloat(props.gymInfo.lng),
    name: props.gymInfo.name || props.gymInfo.busName,
    address: props.gymInfo.address || props.gymInfo.busAddress,
  })
}
</script>

<style lang="scss" scoped>
.gym {
  width: 690rpx;
  border: 1px solid #e8e8e8;
  border-radius: 10rpx;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 30rpx 20rpx;
  .info {
    display: flex;
    flex-direction: row;

    .avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 110rpx;
      width: 110rpx;
      margin-right: 26rpx;
      image {
        height: 110rpx;
        width: 110rpx;
        border-radius: 20rpx;
      }
    }

    .desc {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .name {
        font-size: 30rpx;
        font-weight: bold;
        color: #313131;
      }

      .addition {
        display: flex;
        align-items: center;
        margin-top: 10rpx;

        image {
          height: 30rpx;
          width: 30rpx;
        }

        text {
          font-size: 24rpx;
          color: #898989;
          margin-left: 13rpx;
        }
      }
    }
  }

  .scene {
    margin-top: 13rpx;
    .gym-scene {
      height: 180rpx;
      width: 100%;
      white-space: nowrap;

      .gym-bg {
        height: 180rpx;
        width: 260rpx;
        border-radius: 10rpx;
        margin-right: 20rpx;
      }
    }
  }
}
</style>
