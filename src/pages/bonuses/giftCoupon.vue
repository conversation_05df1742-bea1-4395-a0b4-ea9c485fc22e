<template>
  <NavBar />
  <view class="experience-card-page footer-hasfixed theme-bg">
    <view class="experience-card-container">
      <view class="top-info">
        <view class="be-from">您的好友【{{ buddy }}】</view>
        <view class="title">赠送给您一张折扣券</view>
      </view>
      <view class="card-body"> <CouponItem v-if="coupon" :coupon="coupon" is-style-other /> </view>
    </view>
    <GiftBusInfo :gym-info="gym" />
  </view>

  <view v-if="coupon?.status == 1" class="fixed-bottom-wrap theme-bg">
    <view class="buttons">
      <button class="normal-btn" @tap="getCoupon">立即领取</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="pageExperienceCard">
import NavBar from '@/components/NavBar'
import CouponItem from '@/components/couponItem.vue'
import GiftBusInfo from './components/GiftBusInfo.vue'
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { useUserStore } from '@/store/user'

const { checkLogin } = useLogin()
const curOptions = reactive({
  couponId: '',
  busId: '',
  giftOpenId: '',
  fromOpenid: '',
})
const coupon = ref()
const gym = ref()
const buddy = ref()
const userStore = useUserStore()
onLoad((options) => {
  curOptions.couponId = options.couponId || ''
  curOptions.busId = options.busId || ''
  curOptions.giftOpenId = options.openId || ''
  curOptions.fromOpenid = options.fromOpenid || ''
  getInfo()
})

function getInfo() {
  http
    .get('Coupon/receiveCouponInfo', {
      coupon_receive_id: curOptions.couponId,
      openid: curOptions.giftOpenId,
      from_openid: curOptions.fromOpenid,
      bus_id: curOptions.busId,
    })
    .then((res) => {
      coupon.value = res.data.coupon
      gym.value = res.data.business
      buddy.value = res.data.friend_user_name
    })
}
function getCoupon() {
  checkLogin(true, curOptions.busId).then((res) => {
    http
      .post('Coupon/receiveCoupon', {
        coupon_receive_id: curOptions.couponId,
        openid: userStore.userInfo.openid,
        bus_id: res.bus_id,
        user_id: res.user_id,
      })
      .then((res) => {
        uni.showToast({
          title: '领取成功！',
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/my/coupon',
          })
        }, 1000)
      })
  })
}
</script>

<style lang="scss" scoped>
.experience-card-page {
  // height: 100%;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/experience-card-page-top-bg.png') top
    left/contain no-repeat;

  .experience-card-container {
    overflow-y: auto;
    box-sizing: border-box;
    padding: 180rpx 30rpx 0;
    // height: 100%;
  }

  .top-info {
    margin-bottom: 350rpx;
    text-align: center;
    font-size: 30rpx;
    .title {
      margin-top: 12rpx;
      font-weight: bold;
    }
  }
}
</style>
