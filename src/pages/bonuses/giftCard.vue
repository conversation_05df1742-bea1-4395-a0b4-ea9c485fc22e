<template>
  <PageExperienceCard :card-info="cardInfo" position="您的好友" @get-card="handleSubmitClick" />
</template>

<script setup lang="ts" name="expCardConfirm">
import PageExperienceCard from '@/pages/card/components/PageExperienceCard'
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'

const { checkLogin } = useLogin()
const cardInfo = ref({})
const data = reactive({
  awardId: '', // 红包开出体验卡的获奖ID
  newAwardId: '', // 红包定向体验卡的新获奖ID
  shareBusId: '',
  shareUserId: '',
  ucid: '', // 魅力值兑换的体验卡
  el_id: '0',
})

onLoad((options) => {
  data.awardId = options.awardId || ''
  data.newAwardId = options.newAwardId || ''
  data.ucid = options.ucid || ''
  data.shareBusId = options.bus_id || ''
  data.shareUserId = options.user_id || ''
  data.el_id = options.elid || '0'
  getInfo()
})

function getInfo() {
  http
    .get('Pointmall/goodsReceivePreview', {
      bus_id: data.shareBusId,
      share_user_id: data.shareUserId,
      uc_id: data.ucid,
    })
    .then((res) => {
      if (res.errorcode === 0) {
        cardInfo.value = res.data?.info
      }
    })
}

const handleSubmitClick = () => {
  uni.setStorageSync('introducer_id', data.shareUserId)

  checkLogin(true, data.shareBusId).then((userInfo) => {
    http
      .post('Pointmall/goodsReceive', {
        bus_id: userInfo.bus_id,
        user_id: userInfo.user_id,
        share_user_id: data.shareUserId,
        uc_id: data.ucid,
        el_id: data.el_id,
      })
      .then((res) => {
        uni.showToast({
          title: '领取成功！',
        })
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/my/index',
          })
        }, 1000)
      })
  })
}
</script>
