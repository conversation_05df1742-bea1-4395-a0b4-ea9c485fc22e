<template>
  <NavBar />
  <view class="page-wrap theme-bg footer-hasfixed">
    <view class="bgimg-gradient" :style="gradientStyle"></view>
    <view class="bus-wrap">
      <view class="coach-info">
        <image class="logo" mode="scaleToFill" :src="coachInfo.avatar" />
        <view class="rig">
          <view class="tag"> 擅长：{{ coachInfo.specialty }} </view>
          <view class="tit">{{ coachInfo.coach_name }}</view>
          <view class="pos">{{ coachInfo.position }}</view>
        </view>
      </view>
      <view class="coach-des rich-text">
        <rich-text :nodes="coachInfo.brief"></rich-text>
      </view>
      <view v-if="coachInfo.class_list && coachInfo.class_list.length" class="class-wrap">
        <view class="box-tit">在售私教课</view>
        <view>
          <view v-for="item in coachInfo.class_list" :key="item.card_id">
            <CardItem :item="item" link :coach-id="coach_id" />
          </view>
        </view>
      </view>
    </view>
  </view>
  <view v-if="coachInfo.able_mark" class="fixed-bottom-wrap theme-bg">
    <button class="normal-btn" @tap="goReserve">立即预约</button>
  </view>
</template>

<script setup lang="ts" name="class">
import NavBar from '@/components/NavBar.vue'
import http from '@/utils/request'
import CardItem from '@/pages/card/components/CardItem.vue'
import { unescapeHTML } from '@/utils/shared'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin } = useLogin()

const coachInfo = ref<Record<string, any>>({})
const userStore = useUserStore()
const coach_id = ref('')
const gradientStyle = ref('')
onLoad((options) => {
  coach_id.value = options.coach_id || ''
  getInfo()
})
onShareAppMessage((options) => {
  return {
    path: `/pages/coach/detail?bus_id=${userStore.userInfoBusId}&coach_id=${coach_id.value}&from=share`,
  }
})
const getInfo = () => {
  http
    .get('/Coach/getDetail', {
      bus_id: userStore.userInfoBusId,
      coach_id: coach_id.value,
      loading: true,
    })
    .then((res) => {
      coachInfo.value = {
        ...res.data,
        brief: unescapeHTML(res.data.brief),
        specialty: res.data.specialty.join('、'),
      }
      gradientStyle.value = `background-image: linear-gradient(to top, #FFFFFF, transparent), url('${
        res.data.promotional_photo ||
        'https://imagecdn.rocketbird.cn//minprogram/uni-member/coach-detail-bg.jpeg'
      }');`
    })
}
function goReserve() {
  checkLogin().then(() => {
    uni.navigateTo({
      url: `/pages/class/ptReserve?coach_id=${coach_id.value}`,
    })
  })
}
</script>

<style lang="scss">
.page-wrap {
  min-height: 100%;
  box-sizing: border-box;
}
.bgimg-gradient {
  width: 750rpx;
  height: 730rpx;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}
.coach-info {
  display: flex;
  align-items: center;
  margin-top: -44rpx;
}
.coach-info .rig {
  font-size: 22rpx;
  line-height: 1.7;
  flex: 1;
}
.coach-info .rig .tit {
  font-size: 36rpx;
  font-weight: bold;
}
.coach-info .rig .pos {
  font-size: 24rpx;
  color: #7d7d7d;
}
.coach-info .logo {
  width: 140rpx;
  height: 140rpx;
  background: #ffffff;
  border-radius: 50%;
  margin-right: 23rpx;
  margin-left: 30rpx;
}
.coach-info .tag {
  max-width: 500rpx;
  height: 43rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 43rpx;
  background: rgba(161, 234, 43, 0.6);
  border-radius: 10rpx;
  padding: 0 20rpx;
}
.coach-des {
  padding: 30rpx;
  font-size: 24rpx;
  line-height: 36rpx;
}
.class-wrap {
  padding: 0 30rpx;
}
</style>
