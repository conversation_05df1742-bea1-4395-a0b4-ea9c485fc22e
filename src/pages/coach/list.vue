<template>
  <view class="theme-bg coach-list">
    <z-paging
      ref="paging"
      v-model="coachList"
      :show-loading-more-no-more-view="coachList.length > 10 ? true : false"
      :fixed="false"
      :refresher-enabled="false"
      :empty-view-img-style="{ width: '103rpx', height: '144rpx' }"
      empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png"
      empty-view-text="暂无相关私教数据"
    >
      <view v-for="item in coachList" :key="item.coach_id" class="coach-item" @tap="goDetail(item)">
        <view class="left">
          <image class="coach-img" mode="aspectFill" :src="item.avatar" />
          <view class="left-con">
            <view class="tit">
              <view class="name">{{ item.coach_name }}</view>
              <view v-if="item.position" class="postion">{{ item.position }}</view>
            </view>
            <view class="des">{{ item.specialty }}</view>
            <view class="coach-classes">
              <view v-for="name in item.permitted_class.slice(0, 5)" :key="name" class="class">{{ name }}</view>
            </view>
          </view>
        </view>
        <view v-if="item.min_price !== ''" class="rig">
          <view v-if="item.min_price" class="price">¥{{ item.min_price }}/节 起</view>
          <view v-else class="price">价格面议</view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts" name="class">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()
const coachList = ref([])
const userStore = useUserStore()
const paging = ref()
themeStore.getConfig({ type: 7 }).then(() => {
  uni.setNavigationBarTitle({
    title: themeStore.theme7.appellation || '教练',
  })
})
const getInfo = () => {
  http
    .get('/Coach/getList', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      paging.value.reload()
      paging.value.setLocalPaging(res.data.list)
    })
    .catch((res) => {
      paging.value.setLocalPaging([], false)
    })
}
onShow(() => {
  getInfo()
})
function goDetail(info) {
  uni.navigateTo({
    url: `/pages/coach/detail?coach_id=${info.coach_id}`,
  })
}
onShareAppMessage((options) => {
  return {
    path: `/pages/coach/list?bus_id=${userStore.userInfoBusId}&from=share`,
  }
})
</script>
<style lang="scss">
.coach-list {
  padding: 0 0 40rpx;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.coach-item {
  font-size: 24rpx;
  margin: 0 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f6f6f8;
  .coach-classes {
    display: flex;
    flex-wrap: wrap;
    max-width: 346rpx;
    max-height: 80rpx;
    overflow: hidden;
    font-size: 20rpx;
    margin-top: 20rpx;
  }
  .class {
    display: inline-block;
    line-height: 32rpx;
    padding: 0 15rpx;
    height: 32rpx;
    box-sizing: border-box;
    background: rgba(var(--THEME-RGB), 0.2);
    border-radius: 6rpx;
    margin-right: 7rpx;
    margin-bottom: 12rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .left {
    display: flex;
    justify-items: flex-start;
    align-items: flex-start;
  }
  .tit {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }
  .name {
    font-size: 30rpx;
    font-weight: bold;
    max-width: 300rpx;
    margin-right: 6rpx;
  }
  .postion {
    max-width: 100rpx;
    padding: 0 15rpx;
    height: 29rpx;
    text-align: center;
    line-height: 29rpx;
    font-size: 20rpx;
    color: $theme-text-color-grey;
    border: 1px solid var(--THEME-COLOR);
    border-radius: 14rpx;
  }
  .des {
    font-size: 22rx;
    font-weight: 400;
    color: $theme-text-color-grey;
    width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .des,
  .postion,
  .name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .price {
    white-space: nowrap;
    font-size: 30rpx;
    font-weight: bold;
    color: $theme-text-color-other;
  }
}
.coach-img {
  width: 110rpx;
  min-width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  margin-right: 28rpx;
}
</style>
