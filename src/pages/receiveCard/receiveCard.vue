<template>
  <view class="card-detail-container footer-hasfixed theme-bg">
    <!-- 会员卡封面 -->
    <!-- <image
      class="card-thumb-img"
      :src="'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png'"
      mode="aspectFill"
    /> -->
    <!-- 会员卡信息 -->
    <view class="card-detail-wrap">
      <!-- <view class="card-name">
        {{ receiveTicketInfo.name }}
      </view>
      <view class="card-type-row">
        <text>{{ receiveTicketInfo.base_duration }} {{ receiveTicketInfo.duration_unit == 1 ? '小时' : '分钟' }}</text>
        <text> | {{ receiveTicketInfo.bus_name }}</text>
        <text v-if="receiveTicketInfo.ticket_type == 1"> | {{ receiveTicketInfo.validity_time }} 前可核销</text>
        <text v-else> | 场次结束前可核销</text>
      </view> -->
      <view class="item-left">
        <view class="item-title">{{ receiveTicketInfo.name || '' }}</view>
        <view class="item-subtitle">
          <text class="name">{{
            receiveTicketInfo && receiveTicketInfo.space_name && receiveTicketInfo.space_name.length
              ? receiveTicketInfo.space_name
              : '入场'
          }}</text>
          <text v-if="receiveTicketInfo.ticket_type == 1" style="margin-left: 20rpx"
            >{{ receiveTicketInfo.base_duration }} {{ receiveTicketInfo.duration_unit == 1 ? '小时' : '分钟' }}</text
          >
          <text v-else style="margin-left: 20rpx">{{ receiveTicketInfo.base_duration || '' }}</text>
        </view>
        <view v-if="receiveTicketInfo.bus_name" class="item-subtitle">
          门店：
          {{ receiveTicketInfo.bus_name }}
        </view>
        <view v-if="receiveTicketInfo.ticket_type == 1" class="item-subtitle"
          >{{ receiveTicketInfo.validity_time }} 前可核销</view
        >
        <view v-else class="item-subtitle">场次结束前可核销</view>
      </view>
      <!-- 领取规则 -->
      <view class="card-info-box">
        <view class="box-tit">领取规则</view>
        <view class="info-table">
          <template>
            <view class="item">
              <view class="label">1. 每个链接只能领取1次</view>
              <view class="value"></view>
            </view>
            <view class="item">
              <view class="label">2. 领取后卡仅限自己使用</view>
              <view class="value"></view>
            </view>
            <view class="item">
              <view class="label">3. 卡不允许退款、转让</view>
              <view class="value"></view>
            </view>
            <view class="item">
              <view class="label">4. 请注意票到期时间，过期无法再使用</view>
              <view class="value"></view>
            </view>
          </template>
        </view>
      </view>
    </view>

    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <!-- 优先级 已被领取 > 票已失效 > 领取 -->
        <button v-if="receiveTicketInfo.received" class="normal-btn" disabled>已被领取</button>
        <button v-else-if="receiveTicketInfo.expired" class="normal-btn" disabled>票已失效</button>
        <button
          v-else-if="!receiveTicketInfo.received && !receiveTicketInfo.expired"
          class="normal-btn outer-org"
          @tap="handleReceive"
          :disabled="disabled"
        >
          领取
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="receiveCard">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { queryParser } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin.ts'

const { checkLogin, getParam } = useLogin()

// 变量-分享票信息
const receiveTicketInfo = ref({
  name: '',
  space_name: '',
  ticket_type: '',
  base_duration: '',
  duration_unit: '',
  bus_name: '',
  validity_time: '',
  received: '',
  expired: '',
})
// 变量-领取会员卡的人信息  userStore.userInfo
const userStore = useUserStore()
// 变量-bus_id
const shareBusID = ref('')
// 变量-share_id
const shareId = ref('')
// 变量-share_id
const sanLogId = ref('')
// 是否禁用按钮
const disabled = ref(false)

onLoad((options) => {
  shareBusID.value = options.bus_id || ''
  shareId.value = options.share_id || ''
  sanLogId.value = options.san_log_id || ''
  getDetail()
})

const getDetail = () => {
  const url = '/Santicket/getShareDetail'
  const params = {
    san_log_id: sanLogId.value,
    share_id: shareId.value,
  }
  http
    .get(url, params)
    .then((res) => {
      receiveTicketInfo.value = res.data
    })
    .catch((err) => {
      console.log(err)
    })
}

const handleReceive = async () => {
  const curParams = await getParam()
  // 检查登录
  checkLogin(true, curParams.bus_id || shareBusID.value).then((res) => {
    const url = '/Santicket/sanReceive'
    const params = {
      san_log_id: sanLogId.value,
      share_id: shareId.value,
      user_id: userStore.userInfo.user_id,
      phone: userStore.userInfo.phone,
    }
    http
      .post(url, params)
      .then((res) => {
        disabled.value = true
        uni.showToast({
          title: res.errormsg,
          icon: 'success',
          duration: 1000
        })
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/my/ticket`,
          })
          getDetail()
        }, 1000);
      })
      .catch((err) => {
        console.log(err)
        uni.showToast({
          title: err,
          icon: 'success',
          duration: 1000
        })
        setTimeout(() => {
          getDetail()
        }, 1000);
      })
  })
}
</script>

<style lang="scss" scoped>
.card-detail-container {
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20rpx 30rpx 178rpx;
  height: 100%;
  .box-tit {
    font-size: 30rpx;
  }
}
.card-thumb-img {
  display: block;
  margin: 0 auto;
  border-radius: 20rpx;
  width: 690rpx;
  height: 400rpx;
}
.card-name {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
  font-weight: bold;
  font-size: 36rpx;
}
.poins-tips {
  display: flex;
  font-size: 20rpx;
  margin-left: 10rpx;
  color: $theme-text-color-other;
}
.card-type-row {
  margin-top: 28rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: $theme-text-color-grey;
}
.card-info-box {
  margin-top: 38rpx;
  border-top: 1rpx solid #e8e8e8;
  // border-bottom: 1rpx solid #e8e8e8;
  .info-table {
    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 28rpx;
      &:last-child {
        margin-bottom: 32rpx;
      }
    }
  }
}
.card-desc-box {
  .info-des {
    line-height: 1.7;
    font-size: 24rpx;
  }
}

[data-theme='dark'] {
  .card-detail-container {
    background-color: #000 !important;
    .card-info-box {
      border: 0;
    }
  }
}

.item-left {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  width: 100%;

  .item-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #313131;
  }

  .item-subtitle {
    font-size: 24rpx;
    color: #313131;
    margin-top: 24rpx;

    .name {
      display: inline-block;
      vertical-align: middle;
      max-width: 400rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      background: rgba(255, 116, 39, 0.1);
      font-size: 24rpx;
      line-height: 44rpx;
      color: #313131;
      padding: 2rpx 4rpx;
    }
  }
}
</style>
