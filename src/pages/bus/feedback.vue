<template>
  <view class="talk-box">
    <view class="form-items theme-bg mt20">
      <!-- <view class="item">
        <view class="label">请假场馆</view>
        <view class="value">
          <MerchantBusPick
            v-model="busUserInfo.bus_id"
            :has-all-bus-option="false"
            :is-filter-user-id-bus="true"
            selecte-icon-color="origin"
            :is-show-icon="false"
            @change="handleChangeBus"
          />
        </view>
      </view> -->
      <view class="merchant-bus-wrap">
        <MerchantBusPick v-model="busUserInfo.bus_id" :hasAllBusOption="!isShowTabBar" @change="handleChangeBus" />
      </view>
    </view>
    <view class="talk theme-bg">
      <textarea v-model="content" maxlength="500" auto-height placeholder="有什么疑问或者意见建议请给我留言吧" />
    </view>
    <view class="normal-wrap">
      <button class="normal-btn" :disabled="busUserInfo.bus_id === ''" @tap="handleSubmit">提交留言</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="class">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useThemeStore } from '@/store/theme'
const themeStore = useThemeStore()
const { checkLogin } = useLogin()
const content = ref('')
const busUserInfo = reactive({
  bus_id: '',
  user_id: '',
})

//运营模式为综合体育场馆版本时，只有商家下展示tabBar
const isShowTabBar = computed(() => {
  return (themeStore.operationMode === 1 && themeStore.isShowMerchantMode) || themeStore.operationMode !== 1
})

function handleChangeBus({ bus_id, user_id }) {
  busUserInfo.bus_id = bus_id
  busUserInfo.user_id = user_id
}
function handleSubmit() {
  checkLogin().then(() => {
    http
      .post('/Business/comment', {
        bus_id: busUserInfo.bus_id,
        user_id: busUserInfo.user_id,
        content: content.value,
      })
      .then((res) => {
        uni.showToast({ title: res.errormsg })
        setTimeout(() => {
          uni.navigateBack({
            delta: 1, // 回退前 delta(默认为1) 页面
          })
        }, 1000)
      })
  })
}
</script>

<style lang="scss" scoped>
.form-items {
  margin: 0;
  border-bottom: 1rpx solid #f5f7f9;
}
.talk-box {
  margin: 20rpx;
  .normal-wrap {
    margin-top: 30rpx;
  }
}
.talk {
  min-height: 320rpx;
  border-radius: 6rpx;

  textarea {
    box-sizing: border-box;
    width: 100%;
    font-size: 26rpx;
    color: #898989;
    padding: 26rpx 34rpx;
    min-height: 280rpx;
  }
}

.merchant-bus-wrap {
  padding: 0;
}
</style>
