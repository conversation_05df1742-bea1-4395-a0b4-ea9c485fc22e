<template>
  <view>
    <uni-popup ref="msgPopup" :is-mask-click="false" @change="handleStatusChange">
      <view class="distance-container">
        <view class="distance-box">
          <view class="title">
            <image class="icon" mode="aspectFit" src="/static/img/address.png" />
            当前场馆
          </view>
          <view class="desc-wrap" v-if="busInfo">
            <view class="desc">{{busInfo.name}}</view>
            <view class="distance-desc">{{busInfo.address}}</view>
          </view>
          
        </view>
        <view class="distance-btn buttons custom">
          <SwitchBusPick :busList="busList" @change="handleChange">
            <button class="normal-btn normal-btn-min outer-gray">
              更换场馆
            </button>
          </SwitchBusPick>
          <button class="normal-btn normal-btn-min" style="background: #07c160;color: #ffffff;" @tap="handleStoreConfirm">
            确认场馆
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { goUrlPage } from '@/utils/urlMap'
import { useUserStore } from '@/store/user'
import SwitchBusPick from './SwitchBusPick.vue'

const emits = defineEmits(['closeModal','busConfirm', 'change'])

const props = defineProps({
  // 是否展示弹窗
  show: {
    type: Boolean,
  },
  busInfo: {
    type: Object,
  },
  busList: {
    type: Array,
  },
})

const userStore = useUserStore()
const userBusName = computed(() => {
  return userStore.userInfo.bus_name
})
const msgPopup = ref()
watch(
  () => props.show,
  (val) => {
    if (val) {
      showPopup()
    }
    if (!val) {
      closePopup()
    }
  }
)

async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}

async function closePopup() {
  if (msgPopup.value) {
    msgPopup.value.close()
  } else {
    await nextTick()
    msgPopup.value.close()
  }
}

// 弹窗关闭切换事件
function handleStatusChange(info) {
  if (!info.show) {
    emits('closeModal')
  }
}
// 确定门店
function handleStoreConfirm(info) {
  closePopup()
  emits('busConfirm')
}

function handleChange(index) {
  closePopup()
  emits('change', index)
}

</script>

<style lang="scss" scoped>
.distance-container {
  padding: 48rpx 30rpx;
  background: #fff;
  border-radius: 20rpx;
  width: 680rpx;
  box-sizing: border-box;
  .distance-box {
    width: 100%;
    margin-bottom: 48rpx;
    color: #000;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      line-height: 50rpx;
      .icon {
        width: 30rpx;
        height: 30rpx;
      }
    }
    .desc-wrap {
      box-sizing: border-box;
      width: 100%;
      padding: 28rpx;
      margin-top: 48rpx;
      background: #F6F6F8;
    }
    .desc {
      font-size: 36rpx;
      font-weight: bold;
    }

    .distance-desc {
      font-size: 26rpx;
      line-height: 30rpx;
      margin-top: 40rpx;
      color: #333;
    }
  }
  .distance-btn {
    position: relative;
    width: 100%;
    display: flex;
    .outer-gray {
      margin-right: 20rpx;
    }
  }
}
</style>
