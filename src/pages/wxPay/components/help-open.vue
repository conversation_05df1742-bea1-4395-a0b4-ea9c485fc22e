<template>
  <view>
    <uni-popup type="bottom" ref="msgPopup" @change="handleStatusChange">
      <view class="help-wrap">
        <view class="msg-tit">信息录入</view>
        <view class="form-items" v-if="unBindList.length">
          <view class="item">
            <view class="label">协助亲友</view>
            <view class="value">
              <picker :value="postData.sex" :range="unBindList" range-key="phone" @change="handleChange">
                <view class="value rig-sel" v-if="unBindList[userIndex]">
                  {{ unBindList[userIndex].phone || '请选择' }}
                </view>
              </picker>
            </view>
          </view>
          <view class="item">
            <view class="label">亲友姓名</view>
            <input v-model="postData.bind_palm_username" class="item-input" placeholder="请填写" />
          </view>
          <view class="item">
            <view class="label">有效期</view>
            <view class="value">
              {{ postData.expire_time }}
            </view>
          </view>
        </view>
        <view class="nodata" v-else>未发现可以绑定手掌的亲友，请联系场馆工作人员。</view>
        <button class="normal-btn friend" @tap="addBindInfo">
          确定
        </button>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { goUrlPage } from '@/utils/urlMap'
import { useUserStore } from '@/store/user'
import { addDays } from '@/utils/shared'

const emits = defineEmits(['closeModal'])

const props = defineProps({
  // 是否展示弹窗
  show: {
    type: Boolean,
  },
})
const userIndex = ref(0)
const postData = reactive({
  bind_palm_user_id: '',
  bind_palm_username: '',
  expire_time: addDays(new Date(), 364),
})

const userStore = useUserStore()

const msgPopup = ref()

watch(
  () => props.show,
  (val) => {
    if (val) {
      showPopup()
      getUnbindList()
    }
    if (!val) {
      closePopup()
    }
  }
)

async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}

async function closePopup() {
  if (msgPopup.value) {
    msgPopup.value.close()
  } else {
    await nextTick()
    msgPopup.value.close()
  }
}

// 弹窗关闭切换事件
function handleStatusChange(info) {
  if (!info.show) {
    emits('closeModal')
  }
}
function handleChange(e) {
  const index = e.detail.value
  changeIndex(index)
}
const unBindList = ref([])
const getUnbindList = () => {
  http.get('Assistbindpalm/unbindList', {
    bus_id: userStore.userInfo.bus_id,
    user_id: userStore.userInfo.user_id
  }).then((res) => {
    unBindList.value = res.data.list || []
    changeIndex(0)
  })
}
function changeIndex(index: number) {
  userIndex.value = index
  if (unBindList.value.length > 0) {
    const selectedUser = unBindList.value[index]
    postData.bind_palm_user_id = selectedUser.user_id
    postData.bind_palm_username = selectedUser.username
  }
}
function addBindInfo() {
  if(!unBindList.value.length) {
    closePopup()
    return
  }
  if(!postData.bind_palm_user_id || !postData.bind_palm_username) {
    uni.showToast({
      title: '请输入信息',
      icon: 'none'
    })
    return
  }
  http.post('Assistbindpalm/addBindInfo', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    openid: userStore.userInfo.openid,
    ...postData,
    loading: true,
  }).then((res) => {
    getPreBind();
  })
}
const prebindToken = ref('');
function getPreBind() {
  http.get('Assistbindpalm/preBind', {
    bus_id: userStore.userInfoBusId,
    bind_palm_user_id: postData.bind_palm_user_id,
    expire_time: postData.expire_time,
    loading: true,
  }).then((res) => {
      prebindToken.value = res.data.prebind_token;
      if(!prebindToken.value){
        uni.showToast({
          title: '未获取到预绑定值，绑定失败',
          icon: 'none',
        })
        return false
      }
      // 携带 prebind_token 跳转刷掌小程序
      wx.navigateToMiniProgram({
        appId: 'wxd6ced4b24adebe76',
        path: 'pages/palm-router/palm-router?action=assist_bind_online',
        extraData: {
          prebind_token: prebindToken.value,
        },
        success(res) {
          closePopup()
        }
      })

  })
}
</script>

<style lang="scss" scoped>
.help-wrap {
  padding: 70rpx 45rpx 30rpx;
  background: #fff;
  .normal-btn {
    width: 350rpx;
    margin: 40rpx auto;
  }
  .form-items {
    padding: 0;
    margin-top: 60rpx;
    .item {
      background: rgba(255,116,39,0.1);
      border-radius: 6rpx;
      border-bottom:0;
      padding: 0 30rpx;
      margin-bottom: 20rpx;
    }
  }
}
.msg-tit {
  font-weight: bold;
  font-size: 36rpx;
  color: #000000;
  text-align: center;
}
</style>
