<template>
  <picker
    range-key="name"
    :range="busList"
    @change="handleChange"
  >
    <slot>
      <view class="header-top-rig">
      更换场馆
      </view>
    </slot>
  </picker>
</template>

<script setup lang="ts">

interface Props {
  busList: array
  modelValue?: string
}
const props = withDefaults(defineProps<Props>(), {
  busList: [],
  disabled: false,
})

const emits = defineEmits(['update:modelValue', 'change'])
const selectedBusId = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  },
})

function handleChange(e) {
  const index = e.detail.value
  emits('change', index)
}

</script>

<style lang="scss" scoped>
</style>
