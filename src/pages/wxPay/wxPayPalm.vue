<template>
  <view class="container">
    <NavBar icon-word="回到首页" default-color="#000" />
    <view class="contain" style="padding-top: 200rpx">
      <view v-if="busInfo.name" class="header-top">
        <view class="header-top-left">
          <view class="top-tit">{{busInfo.name}}</view>
          <view class="top-con">{{busInfo.address}}</view>
        </view>
        <SwitchBusPick
          :busList="busList"
          @change="handleChange"
        >
          <view class="header-top-rig">
          更换场馆
          </view>
        </SwitchBusPick>
      </view>
      <view v-if="userInfo.user_id" class="header">
        <text>你好，欢迎开通刷掌服务</text>
      </view>
      <view v-else class="header noInformation">
        <text>未查询到会员信息</text>
        <text>请授权手机号关联或快速注册会员</text>
      </view>
      <view class="card">
        <view class="cardHeader">
          <text>{{ userInfo.merchants_name || busInfo.name }}</text>
        </view>
        <view v-if="isShowAuth" class="cardFooter">
          <text>{{ userInfo.nickname }}</text>
          <text>{{ userInfo.formatPhone }}</text>
        </view>
      </view>
      <view v-if="isShowAuth" class="btn" @tap="handleNext">下一步</view>
      <view v-else class="btn" @tap="goLogin">授权微信手机号</view>
    </view>
    <BusInfoModal :show="showConfirmBus" :busList="busList" :busInfo="busInfo" @busConfirm="handleBusConfirm" @change="handleBusConfirmChange" />
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import NavBar from '@/components/NavBar.vue'
import { unescapeHTML } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin.ts'
import BusInfoModal from './components/BusInfoModal.vue'
import SwitchBusPick from './components/SwitchBusPick.vue'

const userStore = useUserStore()
const showConfirmBus = ref(false)
const { checkLogin } = useLogin()
// 场馆信息
const busInfo = reactive({
  name: '',
})
// 用户信息
const userInfo = reactive({
  user_id: '',
  nickname: '',
  phone: '',
  avatar: '',
  point: 0,
  ticket: 0,
  formatPhone: '',
  merchants_id: '',
  merchants_name: '',
  bind_wx_palmservice: false,
  reservation_count: 0,
  sign_days_count: 0,
  sign_duration: 0,
  sign_num_count: 0,
})
// 是否授权
const isShowAuth = ref(false)
// 预授权id
const id = ref('')
// 用户user_id
const user_id = ref('')

// 卸载阶段
onUnload(() => {
  uni.reLaunch({
    url: 'pages/index/index',
  })
})

// 开始阶段
onLoad((options) => {
  /**
   * 本页逻辑梳理
   * 1.未登录时,跳转至登录界面去登陆/注册,然后再跳转回来
   * 2.已登录时,点击下一步跳转至微信刷掌小程序签约页面
   */
  if (options?.preauthorize_session_id) {
    id.value = options.preauthorize_session_id
  }
  
})

// 每次进入页面都检查是否登录
onShow(() => {
  checkUserInfo(false)
})

function handleNext() {
  // 预授权会话ID preauthorize_session_id
  // 机构用户ID organization_user_id
  if (!userInfo.phone || !userInfo.merchants_id || !id.value) {
    const tips = {
      phone: '缺少手机号',
      merchants_id: '缺少商户ID',
    }
    uni.showToast({
      title: !id.value?'缺少刷掌预授权会话ID':tips[userInfo.phone ? 'merchants_id' : 'phone'],
      icon: 'none',
    })
    return false
  }
  // if (userInfo.bind_wx_palmservice) {
  //   uni.showToast({
  //     title: `请勿重复开通!`,
  //     icon: 'none',
  //   })
  //   return false
  // }
  showConfirmBus.value = true
}

const busList = ref([])
// 获取场馆列表
const getBusList = (bus_id) => {
  http
    .post('/Business/getBusListByHand', {
      bus_id,
      lng: userStore.locationInfo.longitude,
      lat: userStore.locationInfo.latitude,
    })
    .then((res) => {
      busList.value = res.data
    })
}

function getIndexByBusId(busId: string, list?: any[]) {
  const index = (list || busList.value).findIndex((item) => item.id === busId)
  return index >= 0 ? index : 0
}
function handleChange(index) {
  changeIndex(index)
}

const selectedBusId = ref('') 
function changeIndex(index: number) {
  const selectBusInfo = busList.value[index || 0]
  busInfo.bus_id = selectBusInfo.id
  busInfo.name = selectBusInfo.name
  busInfo.address = selectBusInfo.address
  selectedBusId.value = selectBusInfo.id
  checkUserInfo(false, busInfo.bus_id)
}
// 获取场馆信息
const getBusInfo = (bus_id) => {
  http
    .get('/Business/getDetail', {
      bus_id,
      config_index: 0,
      is_index: true,
    })
    .then((res) => {
      const info = res.data?.info
      Object.assign(busInfo, info)
    })
}

// 获取个人信息
const getInfo = (postData) => {
  http.get('Personalcenter/getIndexInfo', postData).then((res) => {
    const info = JSON.parse(JSON.stringify(res.data))
    info.formatPhone = formatPhone(info.phone)
    Object.assign(userInfo, info)
    uni.setStorageSync('userInfo', info)
  })
}

// 手机号码脱敏处理:方法1
const formatPhone = (phone) => {
  const str = phone.substring(0, 3) + '****' + phone.substring(7)
  return str
}
function handleBusConfirmChange(index) {
  showConfirmBus.value = false
  changeIndex(index)
}
// 跳转至微信授权刷掌
const handleBusConfirm = () => {
  showConfirmBus.value = false
  const postData = {
    phone: userInfo.phone,
    preauthorize_session_id: id.value,
    merchants_id: userInfo.merchants_id,
    bus_id: userStore.userInfoBusId,
    user_id: user_id.value,
  }
  const url = '/WechatPalm/preauthorize'
  http.post(url, postData).then((res) => {
    if (!res.data || !res.data.permission_token) {
      uni.showToast({
        title: `未获取到授权token!`,
        icon: 'none',
      })
      return false
    }
    wx.navigateBackMiniProgram({
      extraData: {
        authorize_token: `${res.data.permission_token}`,
      },
      success(res) {
        // 返回成功
      },
    })
  })
}

// 检查登录是否跳过  false会跳转至登录反之不跳
const checkUserInfo = (skip = true, busId = '') => {
  checkLogin(skip, busId || '', false).then((info) => {
    const busId = info.bus_id
    if (busId) {
      if(busInfo.bus_id !== busId) {
        getBusInfo(busId)
      }
      if(!busList.value || !busList.value.length) {
        getBusList(busId)
      }
    }
    if (info.user_id) {
      isShowAuth.value = true
      user_id.value = info.user_id
      getInfo(info)
    } else {
      isShowAuth.value = false
      Object.assign(userInfo, {
        nickname: '',
        phone: '',
        avatar: '',
        point: 0,
        sign_days_count: 0,
        sign_duration: 0,
        sign_num_count: 0,
      })
      uni.setStorageSync('userInfo', '')
    }
  })
}

// 未登录/注册-跳转去登录/注册
const goLogin = () => {
  checkUserInfo()
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/wx-pay-palm-bg.png');
  background-size: cover;
}
.contain {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 72rpx;
    text {
      font-size: 48rpx;
      font-weight: bold;
      color: #000000;
    }
  }
  .noInformation {
    text + text {
      font-size: 32rpx;
      margin-top: 46rpx;
    }
  }
  .card {
    width: 628rpx;
    height: 380rpx;
    box-sizing: border-box;
    padding: 42rpx 40rpx;
    margin-bottom: 180rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/wx-pay-palm-card-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    .cardHeader {
      width: 100%;
      text {
        font-size: 44rpx;
        text-align: left;
        font-weight: bold;
        color: #ffffff;
      }
    }
    .cardFooter {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      text {
        text-align: right;
        font-size: 44rpx;
        font-weight: bold;
        color: #ffffff;
      }
      text + text {
        font-size: 36rpx;
        margin-top: 28rpx;
      }
    }
  }
  .btn {
    width: 460rpx;
    height: 90rpx;
    border-radius: 10rpx;
    background: #07c160;
    font-size: 36rpx;
    font-weight: bold;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 628rpx;
  margin-bottom: 80rpx;
}
.header-top-left {
  width: 500rpx;
}
.top-tit {
  font-size: 40rpx;
  line-height: 47rpx;
  font-weight: bold;
  color: #000;
  margin-bottom: 12rpx;
}
.top-con {
  font-size: 24rpx;
  color: #000000;
  line-height: 28rpx;
}
.header-top-rig {
  font-weight: bold;
  font-size: 26rpx;
  color: #00B5B6;
  line-height: 30rpx;
}
</style>
