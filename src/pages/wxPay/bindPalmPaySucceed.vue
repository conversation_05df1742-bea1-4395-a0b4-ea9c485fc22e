<template>
  <div class="box">
    <img
      v-if="isYingDong"
      class="bg-palm"
      src="https://imagecdn.rocketbird.cn/minprogram/uni-member/bind-palm.png"
      alt="bind-palm"
    />
    <img
      v-else
      class="bg-palm bg-palm-other"
      src="https://imagecdn.rocketbird.cn/minprogram/uni-member/bind-palm-other.png"
      alt="bind-palm"
    />
    <p v-if="isYingDong" class="label">— 在英东游泳馆，使用微信刷掌 —</p>
    <div class="title">去购票</div>
    <div class="ticket">
      <div v-if="isYingDong" class="ticket-info">
        <div class="name">英东游泳馆购票</div>
        <div class="desc">新英东，齐运动。</div>
      </div>
      <div v-else class="ticket-info">
        <div class="name">轻松刷掌，智慧运动</div>
        <div class="desc">出手即非凡</div>
      </div>
      <div class="ticket-btn">
        <div class="btn" @click="handleBuyTicket">购票</div>
      </div>
    </div>
    <div class="done-btn">
      <div class="btn" @click="handleBackHome">我知道了</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { goUrlPage } from '@/utils/urlMap'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const isYingDong = ref(false)
function getAppidToEqule() {
  const accountInfo = uni.getAccountInfoSync()
  const appid = accountInfo.miniProgram.appId
  // 国家奥体英东游泳馆 appid 线上商家id：BBB6-vBAqkl
  if (appid === 'wx0919c7916f6f3847' || useUserStore.userInfoMId === 'BBB6-vBAqkl') {
    isYingDong.value = true
  }
}
getAppidToEqule()
// event
const handleBuyTicket = () => {
  goUrlPage('/pages/stadium/ticketList')
}
const handleBackHome = () => {
  // uni.switchTab({
  //   url: '/pages/index/index',
  // })
  wx.exitMiniProgram()
}
</script>

<style lang="scss" scoped>
.box {
  height: 100%;
  background-color: white;

  .bg-palm {
    width: 750rpx;
    height: 1042rpx;
  }
  .bg-palm-other {
    margin-bottom: -280rpx;
  }

  .label {
    font-size: 30rpx;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.3);
    line-height: 35rpx;
    text-align: center;
    margin-top: -255rpx;
  }

  .title {
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.5);
    line-height: 33rpx;
    margin-top: 96rpx;
    margin-left: 34rpx;
  }

  .ticket {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 24rpx;
    margin-left: 34rpx;
    width: 682rpx;
    height: 180rpx;
    border: 1rpx dashed black;
    border-radius: 4rpx;
    background-color: #fffadf;

    .ticket-info {
      display: flex;
      flex-direction: column;

      .name {
        margin-left: 24rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: #000000;
        line-height: 40rpx;
      }

      .desc {
        margin-left: 24rpx;
        margin-top: 10rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5);
        line-height: 30rpx;
      }
    }

    .ticket-btn {
      margin-right: 32rpx;

      .btn {
        width: 118rpx;
        height: 56rpx;
        background: #07c160;
        border-radius: 6rpx 6rpx 6rpx 6rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #ffffff;
        line-height: 33rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .done-btn {
    margin-top: 108rpx;
    .btn {
      width: 368rpx;
      height: 80rpx;
      background: #f2f2f2;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-size: 34rpx;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.9);
      line-height: 48rpx;
      letter-spacing: 1px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }
  }
}
</style>
