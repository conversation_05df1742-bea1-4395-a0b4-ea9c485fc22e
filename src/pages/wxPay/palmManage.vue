<template>
  <view class="contain">
    <view class="main-wrap">
      <view v-if="!bindList.length" class="nodata">暂未开通微信刷掌服务</view>
       <!-- 1未录掌，2已录掌，3已失效 -->
       <view class="palm-list">
        <view class="palm-item" v-for="item in bindList" :key="item.id" :class="{'abnormal-state':item.bind_status === 1 || item.bind_status === 3}">
          <view class="time-down" v-if="countdowns[item.id]">刷掌还需<text class="time-down-text">{{countdowns[item.id]}}s</text>生效</view>
          <view class="palm-item-left" :style="{ 'align-items': item.bind_status===1 || (item.bind_status===2 && item.expire_time) ? 'flex-start':'center'}">
            <view class="left-tag">{{statusArr[+item.bind_status - 1]}}</view>
            <view class="left-content">
              <view class="left-title">{{item.id===0?'本人':'亲友'}}：{{item.username}} {{item.phone}}</view>
              <view class="left-des" v-if="item.bind_status===1">绑定异常，删除后重新录入</view>
              <view class="left-des" v-if="item.bind_status===2 && item.expire_time">{{item.expire_time}}过期</view>
            </view>
          </view>
          <view class="palm-item-right" v-if="item.id===0">
              <view v-if="item.bind_status === 1 || item.bind_status === 3" class="right-btn-text" @tap="handleDelete(item)">删除</view>
              <view v-if="item.bind_status === 2" class="right-btn-text" @tap="closeServer(item)">解绑</view>
          </view>
          <view class="palm-item-right" v-else>
              <view v-if="item.bind_status === 1 || item.bind_status === 3" class="right-btn-text" @tap="handleDelete(item)">删除</view>
              <view v-if="item.bind_status === 2" class="right-btn-text" @tap="handleStop(item)">停用</view>
          </view>
        </view>   
       </view>
       <view class="palm-item-bottom">
          <button v-if="palmRes.assist_open" class="normal-btn" @tap="handleHelpOpen">
            协助开掌
          </button>
          <button v-if="isShowSelfPalm" class="normal-btn bg-org" @tap="handleSelfPalm">
            本人开掌
          </button>
       </view>
    </view>
    <HelpOpen :show="isShowHelpOpen" @closeModal="closeModal" />
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import HelpOpen from './components/help-open.vue'
import { useLogin } from '@/hooks/useLogin.ts'
import { usePalm } from '@/hooks/usePalm'

const isShowHelpOpen = ref(false)
const statusArr = ref(['未录掌','已录掌','已失效'])
// 是否已经开通
const org_id = ref('')
const bool = ref(false)
const { checkLogin, getParam } = useLogin()
const loginUserInfo = reactive({
  bus_id: '',
  user_id: '',
})
const palmRes = ref({})
const bindList = ref([])
const isShowSelfPalm = ref(false)
const countdowns = ref({}) // 用于存储每个id对应的倒计时
const intervalId = ref(null)
const curOption = ref(null)
onLoad((options) => {
  curOption.value = options
  pathType.value = options.pathType || '4'
})

onShow(async () => {
  const curParams = await getParam(curOption.value.scene || '')
  checkLogin(true, curParams.bus_id || curOption.value.bus_id).then((info) => {
    Object.assign(loginUserInfo, info)
    getInfo()
  })
})

const getInfo = () => {
  http.get('Assistbindpalm/bindList', loginUserInfo).then((res) => {
    palmRes.value = res.data
    bindList.value = res.data.list
    isShowSelfPalm.value = res.data.firmament_open === 1
    updateCountdowns()
  })
}
const updateCountdowns = () => {
  const now = Date.now()
  countdowns.value = {}
  bindList.value.forEach(item => {
    if (item.bind_status === 2) {
      isShowSelfPalm.value = item.id===0 ? false : isShowSelfPalm.value
      const createTime = new Date(item.create_time).getTime()
      const diff = 120 - Math.ceil((now - createTime) / 1000)
      if (diff > 0) {
        countdowns.value[item.id] = diff
      }
    }
  })
  if(intervalId.value) {
    clearInterval(intervalId.value)
  }
  startCountdown()
}
const startCountdown = () => {
   intervalId.value = setInterval(() => {
    const now = Date.now()
    for (const id in countdowns.value) {
      if (countdowns.value[id] > 0) {
        countdowns.value[id] -= 1
      } else {
        delete countdowns.value[id]
      }
    }
  }, 1000)
  onUnmounted(() => clearInterval(intervalId.value))
}
function closeServer(info) {
  if (!info.organization_id) {
    uni.showToast({
      title: '缺少机构id',
      icon: 'none',
    })
    return false
  }
  // org_id 机构id
  wx.navigateToMiniProgram({
    appId: 'wxd6ced4b24adebe76',
    path: `pages/palm-router/palm-router`,
    extraData: {
      action: 'admin', // 表示跳转管理页，固定参数
      org_id: `${info.organization_id}`,
    },
    success(res) {
      // 打开成功
    },
  })
}
function handleStop(info) {
  uni.showModal({
    title: '停用刷掌服务',
    content: '停用后，不能继续【刷掌】使用商家服务，录入的手掌信息也将删除。',
    confirmText: '停用',
    cancelText: '取消',
    success: function (res) {
      if (res.confirm) {
        http.post('Assistbindpalm/unbindInfo', {
          bus_id: loginUserInfo.bus_id,
          id: info.id,
        }).then((res) => {
          getInfo();
          uni.showToast({
            title: '已停用',
            icon: 'none',
          })
        })
      }
    }
  })
}
function handleDelete(info) {
  uni.showModal({
    title: '删除手掌信息',
    content: '录入的手掌信息将被删除。',
    confirmText: '删除',
    cancelText: '取消',
    success: function (res) {
      if (res.confirm) {
        http.post('Assistbindpalm/unbindInfo', {
          bus_id: loginUserInfo.bus_id,
          id: info.id,
          phone: info.phone,
        }).then((res) => {
          getInfo();
          uni.showToast({
            title:'已删除',
            icon: 'none',
          })
        })
      }
    }
  })
}

function closeModal() {
  isShowHelpOpen.value = false
}

const pathType = ref('4') //  是否空中刷掌 默认0不是 1首页弹窗开通 2进店指引 3小程序购卡/票 4刷掌服务
const palmInfo = ref({
  prebind_token: '',
  service_id: ''
})
const { palmStaReport } = usePalm()
function handleHelpOpen() {
  isShowHelpOpen.value = true
  palmStaReport('assist')
}
function handleSelfPalm() {
  palmStaReport('palmservice_service')
  http.post('Wechatpalm/preauthorizeFirmament', {
    bus_id: loginUserInfo.bus_id,
    user_id: loginUserInfo.user_id,
    is_firmament: pathType.value, 
  }).then((res) => {
      palmInfo.value = res.data;
      if(!palmInfo.value || !palmInfo.value.prebind_token || !palmInfo.value.service_id){
        uni.showToast({
          title: '获取信息错误，绑定失败',
          icon: 'none',
        })
        return false
      }
      // 跳转刷掌小程序，本人预开掌
      wx.navigateToMiniProgram({
        appId: 'wxd6ced4b24adebe76',
        path: 'pages/palm-router/palm-router?action=third_online_open',
        extraData: {
          prebind_token: palmInfo.value.prebind_token,
          service_id: palmInfo.value.service_id
        }
      })

  })
}
</script>

<style lang="scss" scoped>
.contain {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: scroll;
  .nodata {
    height: 289rpx;
    margin-bottom: 20rpx;
  }
  .normal-btn {
    width: 310rpx;
    height: 90rpx;
    border: 1px solid $theme-text-color-other;
    color:  $theme-text-color-other;
    background: #fff;
    margin: 50rpx 0;
    &:nth-child(2) {
      margin-left: 30rpx;
    }
  }
  .bg-org {
    color: #fff;
    background: $theme-text-color-other;
  }
}
.main-wrap {
  width: 690rpx;
  background: #FFF;
  border-radius: 10rpx;
  margin: 45rpx 0;
}
.palm-list {
  padding: 30rpx 20rpx 0;
  .palm-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    padding: 30rpx;
    margin-bottom: 20rpx;
    background: #FEF5F0;
    border-radius: 10rpx;
    position: relative;
    .time-down {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(231, 231, 231, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      font-weight: bold;
      .time-down-text {
        color: #FF7427;
      }
    }
  }
  .palm-item-left {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .left-tag {
      width: 84rpx;
      height: 33rpx;
      line-height: 33rpx;
      text-align: center;
      background: rgba(255,116,39,0.1);
      border-radius: 6rpx;
      width: 60rpx;
      font-weight: bold;
      font-size: 20rpx;
      color: #FF7427;
    }
    .left-content {
      margin-left: 16rpx;
      flex: 1;
      overflow: hidden;
      .left-title {
        font-weight: bold;
        font-size: 26rpx;
        color: #000;
        line-height: 40rpx;
      }
      .left-des {
        font-size: 22rpx;
        color: #7D7D7D;
        line-height: 35rpx;
        text-align: left;
        margin-top: 10rpx;
      }
    }
  }
  .palm-item-right {
    margin-left: 8rpx;
    flex-shrink: 0;
  }
  .left-tag {
    height: 33rpx;
    padding: 0 6rpx;
    font-size: 20rpx;
    background: rgba(255,116,39,0.1);
    border-radius: 6rpx;
  }
  .right-btn-text {
    font-weight: bold;
    font-size: 24rpx;
    color: #FF462E;
  }
  .abnormal-state {
    background: #E7E7E7;
    .left-tag {
      background: rgba(255,70,46,0.1);
      color: #FF462E;
    }
    .left-title,.left-des {
      color: #7D7D7D !important;
    }
  }
}
.palm-item-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
