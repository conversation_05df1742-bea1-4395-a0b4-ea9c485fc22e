<template>
  <view class="box">
    <view class="body">
      <view class="body-title">
        <view class="title-date"> </view>
        <navigator open-type="navigateBack">
          <view class="title-link-trend">
            <text>单次体测明细</text>
            <image
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-next.png"
            ></image>
          </view>
        </navigator>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            体重 <text v-if="state.weightIdeal">(理想值 {{ state.weightIdeal }})</text>
          </view>
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyWeight"
            @touchmove="moveHandlerWeight"
            @touchend="touchEndHandlerWeight"
            @touchstart="touchHandlerWeight"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.weightTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.weightTrend }}kg
          </view>
          <view v-if="state.weightTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.weightTrend }}kg
          </view>
          <view v-if="state.weightTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.weightTrend }}kg
          </view>
        </view>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            去脂体重 <text v-if="state.withoutFatIdeal">(理想值 {{ state.withoutFatIdeal }})</text>
          </view>
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyWithoutFat"
            @touchmove="moveHandlerWithoutFat"
            @touchend="touchEndHandlerWithoutFat"
            @touchstart="touchHandlerWithoutFat"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.withoutFatTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.withoutFatTrend }}kg
          </view>
          <view v-if="state.withoutFatTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.withoutFatTrend }}kg
          </view>
          <view v-if="state.withoutFatTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.withoutFatTrend }}kg
          </view>
        </view>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            体脂百分比 <text v-if="state.fatIdeal">(理想值 {{ state.fatIdeal }})</text></view
          >
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyFat"
            @touchmove="moveHandlerFat"
            @touchend="touchEndHandlerFat"
            @touchstart="touchHandlerFat"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.fatTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.fatTrend }}
          </view>
          <view v-if="state.fatTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.fatTrend }}
          </view>
          <view v-if="state.fatTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.fatTrend }}
          </view>
        </view>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            BMI <text v-if="state.bmiIdeal">(理想值 {{ state.bmiIdeal }})</text>
          </view>
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyBMI"
            @touchmove="moveHandlerBMI"
            @touchend="touchEndHandlerBMI"
            @touchstart="touchHandlerBMI"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.bmiTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.bmiTrend }}kg/㎡
          </view>
          <view v-if="state.bmiTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.bmiTrend }}kg/㎡
          </view>
          <view v-if="state.bmiTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.bmiTrend }}kg/㎡
          </view>
        </view>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            骨骼肌 <text v-if="state.chestIdeal">(理想值 {{ state.chestIdeal }})</text>
          </view>
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyChest"
            @touchmove="moveHandlerChest"
            @touchend="touchEndHandlerChest"
            @touchstart="touchHandlerChest"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.chestTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.chestTrend }}kg
          </view>
          <view v-if="state.chestTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.chestTrend }}kg
          </view>
          <view v-if="state.chestTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.chestTrend }}kg
          </view>
        </view>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            腰臀比 <text v-if="state.absIdeal">(理想值 {{ state.absIdeal }})</text>
          </view>
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyAbs"
            @touchmove="moveHandlerAbs"
            @touchend="touchEndHandlerAbs"
            @touchstart="touchHandlerAbs"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.absTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.absTrend }}
          </view>
          <view v-if="state.absTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.absTrend }}
          </view>
          <view v-if="state.absTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.absTrend }}
          </view>
        </view>
      </view>

      <view class="item">
        <view class="item-head">
          <view class="title">
            身体水分含量 <text v-if="state.rumpIdeal">(理想值 {{ state.rumpIdeal }})</text>
          </view>
        </view>
        <view class="item-body">
          <canvas
            canvas-id="checkBodyRump"
            @touchmove="moveHandlerRump"
            @touchend="touchEndHandlerRump"
            @touchstart="touchHandlerRump"
          ></canvas>
        </view>
        <view class="item-foot">
          <view v-if="state.rumpTrendFlag == '+'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--up.png"
            ></image>
            {{ state.rumpTrend }}kg
          </view>
          <view v-if="state.rumpTrendFlag == '='">
            和上次持平
            <image
              class="arrow-trend-eq"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon-equal.png"
            ></image>
            {{ state.rumpTrend }}kg
          </view>
          <view v-if="state.rumpTrendFlag == '-'">
            比上次
            <image
              class="arrow-trend"
              src="https://imagecdn.rocketbird.cn/minprogram/uni-member/physical-examination-icon--down.png"
            ></image>
            {{ state.rumpTrend }}kg
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="physicalExamination">
import http from '@/utils/request'
import wxCharts from '@/utils/wxcharts'

let weightChart = null
let withoutFatChart = null
let fatChart = null
let bmiChart = null
let chestChart = null
let absChart = null
let rumpChart = null

const state = reactive({
  userId: '',
  weight: [],
  weightTrend: 0,
  weightTrendFlag: '',
  weightIdeal: 0,
  withoutFat: [],
  withoutFatTrend: 0,
  withoutFatTrendFlag: '',
  withoutFatIdeal: 0,
  fat: [],
  fatTrend: 0,
  fatTrendFlag: '',
  fatIdeal: 0,
  bmi: [],
  bmiTrend: 0,
  bmiTrendFlag: '',
  dateIdx: '',
  bmiIdeal: 0,
  chest: [],
  chestTrend: 0,
  chestTrendFlag: '',
  chestIdeal: 0,
  abs: [],
  absTrend: 0,
  absTrendFlag: '',
  absIdeal: 0,
  rump: [],
  rumpTrend: 0,
  rumpTrendFlag: '',
  rumpIdeal: 0,
})

onLoad((options) => {
  state.userId = options.uid || ''
  getTrendData()
})

onPullDownRefresh(() => {
  getTrendData().then(() => {
    uni.stopPullDownRefresh()
  })
})

const touchHandlerWeight = (e) => {
  weightChart.scrollStart(e)
}
const touchHandlerWithoutFat = (e) => {
  withoutFatChart.scrollStart(e)
}
const touchHandlerFat = (e) => {
  fatChart.scrollStart(e)
}
const touchHandlerBMI = (e) => {
  bmiChart.scrollStart(e)
}
const touchHandlerChest = (e) => {
  chestChart.scrollStart(e)
}
const touchHandlerAbs = (e) => {
  absChart.scrollStart(e)
}
const touchHandlerRump = (e) => {
  rumpChart.scrollStart(e)
}

const moveHandlerWeight = (e) => {
  weightChart.scroll(e)
}
const moveHandlerWithoutFat = (e) => {
  withoutFatChart.scroll(e)
}
const moveHandlerFat = (e) => {
  fatChart.scroll(e)
}
const moveHandlerBMI = (e) => {
  bmiChart.scroll(e)
}
const moveHandlerChest = (e) => {
  chestChart.scroll(e)
}
const moveHandlerAbs = (e) => {
  absChart.scroll(e)
}
const moveHandlerRump = (e) => {
  rumpChart.scroll(e)
}

const touchEndHandlerWeight = (e) => {
  weightChart.scrollEnd(e)
  weightChart.showToolTip(e, {
    format(item, category) {
      state.weight.forEach((item) => {
        if (item.test_time == category) {
          state.weightTrend = item.compare_to_last
          state.weightTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}
const touchEndHandlerWithoutFat = (e) => {
  withoutFatChart.scrollEnd(e)
  withoutFatChart.showToolTip(e, {
    format(item, category) {
      state.withoutFat.forEach((item) => {
        if (item.test_time == category) {
          state.withoutFatTrend = item.compare_to_last
          state.withoutFatTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}
const touchEndHandlerFat = (e) => {
  fatChart.scrollEnd(e)
  fatChart.showToolTip(e, {
    format(item, category) {
      state.fat.forEach((item) => {
        if (item.test_time == category) {
          state.fatTrend = item.compare_to_last
          state.fatTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}
const touchEndHandlerBMI = (e) => {
  bmiChart.scrollEnd(e)
  bmiChart.showToolTip(e, {
    format(item, category) {
      state.bmi.forEach((item) => {
        if (item.test_time == category) {
          state.bmiTrend = item.compare_to_last
          state.bmiTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}
const touchEndHandlerChest = (e) => {
  chestChart.scrollEnd(e)
  chestChart.showToolTip(e, {
    format(item, category) {
      state.chest.forEach((item) => {
        if (item.test_time == category) {
          state.chestTrend = item.compare_to_last
          state.chestTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}
const touchEndHandlerAbs = (e) => {
  absChart.scrollEnd(e)
  absChart.showToolTip(e, {
    format(item, category) {
      state.abs.forEach((item) => {
        if (item.test_time == category) {
          state.absTrend = item.compare_to_last
          state.absTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}
const touchEndHandlerRump = (e) => {
  rumpChart.scrollEnd(e)
  rumpChart.showToolTip(e, {
    format(item, category) {
      state.rump.forEach((item) => {
        if (item.test_time == category) {
          state.rumpTrend = item.compare_to_last
          state.rumpTrendFlag = item.trend
        }
      })
      return category + ' ' + item.name + ':' + item.data
    },
  })
}

/**
 * 趋势图初始化时滑动到最右边
 */
function turnRightWalk(chart) {
  setTimeout(function () {
    chart.scrollStart({
      touches: [
        {
          x: 600,
        },
      ],
    })
    chart.scroll({
      touches: [
        {
          x: 300,
        },
      ],
    })
    chart.scrollEnd({})
  }, 2222)
}

/**
 * 绘制趋势图
 */
function drawTrendCanvas(canvasId, xField, yField, maxKG, minKG, yFormat, seriesName = '体重') {
  let windowWidth = 320
  try {
    const res = uni.getSystemInfoSync()
    windowWidth = res.windowWidth
  } catch (e) {
    console.error('getSystemInfoSync failed!')
  }

  return new wxCharts({
    canvasId: canvasId,
    type: 'line',
    categories: xField,
    animation: true,
    legend: true,
    series: [
      {
        name: seriesName,
        color: '#4ad0c5',
        data: yField,
        format(val) {
          return val.toFixed(1)
        },
      },
    ],
    xAxis: {
      disableGrid: true,
      gridColor: 'white',
      fontColor: '#b0bbdc',
    },
    yAxis: {
      disableGrid: true,
      gridColor: 'white',
      fontColor: '#b0bbdc',
      min: minKG,
      max: maxKG,
      format: yFormat,
    },
    width: windowWidth,
    height: 140,
    background: 'white',
    dataLabel: true,
    dataPointShape: true,
    enableScroll: true,
    extra: {
      lineStyle: 'curve',
    },
  })
}

/**
 * 获取趋势图数据
 */
function getTrendData() {
  return http
    .post(
      `Coach/InBodyData/getStaminaTendency`,
      {
        uid: state.userId,
      },
      'saasUrl'
    )
    .then((res) => {
      if (res.errorcode == 0) {
        const resData = res.data

        // 体重
        let xField = []
        let yField = []
        let maxKilogram = 0
        let minKilogram = 200
        if (Array.isArray(resData.weight)) {
          if (resData.weight.length === 0) {
            maxKilogram = 105
            minKilogram = 37
          }
          resData.weight.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
            if (maxKilogram < val) {
              maxKilogram = val
            }
            if (minKilogram > val) {
              minKilogram = val
            }
          })
        }
        maxKilogram += 5
        minKilogram -= 5

        weightChart = drawTrendCanvas(
          'checkBodyWeight',
          xField,
          yField,
          maxKilogram,
          minKilogram,
          (val) => val + 'kg'
        )

        // 去脂体重
        xField = []
        yField = []
        if (Array.isArray(resData.weight_without_fat)) {
          if (resData.weight.length === 0) {
            maxKilogram = 100
            minKilogram = 20
          }
          resData.weight_without_fat.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
            if (maxKilogram < val) {
              maxKilogram = val
            }
            if (minKilogram > val) {
              minKilogram = val
            }
          })
        }
        withoutFatChart = drawTrendCanvas(
          'checkBodyWithoutFat',
          xField,
          yField,
          maxKilogram,
          minKilogram,
          (val) => val + 'kg',
          '去脂体重'
        )

        // 体脂
        xField = []
        yField = []
        if (Array.isArray(resData.body_fat_rate)) {
          resData.body_fat_rate.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
          })
        }
        fatChart = drawTrendCanvas(
          'checkBodyFat',
          xField,
          yField,
          100,
          0,
          (val) => val + '%',
          '体脂'
        )

        // BMI
        xField = []
        yField = []
        if (Array.isArray(resData.BMI)) {
          resData.BMI.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
          })
        }
        bmiChart = drawTrendCanvas('checkBodyBMI', xField, yField, 50, 0, (val) => val, 'BMI')

        // 骨骼肌
        xField = []
        yField = []
        if (Array.isArray(resData.skeletal_muscle)) {
          if (resData.weight.length === 0) {
            maxKilogram = 100
            minKilogram = 0
          }
          resData.skeletal_muscle.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
            if (maxKilogram < val) {
              maxKilogram = val
            }
            if (minKilogram > val) {
              minKilogram = val
            }
          })
        }
        chestChart = drawTrendCanvas(
          'checkBodyChest',
          xField,
          yField,
          maxKilogram,
          minKilogram,
          (val) => val + 'kg',
          '骨骼肌'
        )

        // 腰臀比
        xField = []
        yField = []
        if (Array.isArray(resData.waist_hip_ratio)) {
          resData.waist_hip_ratio.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
          })
        }
        absChart = drawTrendCanvas('checkBodyAbs', xField, yField, 1, 0.6, (val) => val, '腰臀比')

        // 水分
        xField = []
        yField = []
        if (Array.isArray(resData.body_contain_water)) {
          if (resData.weight.length === 0) {
            maxKilogram = 100
            minKilogram = 0
          }
          resData.body_contain_water.forEach((item) => {
            const val = parseFloat(item.value ? item.value : 0)
            xField.push(item.test_time)
            yField.push(val)
            if (maxKilogram < val) {
              maxKilogram = val
            }
            if (minKilogram > val) {
              minKilogram = val
            }
          })
        }
        rumpChart = drawTrendCanvas(
          'checkBodyRump',
          xField,
          yField,
          maxKilogram,
          minKilogram,
          (val) => val + 'kg',
          '身体水分含量'
        )

        turnRightWalk(weightChart)
        turnRightWalk(withoutFatChart)
        turnRightWalk(fatChart)
        turnRightWalk(bmiChart)
        turnRightWalk(chestChart)
        turnRightWalk(absChart)
        turnRightWalk(rumpChart)

        state.weight = resData.weight
        if (!!resData.weight_min && !!resData.weight_max) {
          state.weightIdeal = resData.weight_min + 'kg-' + resData.weight_max + 'kg'
        } else {
          state.weightIdeal = false
        }
        state.withoutFat = resData.weight_without_fat
        if (!!resData.weight_without_fat_min && !!resData.weight_without_fat_max) {
          state.withoutFatIdeal =
            resData.weight_without_fat_min + 'kg-' + resData.weight_without_fat_max + 'kg'
        } else {
          state.withoutFatIdeal = false
        }
        state.fat = resData.body_fat_rate
        if (!!resData.body_fat_rate_min && !!resData.body_fat_rate_max) {
          state.fatIdeal = resData.body_fat_rate_min + '%-' + resData.body_fat_rate_max + '%'
        } else {
          state.fatIdeal = false
        }
        state.bmi = resData.BMI
        if (!!resData.body_quality_min && !!resData.body_quality_max) {
          state.bmiIdeal = resData.body_quality_min + 'kg/㎡-' + resData.body_quality_max + 'kg/㎡'
        } else {
          state.bmiIdeal = false
        }
        state.chest = resData.skeletal_muscle
        if (!!resData.skeletal_muscle_min && !!resData.skeletal_muscle_max) {
          state.chestIdeal =
            resData.skeletal_muscle_min + 'kg-' + resData.skeletal_muscle_max + 'kg'
        } else {
          state.chestIdeal = false
        }
        state.abs = resData.waist_hip_ratio
        if (!!resData.waist_hip_ratio_min && !!resData.waist_hip_ratio_max) {
          state.absIdeal = resData.waist_hip_ratio_min + '-' + resData.waist_hip_ratio_max
        } else {
          state.absIdeal = false
        }
        state.rump = resData.body_contain_water
        if (!!resData.body_contain_water_min && !!resData.body_contain_water_max) {
          state.rumpIdeal =
            resData.body_contain_water_min + 'kg-' + resData.body_contain_water_max + 'kg'
        } else {
          state.rumpIdeal = false
        }
      }
    })
}
</script>
<style>
/* 解决小程序canvas元素悬浮，不随父组件滚动的问题 */
page,
.page,
.page-main {
  overflow: auto !important;
  height: auto !important;
}
</style>
<style lang="scss" scoped>
.body {
  background-color: #f5f5f5;
}

.body-title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 74rpx;
  width: 100%;

  .title-date {
    display: flex;
    flex-direction: row;
    align-items: center;

    image {
      height: 26rpx;
      width: 26rpx;
      margin-left: 20rpx;
    }

    picker {
      height: 26rpx;

      text {
        font-size: 26rpx;
        color: #262626;
        margin-left: 18rpx;
      }
    }
  }

  .title-link-trend {
    text {
      font-size: 26rpx;
      font-weight: bold;
      color: #0b78e3;
    }

    image {
      height: 20rpx;
      width: 11rpx;
      margin-left: 18rpx;
      margin-right: 20rpx;
    }
  }
}

.item:first-child {
  margin-top: 22px;
}

.item {
  background-color: white;
  height: 430rpx;
  width: 710rpx;
  margin: 12rpx auto;
}

.item-head {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 68rpx;
  margin: 0 30rpx;
}

.item-head .title {
  font-size: 26rpx;
  color: #353b63;
  margin-top: 36rpx;
}

.item-body {
  height: 294rpx;
  width: 100%;
}

.item-body canvas {
  height: 294rpx;
  width: 100%;
  touch-action: none;
}

.item-foot {
  height: 68rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-top: 1rpx solid #f3f6fa;
  font-size: 24rpx;
  color: #353b63;
  margin: 0 30rpx;

  .arrow-trend {
    height: 27rpx;
    width: 7rpx;
    margin: 0 10rpx;
  }

  .arrow-trend-eq {
    height: 7rpx;
    width: 28rpx;
    margin-bottom: 6rpx;
  }
}
</style>
