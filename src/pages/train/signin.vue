<template>
  <view
    v-if="bookingSignList.length > 0 || cardSignList.length > 0 || classMarklist.length > 0"
    class="sign-wrap"
  >
    <!-- 订场和团课列表可共存 但不可在多选时同时选中两类 -->
    <view v-if="bookingSignList.length > 0" class="sign-title">请选择需要签到的订场信息</view>

    <view v-if="bookingSignList.length > 0">
      <checkbox-group class="check-list" @change="bookingChange">
        <label
          v-for="(item, index) in bookingSignList"
          :key="index"
          class="check-list-label"
          :class="item.checked ? 'checked' : ''"
        >
          <SignSpaceItem :item="item" />
          <view class="checkbox-con con-middle">
            <checkbox
              :value="item.id"
              :checked="item.checked"
              :disabled="checkedBoxs.length !== 0"
            />
            <uni-icons
              v-if="item.checked"
              type="checkbox-filled"
              size="27"
              color="#FF7427"
            ></uni-icons>
            <view v-else class="checkbox-circle"></view>
          </view>
        </label>
      </checkbox-group>
    </view>
    <view class="sign-title">{{
      classMarklist.length > 0
        ? '请选择需要签到的课程'
        : cardSignList.length > 0
        ? '请选择本次签到使用的会员卡'
        : ''
    }}</view>
    <view v-if="classMarklist.length > 0">
      <checkbox-group class="check-list" @change="classMarkChange">
        <label
          v-for="(item, index) in classMarklist"
          :key="index"
          class="check-list-label"
          :class="item.checked ? 'checked' : ''"
        >
          <SignClassItem :item="item" />
          <view class="checkbox-con con-middle">
            <checkbox
              :value="item.id"
              :checked="item.checked"
              :disabled="bookingChecked.length !== 0"
            />
            <uni-icons
              v-if="item.checked"
              type="checkbox-filled"
              size="27"
              color="#FF7427"
            ></uni-icons>
            <view v-else class="checkbox-circle"></view>
          </view>
        </label>
      </checkbox-group>
    </view>
    <view v-else>
      <radio-group class="check-list" @change="radioChange">
        <label
          v-for="(item, index) in cardSignList"
          :key="index"
          class="check-list-label"
          :class="checkedVal == item.id ? 'checked' : ''"
        >
          <CardItem :item="item" />
          <view class="checkbox-con">
            <radio :value="item.id" />
            <uni-icons
              v-if="checkedVal == item.id"
              type="checkbox-filled"
              size="27"
              color="#FF7427"
            ></uni-icons>
            <view v-else class="checkbox-circle"></view>
          </view>
        </label>
      </radio-group>
    </view>

    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="signIn">确定</button>
    </view>
  </view>
  <view v-else class="nodata">数据加载中</view>
  <!-- 位置权限弹窗 -->
  <!-- <LocationPermissionDialog :show="showLocationDialog" @close="showLocationDialog = false" /> -->
</template>

<script setup lang="ts">
import http from '@/utils/request'
import CardItem from './components/CardItem'
import SignClassItem from './components/SignClassItem'
import SignSpaceItem from './components/SignSpaceItem'
import { useLogin } from '@/hooks/useLogin.ts'
import { useUserStore } from '@/store/user'
// import LocationPermissionDialog from '@/components/LocationPermissionDialog.vue'
const { checkLogin, getParam } = useLogin()
const userStore = useUserStore()

const isBusLocationSign = ref(true)
const alreadySign = ref(false)
const classMarklist = ref([])
const cardSignList = ref([])
const bookingSignList = ref([])
const bookingChecked = ref([])
const checkedBoxs = ref([])
const checkedVal = ref('')
const curOptions = reactive({
  bus_id: '',
  hasDoAction: false,
})
onLoad((options) => {
  Object.assign(curOptions, options)
})
onShow(async () => {
  const enterOptions = uni.getEnterOptionsSync()
  const curParam = await getParam(curOptions.scene||'')
  //进入场景值 扫描二维码和小程序码 长按二维码和小程序码
  if (
    // enterOptions.scene === 1011 ||
    // enterOptions.scene === 1047 ||
    // enterOptions.scene === 1012 ||
    // enterOptions.scene === 1048
    true
  ) {
    checkLogin(true, curOptions.bus_id || curParam.bus_id)
      .then((res) => {
        getCardList(res.busId, res.userId)
      })
      .catch((err) => {
        console.error(err)
      })
  } else {
    uni.redirectTo({
      url: '/pages/train/signResult?errorcode=-2&errormsg=请扫码签到',
    })
  }
})
function bookingChange(e) {
  bookingChecked.value = e.detail.value
  bookingSignList.value.forEach((item) => {
    if (bookingChecked.value.indexOf(item.id + '') != -1) {
      item.checked = true
    } else {
      item.checked = false
    }
  })
}
function classMarkChange(e) {
  checkedBoxs.value = e.detail.value
  classMarklist.value.forEach((item) => {
    if (checkedBoxs.value.indexOf(item.id + '') != -1) {
      item.checked = true
    } else {
      item.checked = false
    }
  })
}
function radioChange(e) {
  checkedVal.value = e.detail.value
}

function getCardList() {
  http
    .post('Scan/index', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      const data = res.data
      bookingSignList.value = data.user_booking_space_list
      classMarklist.value = data.user_class_mark_list
      cardSignList.value = data.user_card_sign_list
      isBusLocationSign.value = data.is_bus_location_sign
      if (
        bookingSignList.value.length === 0 &&
        classMarklist.value.length === 0 &&
        cardSignList.value.length === 1 &&
        cardSignList.value[0].status == 1
      ) {
        checkedVal.value = cardSignList.value[0].id
        signIn()
      }
    })
    .catch((err) => {
      handleErrPage(err)
    })
}

// const showLocationDialog = ref(false)
async function signIn() {
  if (isBusLocationSign.value) {
    await userStore.getLocationInfo(true)
    // showLocationDialog.value = userStore.getShowLocationDialog()
    // if (showLocationDialog.value) {
    //   uni.showToast({ title: '请先授权位置信息', icon: 'none' })
    //   return false
    // }
    goSign()
  } else {
    goSign()
  }
}

function goSign() {
  if (!alreadySign.value) {
    //防止onshow多次签到
    alreadySign.value = true
    //  订场签到  返回列表中同时返回订场和团课时会同时显示在页面中
    if (bookingChecked.value.length > 0) {
      bookingSign()
    } else if (classMarklist.value.length > 0) {
      // 团课签到
      userMarkSign()
    } else if (cardSignList.value.length > 0) {
      // 卡签到
      userCardSign()
    } else {
      uni.showToast({ title: '请先选择', icon: 'none' })
      alreadySign.value = false
    }
  } else {
    uni.showToast({ title: '需要扫码才可再次签到', icon: 'none' })
  }
}
function bookingSign() {
  http
    .post('/Booking/userSign', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      space_order_id: bookingChecked.value,
      lng: userStore.locationInfo.longitude,
      lat: userStore.locationInfo.latitude,
    })
    .then((res) => {
      handleSuccessPage({ isBooking: true })
    })
    .catch((err) => {
      handleErrPage(err)
    })
}
function userCardSign() {
  let curCard = ''
  if (cardSignList.value.length == 1) {
    curCard = cardSignList.value[0]
  } else if (!checkedVal.value.length) {
    uni.showToast({
      title: '请先选择!',
      icon: 'none',
    })
    return false
  } else {
    curCard = cardSignList.value.filter((item) => {
      return item.id == checkedVal.value
    })
    curCard = curCard[0]
  }
  signWay(curCard)
}
function signWay(curCard) {
  const isSuspend = cardSignList.value.some((card) => {
    return card.suspend_from == 2
  })
  if ((curCard.status == 3 || curCard.status == 2) && isSuspend) {
    uni.showModal({
      title: '提示',
      content: '此操作将启用全部请假状态的会员卡，是否启用？',
      success(res) {
        if (res.confirm) {
          handleCardSignRequest(curCard)
        } else if (res.cancel) {
          return false
        }
      },
    })
  } else {
    handleCardSignRequest(curCard)
  }
}
function handleCardSignRequest(curCard) {
  http
    .post('Sign/userCardSign', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_name: curCard.name,
      card_id: curCard.card_id,
      end_time: curCard.end_time,
      last_num: curCard.last_num,
      card_user_id: curCard.id,
      card_type_id: curCard.card_type_id,
      status: curCard.status,
      lng: userStore.locationInfo.longitude,
      lat: userStore.locationInfo.latitude,
    })
    .then((res) => {
      handleSuccessPage(res.data.sign_info)
    })
    .catch((err) => {
      handleErrPage(err)
    })
}
function userMarkSign() {
  if (checkedBoxs.value.length == 0) {
    alreadySign.value = false
    uni.showToast({
      title: '请选择课程!',
      icon: 'none',
    })
    return false
  }
  http
    .post('Sign/userMarkSign', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      class_mark_ids: checkedBoxs.value,
      lng: userStore.locationInfo.longitude,
      lat: userStore.locationInfo.latitude,
    })
    .then((res) => {
      handleSuccessPage(res.data.sign_info)
    })
    .catch((err) => {
      handleErrPage(err)
    })
}
function handleSuccessPage(info) {
  uni.setStorageSync('signInfo', info)
  wx.switchTab({
    url: '/pages/index/index',
  })
}
function handleErrPage(err) {
  const errMsg = err.errormsg || err || '未知错误'
  wx.redirectTo({
    url: `/pages/train/signResult?errorcode=${err.errorcode}&errormsg=${errMsg}`,
  })
}

// onMounted(async () => {
//   await userStore.getLocationInfo()
//   showLocationDialog.value = userStore.getShowLocationDialog()// Check if the user has denied location permission
// })
</script>

<style lang="scss" scoped>
.sign-wrap {
  margin: 0 20rpx 160rpx;
  .sign-title {
    line-height: 80rpx;
    font-size: 24rpx;
  }
  .check-list {
    radio,
    checkbox {
      display: none;
    }
    .check-list-label {
      position: relative;
      display: block;
    }
    .checkbox-con {
      position: absolute;
      top: 27rpx;
      right: 32rpx;
      z-index: 9;
    }
    .con-middle {
      top: 50%;
      transform: translateY(-50%);
    }
    .checkbox-circle {
      box-sizing: border-box;
      width: 54rpx;
      height: 54rpx;
      background: #ffffff;
      border: 4rpx solid #e8e8e8;
      border-radius: 50%;
    }
  }
}
</style>
