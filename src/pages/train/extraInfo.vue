<style lang="scss">
$border: 1rpx solid #e7e7e7;

.extra-info {
  min-height: 100vh;
  width: 100%;
  color: #313131;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  .form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 75rpx;
    flex: 1;
    height: 100%;
    font-size: 30rpx;
    background-color: #fff;
    .form-item {
      margin-bottom: 80rpx;
      display: flex;
      flex-direction: column;
      align-items: center;

      .input-box {
        height: 80rpx;
        width: 100%;
        border: $border;
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
      }
      .select {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 20rpx;

        > text {
          margin-right: auto;
          margin-left: auto;
        }
      }
      > text {
        color: #ca2e53;
        margin-bottom: 22rpx;
      }
    }
  }
}
</style>

<template>
  <view class="extra-info">
    <form class="form" @submit="handleSubmit">
      <view class="form-item" v-if="showSex">
        <text>您的性别</text>
        <picker
          name="sex"
          mode="selector"
          @change="sexChange"
          :value="sex - 1"
          style="width: 100%"
          :range="items"
        >
          <view class="input-box select">
            <text>{{ items[sex - 1] }}</text>
            <image
              class="icon"
              src="/static/img/arrow-down.png"
              style="width: 24rpx; height: 13rpx"
            />
          </view>
        </picker>
      </view>
      <view class="form-item">
        <text>您的身高(cm)</text>
        <input name="height" class="input-box" />
      </view>
      <view class="form-item" v-if="showWeight">
        <text>您的体重(kg)</text>
        <input name="weight" class="input-box" />
      </view>
      <view class="form-item" v-if="showBirthday">
        <text>您的出生日期</text>
        <picker
          mode="date"
          name="birthday"
          @change="birthdayChange"
          :value="birthday || '1995-06-15'"
          :end="today"
          style="width: 100%"
        >
          <view class="input-box select">
            <text>{{ birthdayValue }}</text>
            <image
              class="icon"
              src="/static/img/arrow-down.png"
              style="width: 24rpx; height: 13rpx"
            />
          </view>
        </picker>
      </view>
      <button formType="submit" class="long-btn fixed-bottom">提交</button>
    </form>
  </view>
</template>

<script setup lang="ts" name="extraInfo">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin.ts'
import { formatDate } from '@/utils'
import { useUserStore } from '@/store/user'

const { checkLogin } = useLogin()
const userStore = useUserStore()
const type = ref('')
const showBirthday = ref(false)
const birthday = ref('')
const heightRef = ref('')
const weightRef = ref('')
const sex = ref(1)
const items = ref(['男', '女'])
const birthdayValue = ref('请选择')
const today = ref(formatDate(new Date(), 'yyyy-MM-dd'))
const showSex = ref(false)

const user_id = ref('')
const scene = ref('')
const bus_id = ref('')

const showWeight = computed(() => {
  return type.value === 'heartRate'
})

const handleSubmit = (ev) => {
  const { height, weight } = ev.detail.value
  heightRef.value = height || ''
  weightRef.value = weight || ''
  doSubmit()
}

const birthdayChange = (ev) => {
  birthday.value = birthdayValue.value = ev.detail.value
}
const sexChange = (e) => {
  sex.value = +e.detail.value + 1
}

onLoad(async (options) => {
  type.value = options.type || 'heartRate'
  scene.value = options.scene || ''
  user_id.value = userStore.userInfo.user_id
  if (!user_id.value) {
    try {
      const login = await checkLogin()
      user_id.value = login.user_id || ''
      if (!user_id.value)
        return uni.showToast({ title: '未登录!', image: '/static/img/danger.png' })
    } catch (error) {}
  }
  bus_id.value = userStore.userInfo.bus_id
  getUserInfo()
})

const doLink = () => {
  const url = '/mobile/Gymsmart/match'
  const heart_number = uni.getStorageSync('heart_number_cache')
  const tv_number = uni.getStorageSync('tv_number_cache')
  http
    .post(url, { bus_id: bus_id.value, user_id: user_id.value, heart_number, tv_number })
    .then((res) => {
      uni.setStorageSync('heart_number', heart_number)
      uni.setStorageSync('tv_number', tv_number)
      uni.removeStorage({ key: 'heart_number_cache' })
      uni.removeStorage({ key: 'tv_number_cache' })
      setTimeout(() => {
        uni.switchTab({ url: '/pages/index/index' })
      }, 1000)
    })
    .catch((error) => {
      uni.showToast({ title: error.errormsg, image: '/static/img/danger.png' })
      uni.setStorageSync('heart_number', '')
      uni.setStorageSync('tv_number', '')
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    })
}

const doSubmit = () => {
  if (!heightRef.value) {
    return uni.showToast({ title: '请填写身高', image: '/static/img/danger.png' })
  }
  if (!weightRef.value && type.value === 'heartRate') {
    return uni.showToast({ title: '请填写体重', image: '/static/img/danger.png' })
  }
  if (!birthday.value && !showBirthday.value) {
    return uni.showToast({ title: '请填写出生日期', image: '/static/img/danger.png' })
  }
  const url = 'User/updateUserInfo'
  http
    .post(
      url,
      {
        user_id: user_id.value,
        height: heightRef.value,
        weight: weightRef.value,
        birthday: birthday.value,
        sex: sex.value,
        bus_id: bus_id.value,
      }
    )
    .then((res) => {
      uni.showToast({ title: res.errormsg, image: '/static/img/success.png' })
      if (type.value === 'heartRate') {
        doLink()
      } else if (type.value === 'bodyTest') {
        uni.navigateTo({
          url: '/pages/my/result?scene=' + scene.value,
        })
      }
    })
}

const checkData = (data) => {
  if (data.birthday && data.birthday != 0) {
    birthday.value = formatDate(new Date(data.birthday * 1000), 'yyyy-MM-dd')
  } else {
    showBirthday.value = true
  }

  if (data.sex && data.sex != 0) {
    sex.value = data.sex
  } else {
    showSex.value = true
  }
}

const getUserInfo = () => {
  const url = 'User/getUserInfo'
  http.get(url, { user_id: user_id.value, bus_id: bus_id.value }).then((res) => {
    const data = res.data
    checkData(data)
  })
}
</script>
