<template>
  <view class="result-wrap theme-bg">
    <image
      class="result-img"
      mode="widthFix"
      :src="
        action == 'opendoor'
          ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/door-result.png'
          : '/static/img/fail.png'
      "
      alt="失败"
    />
    <view class="result-title">失败了</view>
    <view class="result-msg">{{ errormsg }}</view>
    <button class="normal-btn normal-btn-min" @tap="goback">返回</button>
  </view>
</template>

<script setup lang="ts">
const errorcode = ref(-1)
const errormsg = ref('出错了')
const action = ref('')
const gocenter = ref(false)
function goback() {
  const url = gocenter.value ? '/pages/my/index' : '/pages/index/index'
  uni.switchTab({
    url,
  })
}

onLoad((option) => {
  errorcode.value = +(option.errorcode || -1)
  errormsg.value = option.errormsg || ''
  action.value = option.action || ''
  gocenter.value = option.gocenter === 'true' ? true : false
})
</script>

<style lang="scss" scoped>
.result-wrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding-top: 80rpx;
  text-align: center;
  line-height: 35rpx;
}
.result-img {
  width: 205rpx;
}
.result-title {
  margin: 32rpx auto 38rpx;
  font-size: 30rpx;
  color: #313131;
}
.result-msg {
  font-size: 24rpx;
  color: #313131;
  margin-bottom: 150rpx;
}
</style>
