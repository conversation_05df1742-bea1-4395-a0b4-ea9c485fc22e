<template>
  <NavBar title="运动记录" />
  <view class="training-records-page theme-bg">
    <view class="training-records-container">
      <view class="count-wrap">
        <text class="training-days-row">
          累计训练 <text class="days-num">{{ recordData.sign_days_count }}</text> 天
        </text>
        <view class="line"></view>
        <view class="training-count-box">
          <view class="training-count-item">
            <text class="count-text">
              <text class="count">{{ recordData.sign_num_count }}</text> 次
            </text>
            <text class="type-name">自主训练</text>
          </view>
          <view class="training-count-item">
            <text class="count-text">
              <text class="count">{{ recordData.class_num_count }}</text> 次
            </text>
            <text class="type-name">团操课程</text>
          </view>
          <view class="training-count-item">
            <text class="count-text">
              <text class="count">{{ recordData.private_num_count }}</text> 次
            </text>
            <text class="type-name">教练上课</text>
          </view>
          <view class="training-count-item">
            <text class="count-text">
              <text class="count">{{ recordData.teamclass_num_count }}</text> 次
            </text>
            <text class="type-name">班课上课</text>
          </view>
        </view>
      </view>

      <view class="filter-wrap">
        <picker class="training-type-picker" :value="typeIndex" :range="typeList" @change="handleChangeType">
          <view class="value"> {{ typeList[typeIndex] }}</view>
        </picker>
        <picker
          class="training-type-picker"
          mode="date"
          :value="date"
          :end="today"
          start="2000-01"
          fields="month"
          @change="handleChangeDate"
        >
          <view class="value">{{ date }}</view>
        </picker>
        <MerchantBusPick
          v-model="busId"
          class="training-type-picker"
          :is-filter-user-id-bus="true"
          :is-show-selecte-icon="false"
          :height="38"
          :is-show-icon="false"
        />
      </view>

      <view class="list-wrap">
        <template v-if="recordList.length">
          <template v-for="(item, index) in recordList" :key="index">
            <view v-if="item.id" class="record-item">
              <image class="avatar" :src="item.avatar" mode="scaleToFill" />
              <view>
                <view class="title-row text-overflow">
                  <image v-if="item.class_type !== 1" :src="item.classIconSrc" class="icon icon-mr" />
                  <text class="title">
                    {{ item.recordTitle }}
                  </text>
                </view>
                <text>{{ item.signText }}</text>
              </view>
              <view class="right">
                <text>{{ item.recordDate }}</text>
                <view class="right-bot">
                  <view
                    v-if="item.pt_attend_course_id"
                    class="nav-to-report"
                    hover-class="none"
                    @tap="
                      goUrlPage(
                        `/packagePt/ptConfirm/ptReport?attend_course_id=${item.pt_attend_course_id}&bus_id=${item.bus_id}&service_type=${item.service_type}`
                      )
                    "
                  >
                    查看报告
                  </view>
                  <!-- //comment_status 0:不可评价 1:可评价 2:可追评 3:已评价 4:超时未评价 -->
                  <view
                    v-if="item.comment_status !== 4 && item.comment_status !== 0"
                    class="normal-btn normal-btn-small outer-green"
                    :class="item.user_is_read === 0 ? 'red-dot' : ''"
                    hover-class="none"
                    @tap="
                      goUrlPage(
                        `/pages/class/comment?class_mark_id=${item.class_mark_id}&sign_log_id=${item.id}&is_pt=${
                          item.is_pt || 0
                        }&bus_id=${item.bus_id}`
                      )
                    "
                  >
                    {{ item.comment_status == 2 ? '追评' : item.comment_status == 3 ? '查看评价' : '评价' }}
                  </view>
                  <view v-if="item.comment_status == 4" class="gray-text"> 超时未评价 </view>
                </view>
              </view>
            </view>
            <!-- 英派斯器械训练 -->
            <template v-else-if="item.type_name === '有氧器械训练' || item.type_name === '力量器械训练'">
              <view :class="['training-record', item.more ? 'expand' : 'contract']">
                <view class="training-title">
                  <view class="training-icon">
                    <image
                      class="icon"
                      src="https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-train.png"
                      mode="scaleToFill"
                    />
                    <text>{{item.type_name}}</text>
                  </view>
                  <text class="training-time">{{ item.recordDate }}</text>
                </view>
                <template v-if="item.type_name === '有氧器械训练'">
                  <view v-for="(ele, key) in item.list" :key="index + '-' + key" class="training-container">
                    <view class="training-item">
                      <view class="training-content">
                        <view class="content-header">
                          <view class="content-title">
                            <text>训练项目：</text><text class="content-label">{{ ele.device_name }}</text>
                          </view>
                        </view>
                          <template>
                            <view class="content-footer">
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.distance || 0 }}</text>
                                <text class="footer-unit">距离(km)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.duration || 0 }}</text>
                                <text class="footer-unit">时长(min)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.avgHr || '-' }}</text>
                                <text class="footer-unit">心率(bpm)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.pace || '0.00' }}</text>
                                <text class="footer-unit">配速/km</text>
                              </view>
                            </view>
                          </template>
                      </view>
                    </view>
                  </view>
                </template>
                <template v-if="item.type_name === '力量器械训练'">
                  <view v-for="(ele, key) in item.list" :key="index + '-' + key" class="training-container">
                    <view class="training-item">
                      <view class="training-content">
                        <view class="content-header">
                          <view class="content-title">
                            <text>训练项目：</text><text class="content-label">{{ ele.device_name }}</text> <text class="content-label">{{ ele.duration }}</text>
                          </view>
                        </view>
                          <template>
                            <view class="content-footer">
                              <view class="footer-item" v-for="(v, subKey) in ele.group_motion" :key="index + '-' + subKey">
                                <text class="footer-unit"><text class="circle-num">{{subKey+1}}</text>{{ v[0] }}磅*{{v[1]}}</text>
                              </view>
                            </view>
                          </template>
                      </view>
                    </view>
                  </view>
                </template>
              </view>
            </template>
            <!-- 器材训练 -->
            <!-- 展开 expand 收缩 contract -->
            <template v-else>
              <view :class="['training-record', item.more ? 'expand' : 'contract']">
                <view class="function-icon" @tap="handleChangeStatus(index)">
                  <image src="@/static/img/icon.png" />
                </view>
                <view class="training-title">
                  <view class="training-icon">
                    <image
                      class="icon"
                      src="https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-train.png"
                      mode="scaleToFill"
                    />
                    <text>器械训练</text>
                  </view>
                  <text class="training-time">{{ item.recordDate }}</text>
                </view>
                <view class="training-summary">
                  <view class="exercise">
                    <text class="exercise-number">{{ item.sign_duration }}</text>
                    <text class="exercise-word">运动时长</text>
                  </view>
                  <view class="exercise">
                    <text class="exercise-number">{{ item.kcal }}</text>
                    <text class="exercise-word">卡路里消耗（kcal）</text>
                  </view>
                </view>
                <template v-if="item.list && item.list.length">
                  <view v-for="(ele, key) in item.list" :key="index + '-' + key" class="training-container">
                    <view class="training-item">
                      <view class="training-header">
                        <image src="@/static/img/time.png" />
                        <text class="AM-PM">{{ ele.format_time[1] || '' }}</text>
                        <text>{{ ele.format_time[0] || '' }}</text>
                      </view>
                      <view class="training-content">
                        <view class="content-header">
                          <view class="content-title">
                            <text>训练项目：{{ ele.train_item_name || ele.training_type }}</text>
                            <view v-if="ele.device_type_name" class="content-label">
                              {{ ele.device_type_name }}
                            </view>
                          </view>
                          <navigator
                            v-if="ele.kong_ben_id"
                            class="more"
                            hover-class="none"
                            :url="'/pages/train/trainDetail?mobile=' + item.mobile"
                          >
                            <text>查看更多</text>
                            <image src="@/static/img/right-arrow.png" />
                          </navigator>
                        </view>
                        <template v-if="ele.whochange_id || ele.bique_id || ele.kong_ben_id || ele.ironman_id">
                          <template
                            v-if="
                              ((ele.whochange_id || ele.ironman_id) &&
                                (ele.training_type == '跑步' ||
                                  ele.training_type == '动感单车' ||
                                  ele.training_english_name == 'exercise_bike')) ||
                              ele.bique_id
                            "
                          >
                            <view class="content-footer">
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.training_total || 0 }}</text>
                                <text class="footer-unit">里程(km)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.training_info || 0 }}</text>
                                <text class="footer-unit">时长(min)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.training_quality || 0 }}</text>
                                <text class="footer-unit">速度(km/h)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.kcal || '0.00' }}</text>
                                <text class="footer-unit">消耗(kcal)</text>
                              </view>
                            </view>
                          </template>
                          <template v-else-if="ele.kong_ben_id || ele.ironman_id">
                            <view class="content-footer">
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.use_time || 0 }}</text>
                                <text class="footer-unit">时长(s)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.cycles || 0 }}</text>
                                <text class="footer-unit">周期</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.avg_power || 0 }}</text>
                                <text class="footer-unit">平均功率</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.kcal || '0.00' }}</text>
                                <text class="footer-unit">消耗(kcal)</text>
                              </view>
                            </view>
                          </template>
                          <template v-else>
                            <view class="content-footer">
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.single }}</text>
                                <text class="footer-unit">重量(kg/个)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.training_total || 0 }}</text>
                                <text class="footer-unit">数量(个)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.training_info || 0 }}</text>
                                <text class="footer-unit">组数(组)</text>
                              </view>
                              <view class="footer-item">
                                <text class="footer-number">{{ ele.training_quality || 0 }}</text>
                                <text class="footer-unit">总重(kg)</text>
                              </view>
                            </view>
                          </template>
                        </template>
                      </view>
                    </view>
                  </view>
                </template>
              </view>
            </template>
          </template>
        </template>
        <view v-else class="nodata">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="trainOver">
import http from '@/utils/request'
import { formatDate } from '@/utils/shared'
import { useLogin } from '@/hooks/useLogin'
import NavBar from '@/components/NavBar.vue'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useMerchant } from '@/store/merchant'
import { goUrlPage } from '@/utils'

const busId = ref('')
const { checkLogin } = useLogin()
onShow(() => {
  checkLogin().then(() => {
    getData()
  })
})

const recordData = ref<Record<string, any>>({})
const recordList = ref<Record<string, any>[]>([])
const useMerchantStore = useMerchant()
watch(
  () => useMerchantStore.userInfoBusId,
  (val, oldVal) => {
    if (useMerchantStore.userInfoUserId) {
      getData()
    } else {
      recordList.value = []
    }
  }
)
function getData() {
  const params = {
    bus_id: useMerchantStore.userInfoBusId,
    user_id: useMerchantStore.userInfoUserId,
    date: date.value,
    type: typeIndex.value == 7 ? 12 : typeIndex.value, // 0全部 1自主训练 2团操课 3私教 4泳教
  }
  http.post('Personalcenter/getTrainData', params).then((res) => {
    if (res.errorcode === 0) {
      const { sign_count, list } = res.data
      const { mobile, train_list, yps_list } = res.data.train_data
      if (sign_count) {
        recordData.value = sign_count
      }
      if (Array.isArray(list)) {
        const classIconNames = {
          2: 'tuan',
          3: 'si',
          4: 'yong',
          5: 'train-over-avatar-ban',
          6: 'train-over-avatar-ban',
        }
        const signTypeNames = {
          0: '手动代签',
          1: '扫码签到',
          2: '系统代签',
          3: '指静脉签到',
          4: '智能手环',
          5: '人脸识别',
          7: '订场签到',
          8: '按时计费',
          10: '蜻蜓机签到',
          12: '微信刷掌',
          20: '二维码签到',
          21: '刷卡签到'
        }
        list.forEach((v) => {
          v.avatar =
            v.class_type === 1
              ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-avatar-self.png'
              : v.class_type === 2
              ? 'https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-avatar-tuan.png'
              : v.avatar

          v.recordTitle = `${v.class_type !== 1 ? v.class_name : '自主训练'}`

          v.classIconSrc =
            v.class_type !== 1
              ? `https://imagecdn.rocketbird.cn/minprogram/uni-member/${classIconNames[v.class_type]}.png`
              : null

          const time: string[] = formatDate(new Date(v.create_time * 1000), 'yyyy/MM/dd_hh:mm T').split('_')
          v.signText = `${signTypeNames[v.type]} ${time[1]}`
          v.recordDate = time[0]
        })
      }
      if (Array.isArray(train_list)) {
        train_list.forEach((v) => {
          v.list.forEach((element) => {
            const time: string[] = formatDate(new Date(element.sort_time * 1000), 'yyyy/MM/dd_hh:mm T').split('_')
            const formatTime: any[] = time[1].split(' ')
            element.format_time = formatTime || []
            element.training_info = Math.ceil(Number(element.training_info) / 60)
            if (
              element.training_total &&
              element.training_total.length > 0 &&
              element.training_total.indexOf('个') !== -1
            ) {
              const total = element.training_quality.substring(0, element.training_quality.indexOf('kg'))
              const quality = element.training_total.substring(0, element.training_total.indexOf('个'))
              element.single = quality == '0' || total == '0' ? 0 : (Number(quality) / Number(total)).toFixed(1)
            } else {
              element.single =
                element.training_quality == '0' || element.training_total == '0'
                  ? 0
                  : (Number(element.training_quality) / Number(element.training_total)).toFixed(1)
            }
          })
          const time: string[] = formatDate(new Date(v.date_time * 1000), 'yyyy/MM/dd_hh:mm T').split('_')
          v.recordDate = time[0]
          v.more = true
          v.mobile = mobile
        })
      }
      if (Array.isArray(yps_list)) {
        yps_list.forEach((v) => {
          const time: string[] = formatDate(new Date(v.date_time * 1000), 'yyyy/MM/dd_hh:mm T').split('_')
          v.recordDate = time[0]
        })
      }
      const formatTrainList = train_list.sort(dateData('recordDate', false))
      const formatYpsList = yps_list.sort(dateData('recordDate', false))
      const formatData = [...formatYpsList,...formatTrainList, ...list]
      const resultData = formatData.sort(dateData('recordDate', false))
      recordList.value = resultData
    }
  })
}
/**
 * 排序
 * @param  {string} property 排序依据属性名称(key)
 * @param  {boolean} bol true升序 false降序
 * @returns {any} 根据传入返回
 */
const dateData = (property, bol) => {
  return function (a, b) {
    const value1 = a[property]
    const value2 = b[property]
    if (bol) {
      // 升序
      return Date.parse(value1) - Date.parse(value2)
    }
    // 降序
    return Date.parse(value2) - Date.parse(value1)
  }
}

const typeIndex = ref(0)
const typeList = ref(['所有运动', '自主训练', '团操课程', '私教课', '泳教课', '私教班', '泳教班', '微信刷掌'])
const handleChangeType = (e) => {
  typeIndex.value = +e.detail.value
  getData()
}

const date = ref(formatDate(new Date(), 'yyyy-MM'))
const today = date.value
const handleChangeDate = (e) => {
  date.value = e.detail.value
  getData()
}

// goCommonRepot(id) {
//   if (!id || id.active) return false;
//   wx.navigateTo({ url: `/packagePt/ptConfirm/ptReport?attend_id=${id}&type=0&bus_id=${this.bus_id}` })
// },

const handleChangeStatus = (index) => {
  const list = JSON.parse(JSON.stringify(recordList.value))
  list[index].more = !list[index].more
  recordList.value = list
}
</script>

<style lang="scss" scoped>
.training-records-page {
  position: relative;
  height: 100%;
  // background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-page-bg-2.png')
  //   439rpx -50rpx/386rpx 562rpx no-repeat;
  font-size: 24rpx;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 210rpx;
    height: 229rpx;
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-page-bg-1.png') right bottom/349rpx
      349rpx no-repeat;
  }
  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 311rpx;
    height: 484rpx;
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-page-bg-2.png') left bottom/386rpx
      562rpx no-repeat;
  }
  .training-records-container {
    // overflow-y: auto;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 160rpx 20rpx 30rpx;
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
  }
}
.count-wrap {
  margin: 0 10rpx;
  .training-days-row {
    font-weight: bold;
    font-size: 36rpx;
    .days-num {
      font-size: 48rpx;
      color: $theme-text-color-other;
    }
  }
  .line {
    margin-top: 24rpx;
    border-radius: 4rpx;
    width: 63rpx;
    height: 8rpx;
    background: $theme-text-color-other;
  }
  .training-count-box {
    display: flex;
    margin-top: 55rpx;
    .training-count-item {
      display: flex;
      flex-direction: column;
      margin-right: 46rpx;
      &:last-child {
        margin-right: 0;
      }
    }
    .count-text {
      margin-bottom: 11rpx;
      font-weight: bold;
      .count {
        font-size: 40rpx;
      }
    }
  }
}
.filter-wrap {
  display: flex;
  align-items: center;
  .training-type-picker {
    position: relative;
    margin-top: 40rpx;
    margin-right: 50rpx;
    padding-left: 20rpx;
    min-width: 156rpx;
    height: 40rpx;
    line-height: 38rpx;
    background: rgba(255, 116, 39, 0.1);
    border-radius: 20rpx;

    &:last-child {
      margin-right: 0;
    }
    &::after {
      position: absolute;
      right: 15rpx;
      top: 50%;
      margin-top: -5rpx;
      border: 8rpx solid transparent;
      border-top: 8rpx solid $theme-text-color-other;
      width: 0;
      height: 0;
      content: '';
    }
  }
}
.list-wrap {
  overflow-y: auto;
  margin-top: 24rpx;
  padding-bottom: 24rpx;
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 6rpx;
  .nodata {
    padding-bottom: 50rpx;
  }
  .record-item {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    height: 130rpx;
  }
  .record-item + .record-item {
    border-top: 2rpx solid #f6f6f8;
  }
  .avatar {
    margin-right: 16rpx;
    border-radius: 50%;
    width: 80rpx;
    height: 80rpx;
  }
  .title-row {
    max-width: 420rpx;
    font-weight: bold;
    font-size: 30rpx;
    margin-bottom: 10rpx;
    .title {
      vertical-align: middle;
    }
  }
  .right {
    margin-left: auto;
    margin-top: 8rpx;
    min-height: 70rpx;
    text-align: right;
    .nav-to-report {
      padding: 7rpx 18rpx;
      color: $theme-text-color-other;
    }
  }
  .right-bot {
    display: flex;
    align-items: center;
  }
  .training-record {
    padding: 20rpx;
    box-sizing: border-box;
    border-top: 2rpx solid #f6f6f8;
    display: flex;
    flex-direction: column;
    position: relative;
    .function-icon {
      width: 64rpx;
      height: 32rpx;
      // background: #00f;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: 16rpx;
      left: calc(50% - 32rpx);
      image {
        width: 16rpx;
        height: 8rpx;
      }
    }
    .training-title {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .training-icon {
        .icon {
          width: 80rpx;
          height: 80rpx;
          margin-right: 18rpx;
        }
        text {
          font-size: 30rpx;
          color: #000000;
          font-weight: bold;
          font-family: PingFang SC;
        }
      }
      .training-time {
        font-size: 24rpx;
        color: #000000;
        font-weight: 400;
        font-family: PingFang SC;
      }
    }
    .training-summary {
      min-height: 130rpx;
      height: 130rpx;
      padding: 0 80rpx;
      margin-top: 24rpx;
      border-radius: 10rpx;
      background: #f6f6f8;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .exercise {
        display: flex;
        flex-direction: column;
        align-items: center;
        .exercise-number {
          font-size: 40rpx;
          font-weight: 500;
          color: #313131;
          line-height: 30rpx;
          font-family: Roboto;
        }
        .exercise-word {
          margin-top: 22rpx;
          font-size: 24rpx;
          font-weight: 400;
          color: #000000;
          line-height: 30rpx;
          font-family: PingFang SC;
        }
      }
    }
    .training-container {
      .training-item {
        margin-top: 20rpx;
        .training-header {
          display: flex;
          align-items: center;
          image {
            width: 22rpx;
            height: 22rpx;
          }
          text {
            font-size: 24rpx;
            color: #ff7427;
            font-weight: bold;
            line-height: 30rpx;
            font-family: PingFang SC;
          }
          .AM-PM {
            margin: 0 12rpx;
          }
        }
        .training-content {
          margin-top: 16rpx;
          border-radius: 10rpx;
          border: 2rpx solid #f6f6f8;
          .content-header {
            height: 76rpx;
            box-sizing: border-box;
            padding: 0 22rpx 0 24rpx;
            border-bottom: 2rpx solid #f6f6f8;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .content-title {
              display: flex;
              flex-direction: row;
              align-items: center;
              text {
                font-size: 24rpx;
                color: #000000;
                font-weight: bold;
                line-height: 30rpx;
                font-family: PingFang SC;
              }
              .content-label {
                height: 36rpx;
                padding: 3rpx 9px;
                margin-left: 8rpx;
                border-radius: 18rpx;
                background: #fff1e9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24rpx;
                font-weight: 400;
                color: #ff7427;
                font-family: PingFang SC;
              }
            }
            .more {
              display: flex;
              align-items: center;
              justify-content: center;
              text {
                font-size: 24rpx;
                font-weight: 400;
                color: #000000;
                line-height: 30rpx;
                font-family: PingFang SC;
              }
              image {
                height: 20rpx;
                width: 12rpx;
                margin-left: 12rpx;
              }
            }
          }
          .content-footer {
            height: 115rpx;
            padding-top: 5rpx;
            box-sizing: border-box;
            display: flex;
            flex-direction: row;
            align-items: center;
            flex-wrap: wrap;
            .footer-item {
              flex: 1 0 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 25%;
              .footer-number {
                font-size: 30rpx;
                font-weight: 500;
                color: #313131;
                line-height: 36rpx;
                font-family: Roboto;
              }
              .footer-unit {
                margin-top: 12rpx;
                font-size: 20rpx;
                font-weight: 400;
                color: #000000;
                line-height: 36rpx;
                font-family: PingFang SC;
              }
            }
            .circle-num {
              display: inline-block;
              width: 24rpx;
              height: 24rpx;
              text-align: center;
              line-height: 24rpx;
              border: 1px solid #000000;
              border-radius: 50%;
              margin-right: 4rpx;
            }
          }
        }
      }
    }
  }
  .expand {
    height: 276rpx;
    overflow: hidden;
  }
  .contract {
    height: auto;
    .function-icon {
      bottom: -8rpx;
      transform: rotate(180deg);
    }
  }
}
.normal-btn-small {
  width: 142rpx;
  height: 52rpx;
  line-height: 52rpx;
  border-radius: 25rpx;
}
.red-dot {
  position: relative;
  &::after {
    content: ' ';
    background: red;
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    position: absolute;
    right: 8rpx;
    top: -6rpx;
  }
}
.gray-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #7d7d7d;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.box {
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: space-between;
  padding-left: 24rpx;
  padding-right: 42rpx;
  box-sizing: border-box;
  height: 150rpx;
  position: relative;
  .course-info {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .course-time {
    margin-left: 6rpx;
    image {
      height: 20rpx;
      width: 12rpx;
      margin-left: 12rpx;
    }
  }
  .course-avatar {
    height: 66rpx;
    width: 66rpx;
    border-radius: 50%;
    margin-right: 36rpx;
    border: 2rpx solid #f5f7f9;
  }
  .course-text {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    align-items: flex-start;
    .course-name {
      font-weight: bold;
      color: #313131;
      padding-bottom: 22rpx;
    }
  }
  .train-duration {
    color: #313131;
    font-weight: bold;
  }
}
</style>
