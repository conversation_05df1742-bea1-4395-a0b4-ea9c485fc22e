<template>
  <view class="signclass-item theme-bg">
    <view class="left">
      <image
        class="coach-avatar"
        src="https://imagecdn.rocketbird.cn/minprogram/uni-member/train-over-avatar-tuan.png"
        mode="aspectFill"
      />
      <view class="coach-info">
        <view class="name">
          <text style="maxwidth: 320rpx">{{ item.space_name }}</text>
        </view>
        <view class="item coach">
          <text class="time-bg">{{ item.time_desc }}</text>
        </view>
        <view class="item">
          <ThemeIcon class="icon-mr" type="t-icon-jiaoshi1" />
          <text class="room-name">{{ item.space_type_name || '无' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  item: Object,
})
</script>

<style lang="scss" scoped>
.signclass-item {
  box-sizing: border-box;
  width: 690rpx;
  height: 190rpx;
  border: 1rpx solid var(--THEME-COLOR);
  border-radius: 20rpx;
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin: 0 auto 30rpx;
  .lef-tag {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 20rpx 0 20rpx 0;
    width: 147rpx;
    height: 30rpx;
    line-height: 30rpx;
    text-align: center;
    background: rgba(var(--THEME-RGB), 0.4);
    font-size: 20rpx;
  }
  .left {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .time {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      font-weight: bold;
      margin-right: 66rpx;
      .time-line {
        width: 2rpx;
        height: 16rpx;
        background-color: var(--THEME-COLOR);
        margin: 6rpx 0;
      }
    }
    .time-bg {
      font-size: 23rpx;
      margin-right: 10rpx;
      padding: 0 10rpx;
      height: 32rpx;
      line-height: 32rpx;
      text-align: center;
      background: var(--THEME-COLOR);
      border-radius: 15rpx;
      font-weight: bold;
    }
  }

  .coach-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: 26rpx;
  }
  .coach-info {
    display: flex;
    flex: 1;
    justify-content: center;
    flex-direction: column;
    overflow: hidden;
    .name {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      margin-right: 28rpx;
      margin-bottom: 15rpx;
      font-weight: bold;
      > text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.1;
      }
    }
    .item {
      display: flex;
      align-items: center;
    }
    .coach {
      margin-bottom: 16rpx;
    }
  }
}
</style>
