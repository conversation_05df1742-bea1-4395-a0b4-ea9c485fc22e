<template>
  <view class="card-info" :class="{excard:item.experience_card ==1}">
    <view class="card-top"><text class="card-name">{{ item.name }}</text></view>
    <view class="status" v-if="item.status==2||item.status==3">{{item.status==2?'请假中':'未激活'}}</view>
    <view class="card-top-row">
      <text class="card-type">{{ item.experience_card ==1 ?'体验卡':cardTypeIds[item.card_type_id] }}</text>
    </view>
    <view class="card-des">
      <view class="card-num">{{item.card_sn ? 'NO. ' + item.card_sn : ''}}</view>
      <view class="overplus">剩余 <text>{{ item.last_num }}</text>{{item.card_type_id==1?'天':item.card_type_id==2?'次':item.card_type_id==3?'元':'节'}}</view>
    </view>
    <view class="card-bottom-row">
      <view class="left-btn">
      </view>
      <view>{{item.end_date}}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  item: Object
})
const cardTypeIds = reactive({
  '1': '期限卡',
  '2': '次数卡',
  '3': '储值卡',
  '4': '私教卡',
  '5': '泳教卡',
})
</script>

<style lang="scss" scoped>
.card-info {
  margin-bottom: 30rpx;
  .status {
    top: 120rpx;
    right: 40rpx;
  }
}
</style>
