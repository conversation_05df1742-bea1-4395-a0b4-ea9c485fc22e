<template>
  <view>
    <web-view v-if="canIUse" :src="src"></web-view>
    <view wx:else> 请升级微信版本！ </view>
  </view>
</template>

<script setup lang="ts" name="resultDetail">
import env from '@/config/env'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const canIUse = ref(false)
const src = ref('')

onLoad((options) => {
  canIUse.value = uni.canIUse('web-view')
  const url = 'https://www.kongbenfit.com/h5/#/'
  const param = `mobile=${options.mobile}`
  src.value = url + '?' + param
})
</script>
