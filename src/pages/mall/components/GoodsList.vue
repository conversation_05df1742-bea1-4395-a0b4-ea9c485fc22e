<template>
  <view class="goods-list">
    <z-paging
      ref="openPaging"
      v-model="goodsList"
      :show-loading-more-no-more-view="goodsList.length > 10 ? true : false"
      :fixed="false"
      :refresher-enabled="false"
      empty-view-text="暂无商品数据"
      :empty-view-img-style="{ width: '103rpx', height: '144rpx' }"
      empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png"
    >
      <view class="goods-item-wrap">
        <template v-for="item in goodsList" :key="item.id">
          <GoodsItem :item="item" :type="type" :bus-id="busId" />
        </template>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts" name="OpenClassList">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import GoodsItem from './GoodsItem.vue'
const props = defineProps({
  type: {
    type: Number,
    default: 2,
  },
  busId: {
    type: String,
    default: '',
  },
})
const openPaging = ref()

const userStore = useUserStore()
const goodsList = ref([])
const getClassInfo = () => {
  http
    .get('/Pointmall/getPointGoodsList', {
      bus_id: props.busId || userStore.userInfoBusId,
      type: props.type,
    })
    .then(async (res) => {
      if (!openPaging.value) {
        await nextTick()
      }
      openPaging.value.reload()
      openPaging.value.setLocalPaging(res.data.list)
    })
    .catch(async (res) => {
      if (!openPaging.value) {
        await nextTick()
      }
      openPaging.value.setLocalPaging([], false)
    })
}
watchEffect(() => {
  const busId = props.busId  || userStore.userInfoBusId
  if (busId) {
    getClassInfo()
  }
})

</script>
<style lang="scss" scoped>
.goods-list {
  height: 100%;
}
.goods-item-wrap {
  display: flex;
  justify-content: space-between;
  margin: 40rpx 40rpx 96rpx;
  flex-wrap: wrap;
}
</style>
