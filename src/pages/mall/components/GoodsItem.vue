<template>
  <view
    hover-class="none"
    class="goods-item"
    @tap="goUrlPage(`/pages/mall/detail?id=${item.id}&type=${type}&bus_id=${busId || ''}`)"
  >
    <image
      :src="item.goods_image || 'https://imagecdn.rocketbird.cn/minprogram/member/image/point-store-nodata.png'"
      mode="aspectFill"
    />
    <view class="name">{{ item.goods_name }}</view>
    <view class="volume">库存 {{ item.last_volume }}</view>
    <view v-if="type !== 2" class="price"> ￥{{ item.commodity_price }} </view>
    <view v-else class="price"> {{ item.point }}<text style="color: #898989">积分</text> </view>
  </view>
</template>

<script setup lang="ts" name="GoodsItem">
import { goUrlPage } from '@/utils'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  type: {
    type: Number,
    default: 2,
  },
  busId: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['click-item'])
const handleClick = () => {
  emits('click-item', props.item)
}
</script>

<style lang="scss" scoped>
.goods-item {
  width: 318rpx;
  min-height: 490rpx;
  box-shadow: 0px 2rpx 6rpx 0px rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
  margin-bottom: 30rpx;

  image {
    width: 100%;
    height: 318rpx;
    border-radius: 10rpx 0 10rpx 0;
  }
  .name {
    font-size: 30rpx;
    margin-left: 22rpx;
    margin-top: 15rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .volume {
    font-size: 22rpx;
    margin-left: 22rpx;
    color: #898989;
    margin-top: 10rpx;
  }

  .price {
    font-size: 24rpx;
    margin-left: 22rpx;
    color: $theme-text-color-other;
    margin-top: 15rpx;
  }
}
</style>
