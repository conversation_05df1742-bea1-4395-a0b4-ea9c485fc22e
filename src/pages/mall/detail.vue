<template>
  <view class="goods-box">
    <view class="box-head">
      <image class="goods-img" :src="goodsInfo.goods_image ||
        'https://imagecdn.rocketbird.cn/minprogram/member/image/point-store-nodata.png'
        " mode="heightFix" />
    </view>
    <view class="box-body">
      <view class="name">{{ goodsInfo.goods_name }}</view>
      <view class="price-row">
        <text v-if="goodsInfo.id" class="price-num">{{
          type == 1 ? goodsInfo.commodity_price : goodsInfo.point + '积分'
        }}</text>
        <text>库存 {{ goodsInfo.last_volume }}</text>
      </view>
      <view v-if="
        type == 2 &&
        (goodsInfo.barter_limit_rule_text ||
          goodsInfo.goods_type == 1 ||
          goodsInfo.goods_type == 2)
      " class="limit">
        <text>{{ goodsInfo.barter_limit_rule_text }}</text>
        <view v-if="goodsInfo.goods_type == 1 || goodsInfo.goods_type == 2">
          <text>本人使用</text>
          <image class="limit-image"
            :src="'/static/img/' + (goodsInfo.use_limit_self ? 'success' : 'danger') + '.png'" />
          <text>转赠他人</text>
          <image :src="'/static/img/' + (goodsInfo.use_limit_others ? 'success' : 'danger') + '.png'" />
        </view>
      </view>
      <view v-if="type == 2" class="desc">商品说明：{{ goodsInfo.remark || '暂无说明' }} </view>

      <!-- payment box -->
      <template v-if="type == 1">
        <view class="course">
          <view class="line-item">
            <view class="label">数量</view>
            <view class="value">
              <uni-number-box v-model="count" :min="1" :max="goodsInfo.last_volume" />
            </view>
          </view>
          <view class="line-item">
            <view class="label">原价合计</view>
            <view class="value">{{ sumPrice }}</view>
          </view>
          <view class="line-item" v-if="Number(discount) < 0 && payMethodIdx === '1'">
            <view class="label">储值卡折扣</view>
            <view class="value">
              <text class="type">{{ discountCardName }}</text>
              <text class="amount">{{ discount }}</text>
            </view>
          </view>
        </view>
        <PayTypeSelect :pay-type-list="payMethodList" :pay-type-index="payMethodIdx" :card-list="cardList"
          :card-index="cardIdx"
          :value-card-text="`(余额${selectedCard?.balance}${needPrice > 0 ? '，还需' + needPrice.toFixed(2) : ''})`"
          @change-type="handlePayMethodChange" @change-card="handleCardChange" />
        <view class="course">
          <view class="line-item">
            <view class="label">备注</view>
            <view class="value">
              <textarea v-model="remarks" maxlength="150" class="item-input" auto-height
                placeholder="如有需要请填写（150个字以内）" />
            </view>
          </view>
        </view>
      </template>
    </view>
    <view v-if="diffPrice > 0 && type == 2" class="fixed-protocol-col">
      <view>
        您还差
        <text class="num">{{ diffPrice }}</text>
        积分可以兑换
      </view>
      <navigator hover-class="none" class="router-link" url="/pages/my/pointTask">做任务赚积分</navigator>
    </view>
    <template v-if="goodsInfo.id">
      <view v-if="type == 1 || goodsInfo.goods_type == 5" class="fixed-bottom-wrap theme-bg">
        <button v-if="goodsInfo.goods_type == 5 || !isSale" class="normal-btn" disabled>
          请联系工作人员进行{{ type == 1 ? '购买' : '兑换' }}
        </button>
        <view v-if="type == 1 && isSale" class="price">
          <text style="font-size: 28rpx">¥{{ killBill }}</text>
          <text v-if="discount" style="margin-left: 20rpx">储值卡已抵扣 {{ killValueCard }} 元</text>
        </view>
        <button v-if="type == 1 && isSale" class="normal-btn" style="margin-left: 20rpx;width: 300rpx" @tap="submitPayment">立即支付</button>
      </view>
      <view v-else-if="type == 2" class="fixed-bottom-wrap theme-bg">
        <button class="normal-btn" :disabled="diffPrice > 0" @tap="goodsExchange">兑换</button>
      </view>
    </template>
    <!-- <view if="type == 1" class="fixed-bottom-wrap theme-bg">
      <view class="price">
        <text style="font-size: 28rpx">¥{{ killBill }}</text>
        <text v-if="discount" style="margin-left: 20rpx">储值卡已抵扣 {{ killValueCard }} 元</text>
      </view>
      <button class="normal-btn" style="margin-left: 20rpx;width: 300rpx" @tap="submitPayment">立即支付</button>
    </view> -->
  </view>
</template>

<script setup lang="ts" name="class">
import { goUrlPage } from '@/utils'
import PayTypeSelect from '@/components/PayTypeSelect.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { usePoint } from '@/hooks/usePoint'
import _ from 'lodash'
const userStore = useUserStore()
const id = ref('')
const type = ref(2) // 商品类型 1前台商品 2积分商品
const { currentPoint, getUserPoint } = usePoint()
const goodsInfo = reactive<any>({
  id: '',
  goods_type: 1,
  use_limit: '' as any,
})
let itemPrice = 0;
const diffPrice = computed(() => {
  return type.value == 2 ? goodsInfo.point - currentPoint.value : 0
})
onLoad((options) => {
  id.value = options.id
  type.value = options.type
})
onShow((options) => {
  getGoodsDetail()
  getUserPoint()
})
const getGoodsDetail = () => {
  const url = type.value == 1 ? '/Good/getGoodsInfo' : '/Pointmall/getPointGoodsInfo'
  const method = type.value == 1 ? 'post' : 'get'
  http[method](url, {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    id: id.value,
    type: type.value,
  })
    .then((res) => {
      const info = type.value == 1 ? res.data.goods_info : res.data.info
      let barter_limit_rule_text = ''
      // type 1前台商品 2积分商品   barter_limit 0不限制 1限制, barter_limit_rule限制规则
      if (type.value == 2 && info.barter_limit == 1 && info.barter_limit_rule) {
        const types = {
          day: '天',
          week: '周',
          month: '月',
          season: '季',
          year: '年',
        }
        info.barter_limit_rule = JSON.parse(info.barter_limit_rule.replace(/&quot;/g, '"'))
        const rule = info.barter_limit_rule
        const limit = rule.type == 1 ? '人累计' : types[Object.keys(rule.rule)[0]]
        const num = rule.type == 1 ? rule.rule : Object.values(rule.rule)[0]
        barter_limit_rule_text = `每${limit}可兑换 ${num} 件`
        console.log(barter_limit_rule_text)
      }
      Object.assign(goodsInfo, {
        ...info,
        goods_image: info.goods_image || info.goods_img,
        commodity_price: info.commodity_price
          ? '￥ ' + Number(info.commodity_price).toFixed(2)
          : '',
        use_limit_self: (info.use_limit || '').includes(1),
        use_limit_others: (info.use_limit || '').includes(2),
        barter_limit_rule_text,
      })

      itemPrice = Number(info.commodity_price || 0)
      if (type.value == 1) {
        isSale.value = res.data?.member_seting?.is_goods_sale == 1
        canUseValueCard.value = res.data?.member_seting?.is_goods_card_discount == 1

        payMethodList.value = res.data.pay_type_arr
        cardList.value = res.data.card_list
        price.value = Number(info.commodity_price || 0)

        payMethodIdx.value = '0'
        cardIdx.value = 0
        const index = payMethodList.value.findIndex((item: any) => item.pay_type === 8)
        if (index !== -1) {
          cardIdx.value = cardList.value.findIndex((item: any) => item.checked)

          // discount
          const card = cardList.value[cardIdx.value]
          count.value = 1
          if (canUseValueCard.value) {
            discountRate.value = Number(card.sign_discount) / 10
          } else {
            discountRate.value = 1
          }
          discountCardName.value = card.name

          if (cardIdx.value !== -1) {
            payMethodIdx.value = String(index)
          } else {
            payMethodIdx.value = '0'
            cardIdx.value = 0
          }
        }
      }
    })
}

const doGoodsExchange = _.throttle(
  () => {
    http
      .post('/Pointmall/goodsExchange', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        goods_id: id.value,
      })
      .then((res) => {
        uni.showToast({
          title: res.errormsg,
          success: () => {
            setTimeout(() => {
              // goods_type 1体验卡 2体验课 3折扣券 4金币暂定 5其他
              switch (+goodsInfo.goods_type) {
                case 1:
                case 2:
                  uni.navigateTo({
                    url: '/pages/my/card',
                  })
                  break
                case 3:
                  uni.navigateTo({
                    url: '/pages/my/coupon',
                  })
                  break
                default:
              }
            }, 1500)
          },
        })
      })
  },
  2000,
  true
)
// 处理商品兑换确认
function goodsExchange() {
  const { use_limit } = goodsInfo // 1自己可用 2转赠他人
  uni.showModal({
    title: '兑换确认',
    confirmText: '兑换',
    content: `${!use_limit.includes(1) && use_limit.includes(2) ? '此卡不能自用，只能转赠他人使用，' : ''
      }确认兑换吗？`,
    success: (res) => {
      if (res.confirm) {
        doGoodsExchange()
      }
    },
  })
}

// payment params
const payMethodList = ref([])
const payMethodIdx = ref('0')
const cardList = ref<any[]>([])
const cardIdx = ref(-1)
const price = ref(0)
const count = ref(1)
const discountCardName = ref('')
const discountRate = ref(1)
const remarks = ref('')
const isSale = ref(false) // 是否是售卖商品
const canUseValueCard = ref(false) // 是否可以使用储值卡

const sumPrice = computed(() => {
  return (price.value * count.value).toFixed(2)
})
const discount = computed(() => {
  const realPrice = parseFloat((price.value * count.value * discountRate.value).toFixed(2))
  return (realPrice - price.value * count.value).toFixed(2)
})
const selectedCard: any = computed(() => {
  return cardList.value[cardIdx.value]
})
const needPrice = computed(() => {
  const total = Number(selectedCard.value ? selectedCard.value.balance : 0)
  const pay = Number(sumPrice.value) + Number(discount.value)
  return parseFloat((pay - total).toFixed(2))
})
const killBill = computed(() => {
  if (payMethodIdx.value === '0') {
    const bill = Number(sumPrice.value)
    return bill.toFixed(2)
  } else {
    const bill = Number(sumPrice.value) + Number(discount.value)
    const cardAmount = Number(selectedCard.value?.balance)
    if (bill > cardAmount) {
      return (bill - cardAmount).toFixed(2)
    } else {
      return 0
    }
  }
})
const killValueCard = computed(() => {
  if (payMethodIdx.value === '0') {
    return 0
  } else {
    const bill = Number(sumPrice.value) + Number(discount.value)
    const cardAmount = Number(selectedCard.value?.balance)

    if (bill > cardAmount) {
      return cardAmount.toFixed(2)
    } else {
      return bill.toFixed(2)
    }
  }
})

function handlePayMethodChange(data) {
  payMethodIdx.value = data.index

  // getPayInfo(data.payType === 8)
}

function handleCardChange(data) {
  cardIdx.value = data.index
  if (data.choseCard) {
    // getPayInfo(true, data.choseCard.card_user_id, data.choseCard.card_id)
    if (canUseValueCard.value) {
      discountRate.value = Number(data.choseCard.sign_discount) / 10
    } else {
      discountRate.value = 1
    }
    discountCardName.value = data.choseCard.name
  }
}

function submitPayment() {
  http.post('/Good/goodPayment', {
    id: id.value,
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    commodity_id: goodsInfo.id,
    purchase_count: count.value,
    remarks: remarks.value,
    amount: killBill.value,
    pay_type: payMethodList.value[+payMethodIdx.value].pay_type,
    balance: selectedCard.value?.balance || '',
    card_user_id: selectedCard.value?.card_user_id || '',
    card_id: selectedCard.value?.card_id || '',
    commodity_price: itemPrice,
    sign_discount: selectedCard.value?.sign_discount || '',
    loading: true,
  }).then((res) => {
    if (res.data.info) {
      pay(res.data.info)
    } else {
      uni.showToast({
        icon: 'success',
        title: res.data.errormsg || '购买成功',
      })
      setTimeout(() => {
        goUrlPage(`/pages/my/ticketDetail-new?sanLogId=${res.data.consumption_log_id}`)
      }, 1000)
    }
  })
}

function pay(info) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'alipay',
    orderInfo: info.orderInfo || '',
    success: (res) => {
      uni.showToast({
        icon: 'success',
        title: '购买成功',
      })
      console.log(res)
      setTimeout(() => {
        goUrlPage(`/pages/my/ticketDetail-new?sanLogId=${info.consumption_log_id || res.data.consumption_log_id}`)
      }, 1000)
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      uni.hideLoading()
      // 如果是储值卡组合支付,并且有余额，立即返还扣除的储值卡余额，否则会5分钟后自动返还
      const payTypeState = payMethodList.value[payMethodIdx.value]
      const card = cardList.value[cardIdx.value]
      // if (payTypeState.pay_type === 8 && +payTypeState.balance !== 0) {
      const params = {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        card_user_id: card?.card_user_id || '0',
        business_id: info.order_sn, // 业务id
        business_type: '9',
      }
      http.post('Booking/storedCardChangeRevoke', params).then((res) => {
        if (res.errorcode != 0) {
          uni.showToast({ title: res.data.errormsg, icon: 'none' })
          getGoodsDetail()
        }
      })
      // }
    },
  })
}
</script>

<style lang="scss" scoped>
.goods-box {
  overflow-y: auto;
  padding-bottom: 152rpx;
  // height: 100vh;
  background-color: #fff;
  box-sizing: border-box;

  .box-head {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 70rpx;
    padding-bottom: 90rpx;
    background-color: #f5f7f9;

    .goods-img {
      height: 340rpx;
    }
  }

  .box-body {
    background-color: #fff;

    .name {
      margin-top: 46rpx;
      padding: 0 40rpx;
      font-size: 36rpx;
      line-height: 80rpx;
      font-weight: bold;
    }

    .price-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40rpx;
      height: 112rpx;
      font-size: 24rpx;

      .price-num {
        font-size: 30rpx;
        font-weight: bold;
        color: $theme-text-color-other;
      }
    }

    .limit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 20rpx;
      padding: 0 20rpx;
      height: 70rpx;
      font-size: 24rpx;
      background-color: #f5f7f9;
      border-radius: 10rpx;

      image {
        margin-left: 9rpx;
        width: 20rpx;
        height: 20rpx;
      }

      .limit-image {
        margin-right: 28rpx;
      }
    }

    .desc {
      margin: 22rpx 38rpx;
      padding-bottom: 40rpx;
      line-height: 48rpx;
      font-size: 24rpx;
      color: #898989;
    }
  }

  .fixed-protocol-col {
    justify-content: space-between;

    .num {
      color: $theme-text-color-other;
    }

    .router-link {
      padding-right: 30rpx;
      font-weight: bold;
      color: $theme-text-color-other;
    }
  }

  .box-foot {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .button-wrap {
      min-height: 90rpx;
      text-align: center;

      .not-exchange-text,
      .exchange-btn {
        height: 90rpx;
        line-height: 90rpx;
        font-size: 30rpx;
        font-weight: bold;
        border-radius: 0;
      }

      .not-exchange-text {
        color: #898989;
        background-color: #eee;
      }

      .exchange-btn {
        color: #ffffff;
        background-color: #ca2e53;
      }
    }
  }
}

.price {
  font-size: 24rpx;
  font-weight: bold;
  color: orange;
}

.course {
  padding: 30rpx 50rpx;

  .line-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60rpx;

    .label {
      font-size: 26rpx;
      color: $theme-text-color-grey;
    }

    .value {
      font-size: 30rpx;
      color: #1b1b1b;

      .type {
        font-size: 24rpx;
        font-weight: bold;
        color: #dddddd;
        margin-right: 10rpx;
        border: 1rpx solid #eeeeee;
        padding: 0 10rpx;
        border-radius: 8rpx;
      }

      .amount {
        color: $theme-text-color-other;
      }
    }
  }
}
</style>
