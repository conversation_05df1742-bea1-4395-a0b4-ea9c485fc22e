<template>
  <view class="result-wrap theme-bg">
    <image class="result-img" mode="widthFix" src="/static/img/success.png" alt="失败" />
    <view class="result-title">发布成功</view>
    <view class="result-msg">请留意组团结果通知</view>
    <view v-if="inviteType === 1" class="result-des">{{ tips }}</view>
    <button v-if="inviteType === 1" class="normal-btn" @tap="handlePayClick">订场支付</button>
    <view class="result-link" @tap="goDetail">查看活动详情</view>
  </view>
</template>

<script setup lang="ts">
const inviteType = ref(1)
const sportsMarkId = ref()
const sportsMmarkOrderId = ref()
const tips = ref()
onLoad((options) => {
  inviteType.value = +(options.inviteType || 0)
  sportsMarkId.value = options.sports_mark_id
  tips.value = options.tips
  sportsMmarkOrderId.value = options.sports_mark_order_id
})
onUnload(() => {
  uni.reLaunch({
    url: '/pages/stadium/aboutBall',
  })
})
function handlePayClick() {
  if (inviteType.value === 1) {
    uni.navigateTo({
      url: `/pages/stadium/buySpace?sports_mark_id=${sportsMarkId.value}&sports_mark_order_id=${sportsMmarkOrderId.value}`,
    })
  } else {
    uni.navigateTo({
      url: `/pages/stadium/buyTicket?sports_mark_id=${sportsMarkId.value}&sports_mark_order_id=${sportsMmarkOrderId.value}`,
    })
  }
}
function goDetail() {
  uni.navigateTo({
    url: `/pages/stadium/ballDetail?sports_mark_id=${sportsMarkId.value}&sports_mark_order_id=${sportsMmarkOrderId.value}`,
  })
}

onLoad((option) => {
  inviteType.value = +(option.inviteType || 1)
})
</script>

<style lang="scss" scoped>
.result-wrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 100rpx 70rpx;
  text-align: center;
  line-height: 35rpx;
  color: #03080e;
}
.result-img {
  width: 178rpx;
}
.result-title {
  margin: 32rpx auto 38rpx;
  font-size: 40rpx;
  color: #313131;
}
.result-msg {
  font-size: 30rpx;
  color: #313131;
  margin-bottom: 38rpx;
}
.result-des {
  font-size: 30rpx;
  color: #e83231;
  margin-bottom: 130rpx;
}
.result-link {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: #ff7427;
}
</style>
