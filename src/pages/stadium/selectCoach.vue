<template>
  <view class="class-custom-tabs">
    <CoachList v-if="date && start_time && end_time" :date="date" :start_time="start_time" :end_time="end_time" />
  </view>
</template>

<script setup lang="ts" name="selectCoach">
import CoachList from './components/CoachList.vue'

const date = ref('');
const start_time = ref('');
const end_time = ref('');
onLoad((options: any) => {
  date.value = options.date;
  start_time.value = options.start_time;
  end_time.value = options.end_time;
})
</script>

<style lang="scss">
// .page {
//   padding-bottom: 0; // tabBar页面不需要底部安全距离设置
// }
.page-main {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.class-custom-tabs {
  height: 0;
  flex-grow: 1;
  // padding-bottom: 48px; // tabbar高度
}
.switch-wrap {
  padding: 10rpx;
}
.time-limit {
  margin-bottom: 0;
  font-size: 26rpx;
  margin-bottom: 10rpx;
  width: 130rpx;
  text-align: right;
}
.pt-list-wrap {
  height: 100%;
}

.nodata {
  height: calc(100% - 138px);
}
.private-list {
  padding: 22rpx 0;
  // height: calc(100% - 138px);
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;

  .course {
    box-sizing: border-box;
    width: 690rpx;
    height: 190rpx;
    border: 1rpx solid var(--THEME-COLOR);
    border-radius: 20rpx;
    display: flex;
    position: relative;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    margin: 0 auto 25rpx;
    .lef-tag {
      position: absolute;
      left: 0;
      top: 0;
      border-radius: 20rpx 0 20rpx 0;
      width: 150rpx;
      height: 30rpx;
      line-height: 30rpx;
      text-align: center;
      background: rgba(var(--THEME-RGB), 0.4);
      font-size: 20rpx;
      font-weight: bold;
      &.right {
        right: 0;
        left: auto;
        border-radius: 0 20rpx 0 20rpx;
      }
    }
    .left {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
      .time {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        font-weight: bold;
        margin-right: 66rpx;
        .time-line {
          width: 2rpx;
          height: 16rpx;
          background-color: var(--THEME-COLOR);
          margin: 6rpx 0;
        }
      }
      .time-bg {
        font-size: 23rpx;
        margin-right: 10rpx;
        padding: 0 10rpx;
        height: 32rpx;
        line-height: 32rpx;
        text-align: center;
        color: var(--THEME-COLOR);
        border-radius: 15rpx;
        font-weight: bold;
      }
    }
  }
}
[data-theme='dark'] {
  .date-switch .day {
    color: #fff;
  }
  .time-bg {
    color: var(--THEME-COLOR);
  }
}
.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 26rpx;
}

.surplus-num {
  width: 100rpx;
  text-align: center;
  font-size: 22rpx;
  color: $theme-text-color-grey;
}
.coach-info {
  display: flex;
  flex: 1;
  justify-content: center;
  flex-direction: column;
  overflow: hidden;
  .name {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    margin-right: 28rpx;
    margin-bottom: 15rpx;
    font-weight: bold;
    overflow: hidden;
    > text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.1;
    }
    image {
      flex-shrink: 0;
    }
  }
  .item {
    display: flex;
    align-items: center;
  }
  .coach {
    margin-bottom: 16rpx;
    .coach-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.1;
      flex: 1;
    }
  }
  .coach-classes {
    display: flex;
    flex-wrap: wrap;
    max-height: 80rpx;
    overflow: hidden;
    font-size: 20rpx;
  }
  .class {
    display: inline-block;
    line-height: 32rpx;
    padding: 0 15rpx;
    height: 32rpx;
    box-sizing: border-box;
    background: rgba(var(--THEME-RGB), 0.2);
    border-radius: 6rpx;
    margin-right: 7rpx;
    margin-bottom: 12rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.reserve-btn {
  width: 94rpx;
  height: 94rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: var(--THEME-COLOR);
  border-radius: 50%;
  text-align: center;
  position: relative;
  font-size: 26rpx;
  font-weight: bold;
  color: #03080e;
}
.reserve-btn::after {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  width: 102rpx;
  height: 102rpx;
  content: ' ';
  border: 4rpx solid var(--THEME-COLOR);
  border-radius: 50%;
  border-right: 4rpx solid transparent;
}
.reserve-btn.circle::after {
  border: 4rpx solid var(--THEME-COLOR);
}
.reserve-btn.disabled {
  background-color: #dedede;
  color: $theme-text-color-grey;
}
.reserve-btn.disabled::after {
  border: 4rpx solid #dedede;
}
.reserve-btn.waiting {
  background-color: $theme-text-color-other;
  color: #fff;
}
.reserve-btn.waiting::after {
  border: 4rpx solid $theme-text-color-other;
}
.reserve-btn.waiting-circle {
  background-color: transparent;
  color: $theme-text-color-other;
}
.reserve-btn.waiting-circle::after {
  border: 4rpx solid $theme-text-color-other;
}
</style>