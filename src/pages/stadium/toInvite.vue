<template>
  <view class="invite-wrap theme-bg">
    <view class="invite-item space-wrap" @tap="inviteChoose(1)">
      <view class="tit">订场邀约</view>
      <view class="des">邀约他人一起运动，成团后再进行场地预约支付。</view>
      <view class="bot">可以设置参与人平摊费用（线下支付）。</view>
    </view>
    <view class="invite-item san-wrap" @tap="inviteChoose(2)">
      <view class="tit">散场邀约</view>
      <view class="des">不预订场地，参与人自主散场购票一起运动。</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="class">
function inviteChoose(inviteType) {
  uni.navigateTo({
    url: `/pages/stadium/inviteChoose?inviteType=${inviteType}`,
  })
}
</script>
<style lang="scss" scoped>
.invite-wrap {
  height: 100%;
  overflow: hidden;
}
.invite-item {
  box-sizing: border-box;
  width: 667rpx;
  height: 327rpx;
  margin: 30rpx auto;
  padding: 46rpx;
  color: #1b1b1b;
  &.space-wrap {
    margin-top: 50rpx;
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/san-order-bg.png')
      center/ cover no-repeat;
  }
  &.san-wrap {
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/space-order-bg.png')
      center/ cover no-repeat;
  }
  .tit {
    margin-bottom: 25rpx;
    font-size: 40rpx;
    font-weight: bold;
  }
  .des {
    margin-bottom: 50rpx;
    font-size: 30rpx;
  }
  .bot {
    font-size: 20rpx;
    color: #7d7d7d;
  }
}
</style>
