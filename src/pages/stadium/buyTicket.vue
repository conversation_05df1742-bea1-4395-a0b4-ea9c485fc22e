<template>
  <view v-if="info" class="footer-hasfixed">
    <view class="form-items theme-bg mt20">
      <view class="item">
        <view class="label">票名称</view>
        <view class="value">{{ info.name }}</view>
      </view>
      <view class="item">
        <view class="label">截止时间</view>
        <view class="value">{{ info.valid_data }}</view>
      </view>
      <view class="item">
        <view class="label">适用场地</view>
        <view class="value" @tap="handleSpaceName(sanId, info.space_name)">
          <view class="value-overtext">{{ info.space_name && info.space_name.length ? info.space_name : '入场' }}</view>
          <view class="arrow-right"></view>
        </view>
      </view>

      <view class="item">
        <view class="label">时长</view>
        <view class="value">{{ info.base_duration }}{{ info.duration_unit == 1 ? '小时' : '分钟' }}</view>
      </view>

      <view class="item">
        <view class="label">费用</view>
        <view class="value">￥{{ info.base_fee }}</view>
      </view>
      <CouponSelect
        v-if="info.base_fee"
        :id="sanId"
        :amount="info.base_fee"
        :use-limit="5"
        @on-change="changeCouponInfo"
      />
    </view>
    <PayTypeSelect
      :pay-type-list="payMethodList"
      :pay-type-index="payMethodIdx"
      :card-list="cardList"
      :card-index="cardIdx"
      :value-card-text="useValueCardData.valueText"
      @change-type="handlePayMethodChange"
      @change-card="handleCardChange"
      @refresh-page="getInfo"
    />
    <view class="form-items theme-bg mt20" v-if="isEmpty">
      <view class="item">
        <rich-text :nodes="notice" type="text"></rich-text>
      </view>
    </view>

    <!-- <view v-if="htmlSnipFlag === 1" class="rich-text payment form-items theme-bg">
      <view class="payment-tit">订场须知</view>
      <rich-text :nodes="htmlSnip"></rich-text>
    </view> -->
    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <view v-if="isUseValueCard" class="lef-flex mgr">
          <text class="bill"> <text class="unit">￥</text>{{ useValueCardData.bill }} </text>
          <text v-if="isUseValueCard" class="usage-text">{{ useValueCardData.usageText }}</text>
        </view>
        <button class="normal-btn" :class="{ custom: isUseValueCard }" @tap="handlePayClick">支付</button>
      </view>
    </view>
    <!-- 购票须知 -->
    <view v-if="noticePopup" class="notice-container">
      <scroll-view scroll-y="true" class="notice-content">
        <rich-text :nodes="notice" type="text"></rich-text>
      </scroll-view>
      <button class="normal-btn" @tap="closeNotice">我知道了</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="buyTicket">
import { unescapeHTML } from '@/utils/shared'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import CouponSelect from '@/components/CouponSelect.vue'
import PayTypeSelect from '@/components/PayTypeSelect.vue'
import _ from 'lodash'
import { useSpace } from '@/hooks/useSpace'
// 购票须知-内容
const notice = ref('')
// 购票须知-弹窗boolean
const noticePopup = ref(false)
// 购票须知-是否存在内容boolean
const isEmpty = ref(false)
const { handleSpaceName } = useSpace()
const userStore = useUserStore()
const { checkLogin, getParam } = useLogin()
const sanId = ref()
const info = ref()
const cardList = ref<Record<string, any>>([])
const cardIdx = ref(0)
const isUseValueCard = computed(() => {
  // 是否使用储值卡
  return payTypeState.pay_type === 8
})
const useValueCardData = computed(() => {
  // 使用储值卡支付，需要展示的数据
  if (!isUseValueCard.value) {
    return {
      total: 0, // 总售价
      usage: 0, // 抵扣金额
      bill: 0, // 抵扣后售价，大于0则代表储值卡余额不足
      valueText: '', // 余额文本
      usageText: '', // 抵扣文本
    }
  }
  const selectedCard = cardList.value[cardIdx.value]
  const total = +info.value.base_fee
  const afterCoupon = couponInfo.value ? +couponInfo.value.bill : total // 折扣券后售价
  const usage = selectedCard.balance >= afterCoupon ? afterCoupon : +selectedCard.balance
  const bill = +(afterCoupon - usage).toFixed(2)
  const valueText = `(余额${selectedCard.balance}${bill > 0 ? '，还需' + bill.toFixed(2) : ''})`
  const usageText = `储值卡已抵${usage}元`

  return {
    total,
    usage,
    bill,
    valueText,
    usageText,
  }
})
const payTypeState = reactive({
  pay_type: 1,
  balance: '0', // 使用储值卡余额，支付方式不是储值卡则为0
  card_user_id: '',
})
const htmlSnipFlag = ref(0)
const htmlSnip = ref('')
const payMethodIdx = ref('0')
const payMethodList = ref<
  {
    pay_type: number
    pay_name: string
    icon?: string
  }[]
>([])
const couponInfo = ref()

// 二维码跳转进散场票
const curOptions = ref()

onLoad((options) => {
  sanId.value = options.id
  curOptions.value = options
})

onShow(async () => {
  curOptions.value = curOptions.value.scene !== undefined ? await getParam(curOptions.value.scene || '') : curOptions.value
  if (curOptions.value.san_id) {
    sanId.value = curOptions.value.san_id
  }
  if (curOptions.value.scene && curOptions.value.bus_id == userStore.userInfoBusId) {
    getInfo()
  } else {
    await checkLogin(true, curOptions.value.bus_id).then(() => {
      getInfo()
    })
  }
})

function changeCouponInfo(info) {
  couponInfo.value = info
}
function handlePayMethodChange(data) {
  payMethodIdx.value = data.index
  payTypeState.pay_type = data.payType
  if (payTypeState.pay_type === 8 && data.choseCard) {
    payTypeState.card_user_id = data.choseCard.card_user_id
    payTypeState.balance = data.choseCard.balance
  } else {
    payTypeState.card_user_id = ''
    payTypeState.balance = '0'
  }
}

function handleCardChange(data) {
  cardIdx.value = data.index
  if (data.choseCard) {
    payTypeState.card_user_id = data.choseCard.card_user_id
    payTypeState.balance = data.choseCard.balance
  }
}

const handlePayClick = _.throttle(
  async () => {
    const { user_id, bus_id } = await checkLogin()
    const { bill, ...coupon } = couponInfo.value // 去除折扣后金额字段bill
    const postData = {
      user_id,
      bus_id,
      san_id: sanId.value,
      ...payTypeState,
      ...coupon,
    }
    if (curOptions.value.source) {
      postData.source = curOptions.value.source
    }
    if (curOptions.value.device_id) {
      postData.device_id = curOptions.value.device_id
    }
    http.post('Santicket/reserve', postData).then((res) => {
      const orderSn = res.data.san_order_sn
      if (res.data.jsApiParameters) {
        const sanLogId = res.data.san_log_id
        pay(res.data.jsApiParameters, orderSn, sanLogId)
      } else {
        navigateToPage(orderSn)
      }
    })
  },
  2000,
  true
)
function pay(info, orderSn, sanLogId) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'wxpay',
    orderInfo: info.orderInfo || '',
    success: () => {
      navigateToPage(orderSn)
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      uni.hideLoading()
      // 如果是储值卡组合支付,并且有余额，立即返还扣除的储值卡余额，否则会5分钟后自动返还
      if (payTypeState.pay_type === 8 && sanLogId && +payTypeState.balance !== 0) {
        const params = {
          bus_id: userStore.userInfoBusId,
          user_id: userStore.userInfoUserId,
          card_user_id: payTypeState.card_user_id,
          business_id: sanLogId, // 业务id，散场票传san_log_id
          business_type: '1', // 业务类型，1--散场票，2--订场，3--购卡
        }
        http.post('Booking/storedCardChangeRevoke', params).then((res) => {
          if (res.errorcode != 0) {
            uni.showToast({ title: res.data.errormsg, icon: 'none' })
          }
        })
      }
    },
  })
}
function navigateToPage(orderSn) {
  uni.showLoading({
    title: '支付成功',
    mask: true,
  })
  setTimeout(() => {
    uni.navigateTo({
      url: `/pages/stadium/orderSucceed?orderSn=${orderSn}`,
      success: (result) => {
        uni.hideLoading()
      },
    })
  }, 1000)
}
function getInfo() {
  http
    .get('/Santicket/buySanInfo', {
      bus_id: userStore.userInfoBusId,
      san_id: sanId.value,
      user_id: userStore.userInfoUserId,
    })
    .then((res) => {
      info.value = res.data.san_info
      const noticeHTML = unescapeHTML(res.data.san_info.notice || '')
      const cleanedHtml = noticeHTML.replace(/<[^>]+>/g, '');
      String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, '');
      }

      if(cleanedHtml.trim() != "") {
        notice.value = noticeHTML
        isEmpty.value = true
        noticePopup.value = true
      }

      cardList.value = res.data.card_list
      const stadiumCdn = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/'
      // 散票支付方式: 微信/储值卡
      payMethodList.value = res.data.pay_type_arr.map((item, index) => {
        if (item.pay_type === 1) {
          item.icon = `${stadiumCdn}wx.png`
        } else if (item.pay_type === 8) {
          if (cardList.value.length) {
            payMethodIdx.value = index + ''
          }
          item.icon = `${stadiumCdn}vip.png`
        }
        return item
      })
      if (cardList.value.length) {
        const choseCard = cardList.value[cardIdx.value]
        // cardIdx.value = cardList.value.findIndex((item) => item.card_user_id === payTypeState.card_user_id)
        payTypeState.card_user_id = choseCard.card_user_id
        payTypeState.balance = choseCard ? choseCard.balance : '0'
      }
      payTypeState.pay_type = payMethodList.value[payMethodIdx.value].pay_type

      htmlSnip.value = unescapeHTML(res.data.config.booking_notice)
      htmlSnipFlag.value = res.data.config.booking_notice_swtich
    })
}

function closeNotice() {
  noticePopup.value = false
}
</script>

<style lang="scss" scoped>
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}

.fixed-bottom-wrap {
  .lef-flex {
    display: flex;
    color: $theme-text-color-other;
  }
  .bill {
    margin-right: 7px;
    font-weight: bold;
    font-size: 36rpx;
    .unit {
      font-size: 24rpx;
    }
  }
  .usage-text {
    align-self: flex-end;
    font-size: 24rpx;
  }
  .normal-btn.custom {
    width: 296rpx;
  }
}

.footer-hasfixed {
  position: relative;
}

.notice-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 30rpx;
  box-sizing: border-box;

  .notice-content {
    flex: 1;
    height: 70vh;
    background: #fff;
    padding: 18rpx;
    box-sizing: border-box;
  }
}
</style>
