<template>
  <view class="ball-release footer-hasfixed">
    <view class="box-tit"> {{ sbPost.type === 1 ? '场地信息' : '场地类型' }} </view>
    <view v-if="sbPost.type === 2" class="spaceitem-wrap">
      <SpaceItem :link="false" :info="sanInfo" />
    </view>
    <view v-if="sbPost.type === 1" class="form-items theme-bg">
      <view class="item">
        <view class="label">时间</view>
        <view class="value">{{ sbPost.date }} {{ sbPost.datetime }}</view>
      </view>
      <view class="item">
        <view class="label">场地</view>
        <view class="value">{{ sbPost.space_name }}</view>
      </view>

      <view class="item">
        <view class="label">预估金额</view>
        <view class="value">￥{{ sbPost.amount }}</view>
      </view>
    </view>
    <view class="form-items theme-bg">
      <view v-if="sbPost.type === 2" class="item">
        <view class="label">邀约日期</view>
        <view class="value"
          ><MyDatePick v-model="sbPost.date" :start="dateStart" :end="dateEnd" @change="dateChange"
        /></view>
      </view>
      <view v-if="sbPost.type === 2" class="item">
        <view class="label">时间范围</view>
        <view class="value"><MyTimeRangePick v-model="sbPost.datetime" @change="timeRangeChange" /></view>
      </view>
      <view class="item">
        <view class="label label-hastips">
          成团人数
          <view class="label-tips"
            >发起人占用1个名额
            <text v-if="sbPost.type === 1">，最大人数不能多于{{ sbPost.max_join_people }}人</text></view
          >
        </view>
        <view class="value">
          <uni-number-box
            v-model="sbPost.group_size"
            :min="2"
            :max="sbPost.type === 1 ? sbPost.max_join_people : 30"
            @change="getAaPrice"
          />
        </view>
      </view>

      <view class="item">
        <view class="label label-hastips">
          邀约截止时间
          <view class="label-tips"
            >截止时间不晚于
            {{
              sbPost.type === 1
                ? lastTime
                : `${sbPost.date || ''} ${
                    (sbPost.schedules &&
                      JSON.parse(sbPost.schedules)[0] &&
                      JSON.parse(sbPost.schedules)[0].start_time) ||
                    ''
                  }`
            }}</view
          >
        </view>
        <view class="value">
          <MyDatePick v-model="sbPost.signup_cutoff_date" :start="dateStart" :end="sbPost.date || dateEnd" />
          <MyTimePick v-model="sbPost.signup_cutoff_time" />
        </view>
      </view>
      <view v-if="sbPost.type === 2" class="item">
        <view class="label"> 参与方式 </view>
        <view class="value"> 自行购票入场 </view>
      </view>
      <view v-if="sbPost.type === 1" class="item">
        <view class="label"> 平摊场地费用 </view>
        <view class="value">
          <radio-group class="radio-group" @change="radioChange">
            <label class="radio"> <radio value="2" :checked="sbPost.price_type === '2'" />需要平摊费用 </label>
            <label class="radio"> <radio value="1" :checked="sbPost.price_type === '1'" />无需平摊费用 </label>
          </radio-group>
        </view>
      </view>
      <view v-if="sbPost.type === 1 && sbPost.price_type === '2'" class="item item-tips">
        <view class="label label-hastips">
          费用金额
          <view class="label-tips">费用请发起人自行线下收取，平台仅提供信息发布，不提供费用代收功能</view>
        </view>
        <view class="value"> <input v-model="sbPost.aa_price" class="item-border-input" /> 元/人 </view>
      </view>
      <view class="talk">
        <view class="talk-tit">邀约说明</view>
        <textarea v-model="sbPost.desc" maxlength="500" auto-height placeholder="请填写" />
      </view>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="handleRelease">确认发布</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="ballRelease">
import MyDatePick from '@/components/MyDatePick.vue'
import MyTimePick from '@/components/MyTimePick.vue'
import MyTimeRangePick from '@/components/MyTimeRangePick.vue'
import { useLogin } from '@/hooks/useLogin.ts'
import SpaceItem from './components/SpaceItem.vue'
import { formatDate } from '@/utils/shared'
import http from '@/utils/request'
const { checkLogin } = useLogin()
const sanInfo = ref()
const lastTime = ref()
const sbPost = reactive({
  type: 1,
  date: '',
  amount: 0,
  all_price: '',
  price_type: '2',
  max_join_people: 2,
  group_size: 2,
  signup_cutoff_date: '',
  aa_price: '',
  signup_cutoff_time: '',
  datetime: '',
  space_name: '',
  bus_id: '',
  user_id: '',
  space_id: '',
  space_type_id: '',
  card_user_id: '',
  is_half: 0,
  schedules: '',
  desc: '',
})
const dateStart = formatDate(new Date(), 'yyyy-MM-dd')
const dateEnd = formatDate(new Date(Date.now() + 30 * 24 * 3600 * 1000), 'yyyy-MM-dd')
onLoad((options) => {
  if (options.inviteType === '2') {
    sanInfo.value = uni.getStorageSync('sanPost') || {}
    sbPost.space_type_id = sanInfo.value.id
  } else {
    Object.assign(sbPost, uni.getStorageSync('sbPost') || {})
    sbPost.schedules = JSON.stringify(sbPost.schedules)
  }
  sbPost.type = +(options.inviteType || 1)
  setTimeAndDes(+(options.mark_group_cutoff_time || 0))
  getAaPrice(sbPost.group_size)
})
onUnload(() => {
  uni.removeStorageSync('sbPost')
})
function setTimeAndDes(hour) {
  if (sbPost.type === 1) {
    sbPost.desc = '主题：轻松休闲赛\n3v3正式到场后随机组队，可以自行多人成队\n费用：到场后联系发起人交纳\n发起人电话:'
    lastTime.value = formatDate(
      new Date(
        new Date(
          `${sbPost.date} ${sbPost.schedules && JSON.parse(sbPost.schedules)[0].start_time}`.replace(/-/g, '/')
        ).getTime() -
          hour * 3600 * 1000
      ),
      'yyyy-MM-dd HH:ss'
    )
    sbPost.signup_cutoff_date = lastTime.value.slice(0, 10)
    sbPost.signup_cutoff_time = lastTime.value.slice(11, 16)
  } else {
    sbPost.desc =
      '主题：轻松休闲赛\n3v3正式到场后随机组队，可以自行多人成队\n参与方式：自行购票入场后联系发起人集合\n发起人电话:'
  }
}

function radioChange(ev) {
  const type = ev.detail.value
  sbPost.price_type = type
  if (type === '1') {
    sbPost.desc = '主题：轻松休闲赛\n3v3正式到场后随机组队，可以自行多人成队\n发起人电话:'
  } else {
    sbPost.desc = '主题：轻松休闲赛\n3v3正式到场后随机组队，可以自行多人成队\n费用：到场后联系发起人交纳\n发起人电话:'
  }
}
function dateChange(date) {
  if (!sbPost.signup_cutoff_date || sbPost.signup_cutoff_date > date) {
    sbPost.signup_cutoff_date = date
  }
}
function timeRangeChange(rangeArr) {
  sbPost.signup_cutoff_time = rangeArr[0]
  sbPost.schedules = JSON.stringify([
    {
      start_time: rangeArr[0],
      end_time: rangeArr[1],
    },
  ])
}
function getAaPrice(peopleNum) {
  sbPost.aa_price = (Math.ceil((sbPost.amount / peopleNum) * 100) / 100).toFixed(2)
}
async function handleRelease() {
  const { bus_id, user_id } = await checkLogin()
  const postData = {
    bus_id,
    user_id,
  }
  if (!sbPost.signup_cutoff_date || !sbPost.signup_cutoff_time) {
    uni.showToast({
      title: '请填写截止时间！',
      icon: 'none',
    })
    return
  }
  http
    .post('/Sportsmark/initiateMark', {
      ...sbPost,
      ...postData,
    })
    .then((res) => {
      uni.showToast({ title: res.errormsg })
      uni.requestSubscribeMessage({
        tmplIds: [res.data && res.data.template_id],
        complete() {
          uni.navigateTo({
            url: `/pages/stadium/releaseSuccess?sports_mark_id=${res.data.sports_mark_id}&sports_mark_order_id=${res.data.sports_mark_order_id}&tips=${res.data.tips}&inviteType=${sbPost.type}`,
          })
        },
      })
    })
}
</script>

<style lang="scss" scoped>
.spaceitem-wrap {
  margin: -30rpx 30rpx 30rpx;
}
.ball-release {
  .box-tit {
    margin: 0 20rpx;
  }
}
.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
.payment-tit {
  font-size: 26rpx;
  font-weight: bold;
  color: #1b1b1b;
  line-height: 80rpx;
}
.payment {
  .item {
    .label {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 26rpx;
      color: #1b1b1b;
      line-height: 30rpx;
    }

    .value {
      font-size: 26rpx;
      color: #313131;
      line-height: 30rpx;
      margin: 0 20rpx;
    }

    .value-card {
      font-size: 24rpx;
      color: #888888;
      line-height: 30rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin: 0 30rpx 0 40rpx;
    }
  }
}
.talk {
  min-height: 250rpx;
  padding-bottom: 28rpx;
  .talk-tit {
    margin: 28rpx 0;
  }

  textarea {
    border: 1px solid #dedee0;
    border-radius: 10px;
    box-sizing: border-box;
    width: 100%;
    font-size: 26rpx;
    color: #898989;
    padding: 26rpx 34rpx;
    min-height: 280rpx;
    line-height: 1.5;
  }
}
</style>
