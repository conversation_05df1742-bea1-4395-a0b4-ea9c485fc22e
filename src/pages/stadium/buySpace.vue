<template>
  <view v-if="sbPost" class="footer-hasfixed">
    <view class="form-items theme-bg mt20">
      <view class="title">场地信息</view>
      <view class="item">
        <view class="label">场地</view>
        <view class="value">{{ sbPost.space_name }}</view>
      </view>
      <view class="item">
        <view class="label">时间</view>
        <view class="value">{{ sbPost.date }} {{ sbPost.datetime }}</view>
      </view>
      <view class="item">
        <view class="label">类型</view>
        <view class="value">{{ sbPost.is_half == 1 ? '半场' : '全场' }}</view>
      </view>
      <view class="item">
        <view class="label">入场人数</view>
        <view class="value">最多允许{{ sbPost.max_join_people }}人</view>
      </view>
      <view class="item">
        <view class="label">费用</view>
        <view class="value">￥{{ sbPost.all_price }}</view>
      </view>
      <CouponSelect
        v-if="!sportsMarkId && !!sbPost.all_price"
        :id="sbPost.space_type_id"
        :amount="sbPost.all_price"
        :use-limit="4"
        @on-change="changeCouponInfo"
      />
      <!-- <view class="item">
        <view class="label">优惠后需支付</view>
        <view class="value price">￥{{ sbPost.all_price }}</view>
      </view> -->
    </view>

    <view v-if="authority" class="form-items theme-bg mt20">
      <view class="title" style="display: flex; justify-content: space-between; align-items: center">
        <text>课程信息</text>
        <uni-icons v-if="course.coach_id" type="trash" size="20" @tap="handleClearCourse" />
      </view>
      <view class="item">
        <view class="label">教练</view>
        <view class="value" @tap="handleCoach">
          <!-- <icon v-if="!course.coach_id" type="search" size="20"/> -->
          <text v-if="!course.coach_id">请选择教练</text>
          <text v-else>{{ course.coach_name }}</text>
          <div class="arrow">
            <div class="arrow-top"></div>
            <div class="arrow-bottom"></div>
          </div>
        </view>
      </view>
      <view class="item">
        <view class="label">预约方式</view>
        <view class="value">
          <radio-group @change="handleReserveType">
            <label><radio value="1" :disabled="!course.coach_id" :checked="course.reservation_type === '1'" />用卡预约</label>
            <label style="margin-left: 20rpx;"><radio value="2" :disabled="!course.coach_id" :checked="course.reservation_type === '2'" />直接付费预约</label>
          </radio-group>
        </view>
      </view>

      <template v-if="course.coach_id">
        <template v-if="courseList.length > 0">
          <view class="item">
            <view class="label">课程</view>
            <view class="value">
              <picker class="value" style="padding: 0" @change="handleCourse" :value="cardIndex" :range="courseList" range-key="picker_name">
                <text v-if="!course.card_id">请选择课程</text>
                <view v-else>{{ course.card_name }}</view>
              </picker>
              <div class="arrow">
                <div class="arrow-top"></div>
                <div class="arrow-bottom"></div>
              </div>
            </view>
          </view>
          <view class="item">
            <view class="label">上课时间</view>
            <view class="value">
              <picker class="value" style="padding: 0" @change="handleTime" :value="timeIndex" :range="timeList">
                <text v-if="!course.time">请选择时间</text>
                <view v-else>{{ course.time }}</view>
              </picker>
              <div class="arrow">
                <div class="arrow-top"></div>
                <div class="arrow-bottom"></div>
              </div>
            </view>
          </view>
        </template>
        <template v-else>
          <view v-if="course.reservation_type === '1'" class="nodata">您暂无对应课程，无法预约!</view>
          <view v-else class="nodata">此教练不支持单节付费课方案, 无法预约!</view>
        </template>
      </template>

      <template v-if="course.reservation_type === '2'">
        <view class="item" v-if="price?.original_amount">
          <view class="label">标准价格</view>
          <view class="value">
            <text>¥{{ price?.original_amount }}</text>
          </view>
        </view>
        <view class="item">
          <view class="label">持卡优惠</view>
          <view class="value">
            <text class="type" v-if="price?.card_name">{{ price?.card_name }}</text>
            <text class="amount" v-if="price?.discount_amount !== undefined">{{ price?.discount_amount }}</text>
          </view>
        </view>
        <view class="item" v-if="price?.pay_amount">
          <view class="label">优惠后需支付</view>
          <view class="value">
            <text class="amount">{{ price?.pay_amount }}</text>
          </view>
        </view>
      </template>

    </view>

    <!-- 因为onShow调用了，目前这里暂时不用监听@refresh-page -->
    <PayTypeSelect
      :pay-type-list="payMethodList"
      :pay-type-index="payMethodIdx"
      :card-list="cardList"
      :card-index="cardIdx"
      :value-card-text="useValueCardData.valueText"
      @change-type="handlePayMethodChange"
      @change-card="handleCardChange"
    />
    <view class="form-items theme-bg mt20">
      <view class="item">
        <view class="label">备注</view>
        <textarea
          v-model="sbPost.remarks"
          maxlength="150"
          class="item-input"
          auto-height
          placeholder="如有需要请填写（150个字以内）"
        />
      </view>
    </view>
    <view class="rich-text payment form-items theme-bg">
      <custom-tabs v-model="current" class="auto-height-tab">
        <custom-tab-pane label="订场须知">
          <rich-text v-if="htmlSnipFlag === 1" :nodes="htmlSnip"></rich-text>
          <view v-else>暂无</view>
        </custom-tab-pane>
        <custom-tab-pane label="退款规则">
          <view class="title">订场退款规则:</view>
          <view style="margin-bottom: 10rpx">预订超过 <text style="color: #FF2351; font-weight: bold">{{ info.cancel_booking_after_order }}</text> 小时，不可退订</view>
          <view style="margin-bottom: 10rpx">开场前 <text style="color: #FF2351; font-weight: bold">{{ refundLadder[0] && refundLadder[0].start }}</text> 分钟内，不可退订</view>
          <view v-for="(item, index) in refundLadder" :key="index" style="margin-bottom: 10rpx">
            开场前 <text style="color: #FF2351; font-weight: bold">{{ item.start }}</text> {{ item.end ? '' : '分钟' }} ~ <text style="color: #FF2351; font-weight: bold">{{ item.end || '无限制'
            }}</text> {{ item.end ? '分钟' : '' }}，退款 <text style="color: #FF2351; font-weight: bold">{{ item.rate }}%</text>
          </view>
          <template v-if="course.reservation_type === '2'">
            <view class="title" style="margin-top: 20rpx">约课退款规则:</view>
            <view style="line-height: 40rpx">
              <rich-text :nodes="tagFirst" type="text"></rich-text>
            </view>
            <view style="line-height: 40rpx" v-for="(item, index) of tagList" :key="index">
              <rich-text :nodes="item" type="text"></rich-text>
            </view>
          </template>
        </custom-tab-pane>
      </custom-tabs>
    </view>
    <view class="fixed-bottom-wrap theme-bg">
      <view class="buttons">
        <view v-if="isUseValueCard" class="lef-flex mgr">
          <text class="bill"> <text class="unit">￥</text>{{ useValueCardData.bill.toFixed(2) }} </text>
          <text v-if="isUseValueCard" class="usage-text">{{ useValueCardData.usageText }}</text>
        </view>
        <button class="normal-btn" :class="{ custom: isUseValueCard }" @tap="handlePayClick">支付</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="buySpace">
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import { unescapeHTML } from '@/utils/shared'
import { useUserStore } from '@/store/user'
import CouponSelect from '@/components/CouponSelect.vue'
import PayTypeSelect from '@/components/PayTypeSelect.vue'
import http from '@/utils/request'
import lodash from 'lodash'

interface CardItem {
  name: string
  card_user_id: string | number
  [propName: string]: any
}
const current = ref(0)
const userStore = useUserStore()

const info = ref({})
const cardList = ref<CardItem[]>([])
const cardIdx = ref(0)
const isUseValueCard = computed(() => {
  // 是否使用储值卡
  return sbPost.pay_type === 8
})
const useValueCardData = computed(() => {
  // 使用储值卡支付，需要展示的数据
  if (!isUseValueCard.value) {
    return {
      total: 0, // 总售价
      usage: 0, // 抵扣金额
      bill: 0, // 抵扣后售价，大于0则代表储值卡余额不足
      valueText: '', // 余额文本
      usageText: '', // 抵扣文本
      finalBill: '',
      finalUsage: '',
    }
  }
  const selectedCard = cardList.value[cardIdx.value]
  const total = +sbPost.all_price
  let afterCoupon = (couponInfo.value && !sportsMarkId.value) ? +couponInfo.value.bill : total // 折扣券后售价
  const cptPrice = Number(price.value?.pay_amount || 0)
  afterCoupon = Number(afterCoupon) + cptPrice

  const usage = selectedCard && Number(selectedCard.balance) < afterCoupon? +selectedCard.balance : afterCoupon
  const bill = +(afterCoupon - usage)

  const valueText = `(余额${selectedCard && selectedCard.balance}${bill > 0 ? '，还需' + bill.toFixed(2) : ''})`
  const usageText = `储值卡已抵${usage.toFixed(2)}元`

  return {
    total,
    usage,
    bill,
    valueText,
    usageText,
  }
})
const sbPost = reactive<Record<string, any>>({
  sports_mark_id: '', // 约球过来的会带这个id
  sports_mark_order_id: '', // 约球过来的会带这个id
  date: '',
  bus_id: '',
  user_id: '',
  space_id: 0,
  all_price: '0',
  card_user_id: '',
  balance: '0', // 使用储值卡余额，支付方式不是储值卡则为0
  is_half: 0,
  schedules: '',
  remarks: '',
})
const htmlSnipFlag = ref(0)
const htmlSnip = ref('')
const payMethodIdx = ref('0')
const payMethodList = ref<
  {
    pay_type: number
    pay_name: string
    icon?: string
  }[]
>([])
const sportsMarkOrderId = ref('')
const sportsMarkId = ref('')
const couponInfo = ref()
const hasShowedNum = ref(0)

const NONE_COURSE = {
  coach_id: '',
  coach_name: '',
  reservation_type: '1',
  card_id: '',
  card_name: '',
  time: '',
  sort: '',
  pt_charge_plan_id: '',
  pt_card_user_id: '',
}
const course = ref({...NONE_COURSE})

onLoad((options) => {
  Object.assign(sbPost, uni.getStorageSync('sbPost') || {})
  sbPost.schedules = JSON.stringify(sbPost.schedules)
  sportsMarkId.value = options.sports_mark_id || ''
  sportsMarkOrderId.value = options.sports_mark_order_id || ''

  if (!sportsMarkId.value) {
    getAuthorityForCourse()
  }

  hasShowedNum.value += 1
  getInfo()
})
onShow(() => {
  const coachId = uni.getStorageSync('select_coach_id');
  if (!coachId) {
    course.value.coach_id = '';
    course.value.coach_name = '';

    handleClearCourse()
  } else if (coachId !== course.value.coach_id) {
    course.value.coach_id = coachId;
    course.value.coach_name = uni.getStorageSync('select_coach_name');

    course.value.card_id = ''
    course.value.card_name = ''
    course.value.time = ''
    cardIndex.value = 0
    timeIndex.value = 0
    timeList.value = []

    price.value = null
  }

  if (course.value.coach_id) {
    // course.value.reservation_type = '1'
    getCourseList()
  }
})
onUnload(() => {
  if (!sportsMarkId.value && !sportsMarkOrderId.value) {
    uni.removeStorageSync('sbPost')
  }
  
  uni.removeStorage({ key: 'select_coach_id' });
  uni.removeStorage({ key: 'select_coach_name' });
})

function changeCouponInfo(info) {
  couponInfo.value = info
}

function handlePayMethodChange(data) {
  payMethodIdx.value = data.index
  sbPost.pay_type = data.payType
  if (sbPost.pay_type === 8 && data.choseCard) {
    sbPost.card_user_id = data.choseCard.card_user_id
    sbPost.balance = data.choseCard.balance
    // fix 19719 储值卡支付时需要更新价格为会员价/节假日价格（如有设置）
    // if (data.choseCard.schedule_price?.all_price) {
    //   sbPost.amount = data.choseCard.schedule_price.all_price
    //   sbPost.all_price = data.choseCard.schedule_price.all_price
    // }
    if (course.value.reservation_type === '2') {
      getPayInfo(true, data.choseCard.card_user_id, data.choseCard.card_id)
    }
  } else {
    sbPost.card_user_id = ''
    sbPost.balance = '0'
    if (course.value.reservation_type === '2') {
      getPayInfo(false)
    }
  }
  
  getPrice()
}

function handleCardChange(data) {
  cardIdx.value = data.index
  if (data.choseCard) {
    sbPost.card_user_id = data.choseCard.card_user_id
    sbPost.balance = data.choseCard.balance
    // fix 19719 储值卡支付时需要更新价格为会员价/节假日价格（如有设置）
    // if (data.choseCard.schedule_price?.all_price) {
    //   sbPost.amount = data.choseCard.schedule_price.all_price
    //   sbPost.all_price = data.choseCard.schedule_price.all_price
    // }
    if (course.value.reservation_type === '2') {
      getPayInfo(true, data.choseCard.card_user_id, data.choseCard.card_id)
    }
  } else {
    if (course.value.reservation_type === '2') {
      getPayInfo(false)
    }
  }

  getPrice()
}

const handlePayClick = lodash.throttle(
  async () => {
    const chosePay = payMethodList.value[payMethodIdx.value]
    const choseCard = cardList.value[cardIdx.value]

    if (payMethodList.value.length === 0) {
      uni.showToast({ title: '无储值卡可用!', icon: 'none' })
      return false
    } else if (chosePay.pay_type === 8 && cardList.value.length === 0) {
      uni.showToast({ title: '无储值卡可用!', icon: 'none' })
      return false
    }
    // 因为增加了储值卡组合支付，前端不再判断。由请求后后端判断是否开启组合支付，未开启再返回提示储值卡金额不足
    // else if (
    //   chosePay.pay_type === 8 &&
    //   parseFloat(choseCard.balance) < parseFloat(sbPost.amount)
    // ) {
    //   uni.showToast({ title: '储值卡金额不足!', icon: 'none' })
    //   return false
    // }
    sbPost.pay_type = chosePay.pay_type
    sbPost.card_user_id = choseCard ? choseCard.card_user_id : ''
    sbPost.balance = choseCard ? choseCard.balance : '0'
    if (sportsMarkOrderId.value || sportsMarkId.value) {
      sbPost.sports_mark_order_id = sportsMarkOrderId.value
      sbPost.sports_mark_id = sportsMarkId.value
    }

    let coupon = {}
    if (couponInfo.value) {
      const { bill, ...rest } = couponInfo.value // 去除折扣后金额字段bill
      coupon = rest
    }

    let params = {
      ...sbPost,
      ...coupon,
    } as any

    // fix 19957 支付方式不是储值卡，则card_user_id不传值
    if (params.pay_type !== 8) {
      params.card_user_id = ''
      params.balance = '0'
    }

    let url = '/Booking/reserve'
    if (course.value.coach_id && authority.value) {
      if (!course.value.card_id) {
        uni.showToast({
          title: '请先选择课程',
          icon: 'none',
        })
        return
      }
      if (!course.value.time) {
        uni.showToast({
          title: '请选择课程时间',
          icon: 'none',
        })
        return
      }

      url = '/Booking/CombinationReserve'
      let amount = (Number(price.value?.pay_amount || 0) + Number(sbPost.all_price)).toFixed(2)
      params = {
        ...params,
        ...course.value,
        beg_date: sbPost.date + ' ' + course.value.time,
        pt_charge_plan_detail_id: price.value?.pt_charge_plan_detail_id || 0,
        schedule_amount: price.value?.pay_amount || 0,
        book_amount: sbPost.all_price,
        amount,
      }
    }
    http.post(url, params).then((res) => {
      const orderSn = res.data.order_sn
      const orderId = res.space_order_id
      if (res.data.jsApiParameters) {
        pay(res.data.jsApiParameters, orderSn, orderId)
      } else {
        navigateToPage(orderSn)
      }
    })
  },
  2000,
  true
)
function pay(info, orderSn, orderId) {
  uni.requestPayment({
    timeStamp: info.timeStamp,
    nonceStr: info.nonceStr,
    package: info.package,
    signType: info.signType,
    paySign: info.paySign,
    provider: 'alipay',
    orderInfo: info.orderInfo || '',
    success: () => {
      navigateToPage(orderSn)
    },
    fail: () => {
      // fail
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
      let url = '/pages/my/reserveRecord?type=3'
      /* 订场邀约首次取消支付后，再次取消支付不会有新的待支付订单了
        跳转后判断是否已取消，已取消并且使用了储值卡组合支付,此时需要立即返还余额 */
      if (
        (sportsMarkOrderId.value || sportsMarkId.value) &&
        sbPost.pay_type === 8 &&
        sbPost.card_user_id &&
        +sbPost.balance > 0
      ) {
        url += `&revoke_space_order_id=${orderId}`
      }
      setTimeout(() => {
        uni.navigateTo({
          url,
          success: (result) => {
            uni.hideLoading()
          },
        })
      }, 1000)
    },
    complete: function () {
      uni.removeStorageSync('sbPost')
      uni.removeStorage({ key: 'select_coach_id' });
      uni.removeStorage({ key: 'select_coach_name' });
    },
  })
}
function navigateToPage(orderSn) {
  uni.showLoading({
    title: '支付成功',
    mask: true,
  })
  setTimeout(() => {
    uni.navigateTo({
      url: `/pages/stadium/orderSucceed?orderSn=${orderSn}`,
      success: (result) => {
        uni.hideLoading()

        uni.removeStorage({ key: 'select_coach_id' });
        uni.removeStorage({ key: 'select_coach_name' });
      },
    })
  }, 1000)
}

const refundLadder = ref([])
function getInfo() {
  http.get('/Booking/getPayInfo', sbPost).then((res) => {
    const resData = res.data
    info.value = resData
    cardList.value = resData.card_list
    payMethodList.value = resData.pay_type_arr
    refundLadder.value = resData.cancel_booking_refund_ladder.map((item) => {
      return {
        ...item,
        rate: (item.rate * 100).toFixed(2),
      }
    })
    if (cardList.value.length && !sbPost.card_user_id && hasShowedNum.value === 1) {
      // 不想用折扣券，请求时不再赋值min，否则可能更换掉选择的储值卡
      sbPost.card_user_id = resData.min_card_user_id
      cardIdx.value = cardList.value.findIndex((item) => item.card_user_id === sbPost.card_user_id)
      const choseCard = cardList.value[cardIdx.value]
      if (choseCard) {
        payMethodIdx.value = String(payMethodList.value.findIndex((item) => item.pay_type === 8))
        sbPost.balance = choseCard.balance
        if (choseCard.schedule_price?.all_price) {
          sbPost.amount = choseCard.schedule_price.all_price
          sbPost.all_price = choseCard.schedule_price.all_price
        }
      } else {
        sbPost.balance = '0'
      }
    }
    sbPost.pay_type = payMethodList.value[payMethodIdx.value].pay_type
    htmlSnip.value = unescapeHTML(resData.config.booking_notice)
    htmlSnipFlag.value = resData.config.booking_notice_swtich

    if (!sbPost.card_user_id) {
      sbPost.pay_type !== 8 && getPrice()
    }
  })
}
function getPrice() {
  return http.get('/Booking/getPrice', sbPost).then((res) => {
    const resData = res.data
    sbPost.amount = resData.all_price
    sbPost.all_price = resData.all_price
    sbPost.all_no_member_price = resData.all_no_member_price
  })
}

// one time booking
const handleCoach = () => {
  const [start_time, end_time] = sbPost.datetime.split('-')
  uni.navigateTo({
    url: `/pages/stadium/selectCoach?date=${sbPost.date}&start_time=${start_time}&end_time=${end_time}`,
  })
}
const cardIndex = ref(0)
const handleCourse = (event) => {
  const index = Number(event.detail.value)
  cardIndex.value = index

  const card = courseList.value[index]
  course.value.card_id = card.card_id
  course.value.card_name = card.name
  course.value.sort = card.sort
  course.value.pt_charge_plan_id = card.pt_charge_plan_id || ''
  course.value.pt_card_user_id = card.card_user_id
  
  getPtScheduleTime()
  
  if (course.value.reservation_type === '2') {
    course.value.pt_card_user_id = ''

    if (payMethodIdx.value === '0') {
      getPayInfo(false)
    } else {
      const valueCard:CardItem = cardList.value[cardIdx.value]
      getPayInfo(true, String(valueCard.card_user_id), valueCard.card_id)
    }
  }
}
const handleClearCourse = () => {
  course.value = {
    ...NONE_COURSE
  }

  cardIndex.value = 0
  timeIndex.value = 0
  courseList.value = []
  timeList.value = []

  price.value = null

  uni.removeStorage({ key: 'select_coach_id' });
  uni.removeStorage({ key: 'select_coach_name' });
}
const timeIndex = ref(0)
const handleTime = (event) => {
  const index = Number(event.detail.value)
  timeIndex.value = index

  const time = timeList.value[index]
  course.value.time = time
}
const handleReserveType = (event) => {
  course.value.reservation_type = event.detail.value

  course.value.card_id = ''
  course.value.card_name = ''
  course.value.time = ''
  cardIndex.value = 0
  timeIndex.value = 0
  timeList.value = []

  price.value = null

  getCourseList()
}

const tagFirst = ref('');
const tagList = ref<string[]>([]);
const courseList = ref<any>([]);
const getCourseList = () => {
  http
    .get('/Schedule/getPtScheduleDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      coach_id: course.value.coach_id,
      day: sbPost.date,
      appt_type: Number(course.value.reservation_type) - 1,
      not_toany: 1,
    })
    .then((res) => {
      const resInfo = res.data.info
      let list:any[] = []
      if (Array.isArray(resInfo.class_list)) {
        list = resInfo.class_list
        list.forEach((item) => {
          if (course.value.reservation_type === '1') {
            item.picker_name = `${item.name} (${item.class_duration}分钟)`
          } else {
            item.picker_name = `${item.name} (${item.class_duration}分钟/${item.single_price}元)`
          }
        })
      }
      courseList.value = list

      const refundLadderList = resInfo.cancel_pt_refund_ladder;
      if (Array.isArray(refundLadderList) && refundLadderList.length) {
        tagFirst.value = `<span>开课前 <strong style="color: #FF2351;font-size: 28rpx">${refundLadderList[0].start}</strong> 分钟内不允许取消</span>`;

        tagList.value = [];
        refundLadderList.forEach((item: any) => {
          tagList.value.push(
            `<span>开课前 <strong style="color: #FF2351;font-size: 28rpx">${item.start}</strong> 分钟 ~ ${!item.end ? '<strong style="color: #FF2351;font-size: 28rpx">无限制</strong>' : `<strong style="color: #FF2351;font-size: 28rpx">${item.end}</strong> 分钟`}，退款 <strong style="color: #FF2351;font-size: 28rpx">${(item.rate * 100).toFixed(2)}%</strong></span>`
          );
        });
      }
    })
}

const timeList = ref<string[]>([]);
const getPtScheduleTime = () => {
  course.value.time = ''
  timeIndex.value = 0
  timeList.value = []
  const [start_time, end_time] = sbPost.datetime.split('-')
  http
    .get('/card/PtScheduleHour', {
      bus_id: userStore.userInfoBusId,
      coach_id: course.value.coach_id,
      card_id: course.value.card_id,
      date: sbPost.date,
      start_time,
      end_time,
      user_id: userStore.userInfoUserId,
      appt_type: Number(course.value.reservation_type) - 1,
    })
    .then((res) => {
      const list = res.data.list
      timeList.value = []
      if (Array.isArray(list) && list.length) {
        list.forEach((item: any) => {
          const typedItem = item as { start: string; end: string };
          timeList.value.push(typedItem.start + '-' + typedItem.end)
        })
      }
    })
}

// one time pay
const price = ref<any>(null)
const getPayInfo = (hasValueCard = false, card_user_id = '', stored_card_id = '') => {
  if (course.value.card_id === '') {
    return Promise.resolve()
  }
  return http.post('/Schedule/getPtSchedulePayAmount', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    card_id: course.value.card_id,
    sort: course.value.sort,
    pt_charge_plan_id: course.value.pt_charge_plan_id || '',
    is_stored: hasValueCard ? 1 : 0,
    card_user_id,
    stored_card_id,
  }).then((res) => {
    price.value = res.data

    // payMethodList.value = res.data.pay_type_arr
    cardList.value = res.data.card_list

    if (hasValueCard) {
      payMethodIdx.value = String(payMethodList.value.findIndex((item: any) => item.pay_type === 8))
      if (card_user_id) {
        cardIdx.value = cardList.value.findIndex((item: any) => item.card_user_id == card_user_id)
      }
    }
  })
}
const authority = ref(false)
const getAuthorityForCourse = () => {
  http.post('/Booking/getBookingScheduleSet', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
  }).then((res) => {
    authority.value = res.data.booking_schedule == 1;
  })
}
</script>

<style lang="scss" scoped>
.type {
  font-size: 24rpx;
  font-weight: bold;
  color: #dddddd;
  margin-right: 10rpx;
  border: 1rpx solid #eeeeee;
  padding: 0 10rpx;
  border-radius: 8rpx;
}

.amount {
  color: $theme-text-color-other;
}

.price {
  font-size: 36rpx;
  font-weight: bold;
  color: $theme-text-color-other;
}
.payment-tit {
  font-size: 26rpx;
  font-weight: bold;
  color: #1b1b1b;
  line-height: 80rpx;
}

.fixed-bottom-wrap {
  .lef-flex {
    display: flex;
    color: $theme-text-color-other;
  }
  .bill {
    margin-right: 7px;
    font-weight: bold;
    font-size: 36rpx;
    .unit {
      font-size: 24rpx;
    }
  }
  .usage-text {
    align-self: flex-end;
    font-size: 24rpx;
  }
  .normal-btn.custom {
    width: 296rpx;
  }
}
.auto-height-tab :deep(.tab .tab-cont) {
  height: auto;
}

.title {
  font-size: 30rpx;
  font-weight: bold;
  color: #1b1b1b;
  line-height: 80rpx;
}

/* From Uiverse.io by Nawsome */ 
.arrow {
  cursor: pointer;
  height: 15rpx;
  transform: translateX(-50%) translateY(-50%);
  transition: transform 0.1s;
  width: 13rpx;
  margin-top: 10rpx;
  margin-left: 20rpx;
}

.arrow-top, .arrow-bottom {
  background-color: #666;
  height: 4rpx;
  left: -5rpx;
  position: absolute;
  top: 50%;
  width: 100%;
}

.arrow-top:after, .arrow-bottom:after {
  background-color: #fff;
  content: "";
  height: 100%;
  position: absolute;
  top: 0;
  transition: all 0.15s;
}

.arrow-top {
  transform: rotate(45deg);
  transform-origin: bottom right;
}

.arrow-top:after {
  left: 100%;
  right: 0;
  transition-delay: 0s;
}

.arrow-bottom {
  transform: rotate(-45deg);
  transform-origin: top right;
}

.arrow-bottom:after {
  left: 0;
  right: 100%;
  transition-delay: 0.15s;
}

.arrow:hover .arrow-top:after {
  left: 0;
  transition-delay: 0.15s;
}

.arrow:hover .arrow-bottom:after {
  right: 0;
  transition-delay: 0s;
}

.arrow:active {
  transform: translateX(-50%) translateY(-50%) scale(0.9);
}
</style>
