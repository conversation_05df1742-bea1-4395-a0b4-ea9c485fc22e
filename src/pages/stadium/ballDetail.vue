<template>
  <view v-if="info" class="ball-detail theme-bg footer-hasfixed">
    <AboutBallItem :info="info" :link="false" />
    <view class="con-wrap">
      <view class="box-tit">
        <view class="tit-left"> 参与费用 </view>
        <view class="rig ball-color">
          <text v-if="info.type === 1 && info.price_type === 2">{{ info.aa_price }}</text>
          {{
            info.type === 2
              ? '自行购票入场'
              : info.type === 1 && info.price_type === 1
              ? '免费'
              : '元/人'
          }}
        </view>
      </view>
      <view v-if="info.type === 1 && info.price_type === 2" class="ball-color">
        费用请自行线下向发起人交纳，平台仅提供信息发布不提供费用代收功能。
      </view>
      <view class="des-con">
        <view class="box-tit">邀约说明</view>
        <text>{{ info.desc }}</text>
      </view>
      <view class="warn-wrap">
        <view class="warn-tips">
          <image
            class="icon icon-warn"
            src="https://imagecdn.rocketbird.cn/minprogram/uni-member/warn.png"
          />
          <view class="ml">注意</view>
        </view>
        <view class="warn-con">
          <view>1.截止时间后人数不足自动取消邀约</view>
          <view>2.报名截止时间前可进行取消，超过截止时间不可再取消</view>
          <view
            >3.截止时间后达到组团人数则视为成功<text v-if="info.type === 1"
              >，将自动发放入场门票</text
            ></view
          >
        </view>
        <view v-if="info.is_initiator === 1 && info.type === 1 && info.order_status === 0"
          >4.发起人需在活动截止时间前完成订场费用支付，否则达到截止时间后，即使满足报名人数也将视为组团失败</view
        >
      </view>
    </view>

    <view
      class="fixed-bottom-wrap theme-bg"
      :class="
        info.status === 0 && info.is_initiator === 1 && info.type === 1 && info.order_status === 0
          ? 'has-two-column'
          : ''
      "
    >
      <button
        v-if="
          info.status === 0 && info.is_initiator === 1 && info.type === 1 && info.order_status === 0
        "
        class="normal-btn friend top-column"
        @tap="handlePayClick"
      >
        订场支付
      </button>
      <view class="buttons">
        <template v-if="info.status === 0">
          <button class="normal-btn outer-org mgr" open-type="share">即刻分享</button>
          <!-- 发起人 -->
          <button
            v-if="info.is_initiator === 1"
            class="normal-btn disabled-canclick"
            @tap="handleCancelReserve(info.sports_mark_order_id)"
          >
            取消邀约
          </button>
          <template v-else>
            <!-- 参与人已报名 -->
            <button
              v-if="info.sports_mark_order_id"
              class="normal-btn disabled-canclick"
              @tap="handleCancelReserve(info.sports_mark_order_id)"
            >
              取消报名
            </button>
            <button v-else-if="info.last_num === 0" class="normal-btn" disabled>人数已满</button>
            <button
              v-else-if="info.last_num > 0"
              class="normal-btn"
              @tap="handleConfirmSignup(sportsMarkId)"
            >
              立即报名
            </button>
          </template>
        </template>
        <button v-if="info.status !== 0" class="normal-btn" disabled>已结束</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="buyTicket">
import AboutBallItem from './components/AboutBallItem.vue'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useBallRelease } from '@/hooks/useBallRelease.ts'
import { useLogin } from '@/hooks/useLogin.ts'
const { checkLogin } = useLogin()
const userStore = useUserStore()
const sportsMarkId = ref()
const busId = ref()
const info = ref()
const { cancelReserve, confirmSignup } = useBallRelease()
onLoad((options) => {
  sportsMarkId.value = options.sports_mark_id
  busId.value = options.bus_id
})
onShow(() => {
  checkLogin(false, busId.value || '').then(() => {
    getInfo()
  })
})
onUnload(() => {
  const prevPage = getCurrentPages()[getCurrentPages().length - 2].route
  if (prevPage !== 'pages/stadium/aboutBall') {
    uni.redirectTo({
      url: '/pages/stadium/aboutBall',
    })
  }
})
function getInfo() {
  http
    .get('/Sportsmark/sportsMarkDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId || '',
      sports_mark_id: sportsMarkId.value,
    })
    .then((res) => {
      info.value = res.data
    })
}
function handlePayClick() {
  if (info.value.type === 1) {
    const params = {
      space_id: info.value.space_id,
      space_name: info.value.space_name,
      space_type_id: info.value.space_type_id,
      date: info.value.date,
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      card_user_id: '',
      is_half: info.value.is_half,
      position: info.value.position,
      pay_type: '',
      schedules: info.value.schedule_detail || [
        {
          start_time: info.value.beg_date,
          end_time: info.value.end_date,
        },
      ],
      amount: info.value.price,
      datetime: info.value.beg_date + '-' + info.value.end_date,
      max_join_people: info.value.max_join_people,
    }

    uni.setStorageSync('sbPost', params)
    uni.navigateTo({
      url: `/pages/stadium/buySpace?sports_mark_id=${sportsMarkId.value}&sports_mark_order_id=${info.value.sports_mark_order_id}`,
    })
  } else {
    uni.navigateTo({
      url: `/pages/stadium/buySpace?sports_mark_id=${sportsMarkId.value}&sports_mark_order_id=${info.value.sports_mark_order_id}`,
    })
  }
}
function handleConfirmSignup(sportsMarkId) {
  confirmSignup(sportsMarkId, () => {
    getInfo()
  })
}
function handleCancelReserve(id) {
  cancelReserve(id, () => {
    getInfo()
  })
}
onShareAppMessage(() => {
  return {
    path: `/pages/stadium/ballDetail?bus_id=${userStore.userInfoBusId}&sports_mark_id=${
      info.value.sports_mark_id
    }&user_id=${userStore.userInfoUserId || ''}&from=share`,
  }
})
</script>

<style lang="scss" scoped>
.box-tit {
  font-size: 30rpx;
  line-height: 90rpx;
}
.ball-detail {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.ball-color {
  font-size: 22rpx;
  color: $theme-text-color-other;
}
.box-tit .ball-color {
  font-size: 24rpx;
  text {
    font-size: 32rpx;
    font-weight: bold;
  }
}
.con-wrap {
  margin: 0 30rpx;
}
.des-con {
  margin: 20rpx 0;
  padding: 0 20rpx 20rpx;
  background: #f6f6f8;
  border-radius: 10rpx;
  font-size: 24rpx;
  color: #03080e;
  line-height: 1.5;
}
.warn-wrap {
  color: $theme-text-color-other;
  font-size: 22rpx;
  line-height: 35rpx;
  .warn-tips {
    display: flex;
    align-items: center;
    margin: 20rpx 0 10rpx;
    font-size: 24rpx;
    font-weight: bold;
  }
  .ml {
    margin-left: 10rpx;
  }
}
.has-two-column {
  display: block;
  padding-bottom: 20rpx;
  .top-column {
    margin: 20rpx auto;
  }
}
</style>
