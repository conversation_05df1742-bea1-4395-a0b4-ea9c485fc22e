<template>
  <view class="ticketlist-wrap">
    <custom-tabs v-if="typeList && typeList.length" v-model="tabIndex" type="tag">
      <custom-tab-pane v-for="(item, index) in typeList" :key="index" :label="item.name">
        <view class="ticketlist-main">
          <TicketList v-if="tabIndex === index" :type="item.id" />
        </view>
      </custom-tab-pane>
    </custom-tabs>
  </view>
</template>

<script setup lang="ts" name="ticketList">
import http from '@/utils/request'
import customTabs from '@/components/custom-tabs/custom-tabs'
import customTabPane from '@/components/custom-tabs/custom-tab-pane'
import TicketList from './components/TicketList.vue'
import { useUserStore } from '@/store/user'
const tabIndex = ref(0)
const fromTab = ref(0)
const typeList = ref([])
const userStore = useUserStore()
onLoad((options) => {
  //fromTab  票类型id
  fromTab.value = options.id ? +options.id : 0
  getType()
})

function getType() {
  http
    .get('/Booking/getSpaceType', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      const list = res.data
      list.forEach((item, index) => {
        if (item.id === fromTab.value) {
          tabIndex.value = index
        }
      })
      typeList.value = list
    })
}
</script>
<style lang="scss" scoped>
.ticketlist-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
}
.ticketlist-main {
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
