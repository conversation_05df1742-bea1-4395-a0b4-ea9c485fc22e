<template>
  <custom-tabs v-model="current" class="record-tab">
    <custom-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label">
      <view class="swiper">
        <BallList :tab-index="index" :current-index="current"></BallList>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts" name="class">
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import BallList from './components/BallList.vue'
const current = ref(0)
const tabList = ref([
  { value: 1, label: '活动大厅' },
  { value: 2, label: '我发起的' },
  { value: 3, label: '我参与的' },
])
const loadOptions = ref()
onLoad((options) => {
  loadOptions.value = options
})
onUnload(() => {
  // const prevPage = getCurrentPages()[getCurrentPages().length - 2].route
  // if (prevPage !== 'pages/index/index') {
  //   uni.switchTab({ url: '/pages/index/index' })
  // }
})
</script>
<style lang="scss" scoped>
.swiper {
  height: 100%;
}
</style>
