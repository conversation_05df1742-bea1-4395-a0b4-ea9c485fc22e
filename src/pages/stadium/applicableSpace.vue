<template>
  <view class="ticket-box">
    <SpaceItem v-for="(item, index) in dataList" :key="index" :link="false" :info="item" />
  </view>
</template>

<script setup lang="ts" name="TicketList">
import http from '@/utils/request'
import SpaceItem from './components/SpaceItem.vue'
import { useUserStore } from '@/store/user'
const sanId = ref()
onLoad((options) => {
  sanId.value = options.san_id
  loadList()
})
const userStore = useUserStore()
const dataList = ref([])
function loadList() {
  http
    .get('/Santicket/getCanUseListBySanRule', {
      san_id: sanId.value,
    })
    .then((res) => {
      dataList.value = res.data
    })
}
</script>

<style lang="scss" scoped>
.ticket-box {
  height: 100%;
  padding: 30rpx;
  background: #fff;
}
</style>
