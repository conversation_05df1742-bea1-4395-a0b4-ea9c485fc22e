<template>
  <view class="ticket-box">
    <z-paging
      ref="paging"
      v-model="dataList"
      :fixed="false"
      :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
      @query="loadList"
    >
      <TicketItem v-for="(item, index) in dataList" :key="index" :info="item" />
    </z-paging>
  </view>
</template>

<script setup lang="ts" name="TicketList">
import http from '@/utils/request'
import TicketItem from './TicketItem.vue'
import { useUserStore } from '@/store/user'
const props = defineProps({
  type: {
    type: Number,
  },
})
const userStore = useUserStore()
const dataList = ref([])
const paging = ref()
function loadList(pageNo, pageSize) {
  http
    .get('/Santicket/getList', {
      bus_id: userStore.userInfoBusId,
      type_id: props.type,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
</script>

<style lang="scss" scoped>
.ticket-box {
  width: 100%;
  height: 100%;
}
</style>
