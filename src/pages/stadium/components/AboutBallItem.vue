<template>
  <view v-if="info" class="ball-item theme-bg" @tap="goReserveDetail">
    <view class="img-view">
      <image
        class="ball-img"
        :src="
          (info.pic_url && info.pic_url[0]) || 'https://imagecdn.rocketbird.cn/minprogram/member/image/category-1.png'
        "
      />
      <view class="img-con">
        <view class="tit">{{ info.space_type_name }}</view>
        <view class="address">{{ info.space_name }}</view>
        <view class="time">{{ info.date }} {{ info.beg_date }}~{{ info.end_date }}</view>
      </view>
      <view class="pic-rig">
        <view class="down-num">
          {{
            info.status === 0 ? info.last_date : info.status === 1 ? '成功' : info.status === 2 ? '未成功' : '活动取消'
          }}
        </view>
        <view class="theme-con">
          <text v-if="info.type === 2">自行购票入场</text>
          <text v-if="info.type === 1 && info.price_type === 1">免费</text>
          <view v-if="info.type === 1 && info.price_type === 2">
            {{ info.aa_price }}元/人
            <view class="con-min">(线下支付)</view>
          </view>
        </view>
      </view>
    </view>
    <view class="item-info">
      <view class="users">
        <view v-for="(item, index) in info.signup_list" :key="index" class="user-item">
          <template v-if="index < 6">
            <view class="user-img-wrap">
              <image
                class="avatar-img"
                :src="item.avatar || 'https://imagecdn.rocketbird.cn/minprogram/uni-member/avatar.jpg'"
              />
              <view v-if="item.is_initiator" class="tag">发起人</view>
            </view>
            <view class="username">{{ item.nickname }}</view>
          </template>
          <view v-if="index === 6" class="user-last"> . . . </view>
        </view>
      </view>
      <view class="sign-wrap">
        <view>已报名/余位</view>
        <view class="sign-num">{{ info.signup_num }}/{{ info.last_num }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  info: {
    type: Object as any,
  },
  link: {
    type: Boolean,
    default: true,
  },
})
function goReserveDetail() {
  if (props.link) {
    uni.navigateTo({
      url: `/pages/stadium/ballDetail?sports_mark_id=${props.info.sports_mark_id}`,
    })
  }
}
</script>
<style lang="scss" scoped>
.ball-item {
  width: 690rpx;
  margin: 30rpx 30rpx 0;
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
  overflow: hidden;
}
.img-view,
.ball-img {
  position: relative;
  width: 690rpx;
  height: 260rpx;
  background: #000;
}
.ball-img {
  opacity: 0.5;
}
.img-con {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 30rpx;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  .tit {
    margin-bottom: 90rpx;
    font-size: 36rpx;
  }
  .address {
    margin-bottom: 10rpx;
  }
}
.pic-rig {
  position: absolute;
  top: 80rpx;
  right: 0;
  width: 150rpx;
  height: 100rpx;
  background: rgba(var(--THEME-RGB), 0.8);
  font-weight: bold;
  .down-num {
    height: 30rpx;
    line-height: 30rpx;
    text-align: center;
    background: #000;
    font-size: 20rpx;
    color: $theme-text-color-other;
  }
  .theme-con {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    height: 70rpx;
    font-size: 24rpx;
  }
  .con-min {
    font-size: 20rpx;
  }
}
.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;

  .sign-wrap {
    font-size: 20rpx;
  }
  .sign-num {
    text-align: center;
    font-size: 40rpx;
    font-weight: 800;
    line-height: 36rpx;
    margin-top: 10rpx;
  }
}
.users {
  display: flex;
  font-size: 18rpx;
  text-align: center;
  .user-item {
    width: 65rpx;
    margin-right: 10rpx;
  }
  .user-last,
  .avatar-img {
    width: 55rpx;
    height: 55rpx;
    background: #ececec;
    border-radius: 50%;
  }
  .user-last {
    font-size: 26rpx;
    font-weight: bold;
    color: #787878;
  }
  .avatar-img {
    border: 1px solid var(--THEME-COLOR);
  }
  .user-img-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .tag {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 65rpx;
      height: 24rpx;
      line-height: 24rpx;
      background: var(--THEME-COLOR);
      border-radius: 12rpx;
    }
  }
  .username {
    width: 65rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
