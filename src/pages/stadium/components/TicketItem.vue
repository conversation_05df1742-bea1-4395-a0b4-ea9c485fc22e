<template>
  <view v-if="info.id" class="ticket-card">
    <view class="card-left">
      <view class="label">{{ info.name || info.card_name }}</view>
      <view class="desc"
        >{{ info.max_valid_time }}天内使用，进场后可停留 {{ info.base_duration }}
        {{ info.duration_unit == 1 ? '小时' : '分钟' }}</view
      >
      <view class="desc desc-name" @tap="handleSpaceName(info.id, info.space_name)"
        >适用场地：{{ info.space_name && info.space_name.length ? info.space_name : '入场' }}</view
      >
    </view>
    <view class="card-right">
      <view class="price"> <text>￥</text>{{ info.base_fee }}</view>
      <view class="ticket-btn" @tap="goDetail">立即购买</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useSpace } from '@/hooks/useSpace'
import { goUrlPage } from '@/utils'
const { handleSpaceName } = useSpace()
const props = defineProps({
  info: {
    type: Object as any,
  },
  busId: {
    type: String,
    default: '',
  },
})
function goDetail() {
  goUrlPage(`/pages/stadium/buyTicket?id=${props.info.id}&bus_id=${props.busId}`)
}
</script>
<style lang="scss" scoped>
.ticket-card {
  width: 693rpx;
  height: 230rpx;
  margin: 0 auto 26rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/ticket-bg.png');
  background-size: 100% 100%;
  .card-left {
    width: 468rpx;
    padding: 30rpx;
    box-sizing: border-box;
    .label {
      font-size: 32rpx;
      font-weight: bold;
      color: #313131;
      line-height: 36rpx;
    }
    .desc {
      font-size: 22rpx;
      color: #898989;
      line-height: 30rpx;
      margin-top: 20rpx;
    }
    .desc-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .price {
    color: $theme-text-color-other;
    font-size: 44rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
    text {
      font-size: 26rpx;
    }
  }
  .ticket-btn {
    width: 157rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: $theme-text-color-other;
    border-radius: 30rpx;
    color: #fff;
    font-size: 24rpx;
    font-weight: bold;
    text-align: center;
  }
  .card-right {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
</style>
