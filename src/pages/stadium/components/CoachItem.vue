<template>
  <view class="course theme-bg">
    <view class="left">
      <image class="light-shadow coach-avatar" :src="item.avatar" mode="aspectFill" />
      <view class="coach-info">
        <view class="name">{{ item.name }}</view>
        <view class="coach-classes">
          <view v-for="([start, end], index) in timeList" :key="index" class="class">{{ start }}-{{ end }}</view>
          <view v-if="!getMore && item.working_hours.length > 2" class="class" style="color: #333;" @tap="handleShowMore">更多空闲时间</view>
        </view>
      </view>
    </view>
    <view class="reserve">
      <!-- 付高旭说 true 就可以预约, 别看单词含义 -->
      <view v-if="timeList.length === 0" class="disabled">无空闲时间</view>
      <view v-else-if="item.is_clash" class="reserve-btn" data-info="{{item}}" @tap="selectItem(item)">预约</view>
      <view v-else class="disabled">时间冲突</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="PtClassListItem">
// import { arrIndexOf } from '@/utils/shared'
// import { useLogin } from '@/hooks/useLogin'
// import { goUrlPage } from '@/utils'
// const { checkLogin } = useLogin()

const props = defineProps<{
  config?: Record<string, any>
  item: Record<string, any>
  busId?: string
}>()
function selectItem(info) {
  uni.setStorageSync('select_coach_id', info.id);
  uni.setStorageSync('select_coach_name', info.name);
  uni.navigateBack()
}
// function goCoachDetail(info) {
//   goUrlPage(`/pages/coach/detail?coach_id=${info.coach_id}&bus_id=${props.busId || ''}`)
// }
const getMore = ref(false)
const timeList = computed(() => {
  if (getMore.value) {
    return props.item.working_hours
  } else {
    return props.item.working_hours.slice(0, 2)
  }
})
const handleShowMore = () => {
  const list = props.item.working_hours.map((item) => {
    const [start, end] = item
    return `${start} - ${end}`
  })
  uni.showModal({
    title: '教练名称: ' + props.item.name,
    content: '空闲时间\r\n' + list.join('\r\n'),
    showCancel: false,
    confirmText: '我知道了'
  })
}
</script>
<style lang="scss"></style>
