<template>
  <view class="myball-item theme-bg" @tap="goReserveDetail">
    <view class="item">
      <view class="myball-tit">{{ info.space_type_name }}</view>
      <view
        class="myball-tag"
        :class="info.status === 0 ? 'tag-num' : info.status === 2 ? 'tag-red' : info.status === 3 ? 'tag-gray' : ''"
        >{{
          info.status === 0 ? info.last_date : info.status === 1 ? '成功' : info.status === 2 ? '未成功' : '已取消'
        }}</view
      >
    </view>
    <view class="item">
      <view class="myball-des">
        <view class="myball-adress">
          {{ info.space_name }}
        </view>
        <text v-if="info.type === 1 && isInitiator && info.order_status === 0 && info.status === 0" class="pay-tag"
          >待支付</text
        >
      </view>

      <view class="myball-pay">
        {{ info.type === 2 ? '自行购票入场' : info.type === 1 && info.price_type === 1 ? '无需均摊' : '' }}
        <template v-if="info.type === 1 && info.price_type === 2">
          <text>（线下支付）</text>{{ info.aa_price }}元/人
        </template>
      </view>
    </view>
    <view class="item">
      <view class="des">{{ info.date }} {{ info.beg_date }}~{{ info.end_date }}</view>
      <view class="num-con"
        ><view>已报名/余位</view> <view class="num">{{ info.signup_num }}/{{ info.last_num }}</view></view
      >
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  info: {
    type: Object as any,
  },
  isInitiator: {
    type: Boolean,
    default: false,
  },
})
function goReserveDetail() {
  uni.navigateTo({
    url: `/pages/stadium/ballDetail?sports_mark_id=${props.info.sports_mark_id}`,
  })
}
function refreshPage() {
  uni.$emit('refresh-class-record')
}
</script>
<style lang="scss" scoped>
.myball-item {
  margin: 30rpx 30rpx 0;
  padding: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  font-size: 24rpx;
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15rpx;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .myball-tit {
    font-size: 32rpx;
    font-weight: bold;
    line-height: 50rpx;
  }
  .myball-tag {
    min-width: 80rpx;
    height: 30rpx;
    line-height: 30rpx;
    padding: 0 15rpx;
    background: var(--THEME-COLOR);
    border-radius: 15rpx;
    font-weight: bold;
    text-align: center;
    &.tag-num {
      background: #fff;
      border: 1px solid var(--THEME-COLOR);
    }
    &.tag-red {
      background: $theme-text-color-other;
    }
    &.tag-gray {
      background: #dedee0;
      color: #7d7d7d;
    }
  }
  .myball-des {
    display: flex;
    align-items: center;
  }
  .myball-adress {
    height: 32rpx;
    padding: 0 9rpx;
    background: rgba(255, 116, 39, 0.1);
    text-align: center;
  }
  .pay-tag {
    margin-left: 10rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #e83231;
  }
  .myball-pay {
    font-weight: bold;
    color: #ff7c34;
    text {
      font-weight: normal;
    }
  }
  .des {
    color: #313131;
    max-width: 370rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .num-con {
    display: flex;
    align-items: center;
    font-size: 20rpx;
    color: #7d7d7d;
    .num {
      margin-left: 10rpx;
      font-size: 30rpx;
      font-weight: bold;
      color: #000;
    }
  }
}
</style>
