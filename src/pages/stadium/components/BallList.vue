<template>
  <view class="content">
    <z-paging
      ref="paging"
      v-model="dataList"
      :show-loading-more-no-more-view="dataList.length > 10 ? true : false"
      :fixed="false"
      :auto="false"
      @query="loadList"
    >
      <template v-for="(item, index) in dataList" :key="index">
        <AboutBallItem v-if="tabIndex === 0" :info="item" />
        <MyBallItem v-else :info="item" :is-initiator="tabIndex === 1" />
      </template>
    </z-paging>
    <view class="fixed-bottom-wrap theme-bg">
      <button class="normal-btn" @tap="goInvite">发起邀约</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import AboutBallItem from './AboutBallItem.vue'
import MyBallItem from './MyBallItem.vue'
import { useLogin } from '@/hooks/useLogin'
const { checkLogin } = useLogin()
const userStore = useUserStore()
const paging = ref()
const dataList = ref([])
const firstLoaded = ref(false)
const props = defineProps({
  tabIndex: {
    type: Number,
    default: 0,
  },
  //当前swiper切换到第几个index
  currentIndex: {
    type: Number,
    default: 0,
  },
})
const waitFirst = reactive({
  class_waitting_id: '',
  class_name: '',
})
uni.$on('refresh-class-record', () => {
  paging.value.reload()
})
onShow(() => {
  if (firstLoaded.value) {
    paging.value.reload()
  }
})
onUnload(() => {
  uni.$off('refresh-class-record')
})
watchEffect(() => {
  const currentIndex = props.currentIndex
  if (currentIndex === props.tabIndex) {
    //懒加载，当滑动到当前的item时，才去加载
    if (!firstLoaded.value) {
      setTimeout(() => {
        paging.value.reload()
      }, 100)
    }
  }
})

async function loadList(pageNo, pageSize) {
  if (props.tabIndex === 0) {
    getSportsList(pageNo, pageSize)
  } else {
    if (!userStore.userInfoUserId) {
      await checkLogin()
    }
    http
      .get('/Sportsmark/mySportsList', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        scene: props.tabIndex,
        page_no: pageNo,
        page_size: pageSize,
      })
      .then((res) => {
        paging.value.complete(res.data.list)
        firstLoaded.value = true
      })
      .catch((res) => {
        //如果请求失败写paging.value.complete(false);
        //注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
        //在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
        paging.value.complete(false)
      })
  }
}
function getSportsList(pageNo, pageSize) {
  http
    .get('/Sportsmark/sportsList', {
      bus_id: userStore.userInfoBusId,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value && paging.value.complete(res.data.list)
      firstLoaded.value = true
    })
    .catch((res) => {
      paging.value && paging.value.complete(false)
    })
}
function goInvite() {
  uni.navigateTo({
    url: '/pages/stadium/toInvite',
  })
}
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  height: 100%;
  padding-bottom: 158rpx;
}
</style>
