<template>
  <view v-if="info && (info.type || info.type_name)" class="space-item" @tap="goDetail(info)">
    <template v-if="info.pic_url && info.pic_url.length > 0">
      <swiper style="width: 100%; height: 100%" indicator-dots autoplay="{{false}}">
        <block v-for="photo in info.pic_url" :key="photo">
          <swiper-item>
            <image class="category-bg" :src="photo"></image>
          </swiper-item>
        </block>
      </swiper>
    </template>
    <image
      v-else
      class="category-bg"
      mode="widthFix"
      src="https://imagecdn.rocketbird.cn/minprogram/member/image/category-1.png"
    />
    <view class="category-pin">
      {{ info.name || info.type_name }}
    </view>
    <view v-if="info.type" class="category-tag">
      <text v-if="info.type.includes('1')" class="tag">订场</text>
      <text v-if="info.type.includes('2')" class="tag">散场</text>
    </view>
  </view>
  <view v-if="info.list" class="name-list">
    <view v-for="(infoItem, index) in info.list" :key="index" :link="false">{{ infoItem.space_name }}</view>
  </view>
</template>

<script setup lang="ts">
import { goUrlPage } from '@/utils'

const props = withDefaults(
  defineProps<{
    info: Record<string, any>
    link?: boolean
    busId?: string
    inviteType?: number
  }>(),
  {
    link: true,
    busId: '',
    inviteType: 0, // 订场邀约功能的类型 1订场，2散场
  }
)
function goDetail(info) {
  if (props.inviteType) {
    if (props.inviteType === 2) {
      uni.setStorageSync('sanPost', props.info)
    }
    goUrlPage(
      props.inviteType === 1
        ? `/pages/stadium/choose?id=${info.id}&inviteType=${props.inviteType}&bus_id=${props.busId}`
        : `/pages/stadium/ballRelease?id=${info.id}&inviteType=${props.inviteType}&bus_id=${props.busId}`
    )
  } else if (props.link) {
    goUrlPage(`/pages/stadium/${info.san_type === 1 ? 'detail' : 'choose'}?id=${info.id}&bus_id=${props.busId}`)
  }
}
</script>
<style lang="scss" scoped>
.space-item {
  height: 260rpx;
  border-radius: 20rpx;
  margin: 30rpx auto 0;
  position: relative;

  .category-tag {
    position: absolute;
    right: 28rpx;
    top: 20rpx;

    .tag {
      display: inline-block;
      line-height: 30rpx;
      text-align: center;
      width: 70rpx;
      height: 30rpx;
      background: var(--THEME-COLOR);
      font-size: 20rpx;
      font-weight: bold;
      color: #000;
      margin-left: 10rpx;
    }
  }

  .category-bg {
    width: 100%;
    height: 260rpx;
    border-radius: 20rpx;
  }

  .category-pin {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    background: rgba($color: #000000, $alpha: 0.5);
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    font-size: 30rpx;
    font-weight: bold;
    color: #ffffff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-indent: 25rpx;
  }
}
.name-list {
  box-sizing: border-box;
  width: 690rpx;
  margin: 20rpx auto;
  padding: 20rpx 20rpx 0;
  background: #f6f6f8;
  overflow: hidden;

  font-size: 24rpx;
  view {
    margin-bottom: 20rpx;
    color: #000000;
    padding-left: 20rpx;
    position: relative;
    &::before {
      width: 6rpx;
      height: 6rpx;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      position: absolute;
      background: var(--THEME-COLOR);
      content: ' ';
      border-radius: 50%;
    }
  }
}
</style>
