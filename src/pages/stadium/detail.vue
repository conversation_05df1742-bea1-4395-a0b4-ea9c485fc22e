<template>
  <view>
    <view class="spaceitem-wrap">
      <SpaceItem :link="false" :info="spaceInfo" />
    </view>
    <view class="box-tit">预订场地</view>
    <view class="stadium-box theme-bg">
      <view class="lef">
        <view class="tit">共{{ spaceInfo.space_count }}块场地</view>
        <view
          >可允许 <text>{{ spaceInfo.max_join_people }}</text> 位成员一起入场</view
        >
      </view>
      <button class="normal-btn" @tap="goChoose">选场</button>
    </view>
    <view class="box-tit">散客购票</view>
    <view class="ticket-wrap">
      <TicketItem v-for="item in sanList" :key="item.id" :info="item" />
    </view>
  </view>
</template>

<script setup lang="ts" name="stadiumDetail">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
import SpaceItem from './components/SpaceItem'
import TicketItem from './components/TicketItem'

const userStore = useUserStore()
const { checkLogin } = useLogin()
const typeId = ref()
const spaceInfo = ref({})
const sanList = ref()
onLoad((options) => {
  typeId.value = options.id
})
onShow(() => {
  checkLogin(false).then(() => {
    getInfo()
  })
})
function getInfo() {
  http
    .get('/Santicket/getSpaceSanList', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      type_id: typeId.value,
    })
    .then((res) => {
      spaceInfo.value = res.data?.space
      sanList.value = res.data?.san
    })
}
function goChoose() {
  uni.navigateTo({
    url: `/pages/stadium/choose?id=${typeId.value}`,
  })
}
</script>

<style lang="scss" scoped>
.box-tit {
  margin: 10rpx 30rpx;
}
.stadium-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 690rpx;
  margin: 0 auto;
  height: 160rpx;
  padding: 30rpx;
  border: 1px solid var(--THEME-COLOR);
  border-radius: 20rpx;
  .tit {
    font-size: 32rpx;
    font-weight: bold;
    line-height: 30px;
  }
  text {
    font-weight: bold;
  }
  .normal-btn {
    width: 170rpx;
    height: 60rpx;
    border-radius: 10rpx;
    line-height: 60rpx;
    font-size: 26rpx;
  }
}
.spaceitem-wrap {
  margin: 0 30rpx;
}
</style>
