<template>
  <view class="box">
    <view class="date-switch theme-bg">
      <view
        v-for="item in dateList"
        :key="item.day"
        class="date"
        :class="today == item.date ? 'inset-shadow active' : ''"
        @tap="handleDayClick(item)"
      >
        <text class="week">{{ item.week }}</text>
        <text class="day">{{ item.day }}</text>
      </view>
    </view>
    <view class="filter">
      <view class="filter-left">
        <view class="filter-fold" @tap="handleFoldAll">
          全场
          <view v-if="!isFoldAll" class="down-arrow"></view>
          <view v-else class="left-arrow"></view>
        </view>
        <template v-if="!isFoldAll">
          <view
            v-for="(item, index) in allList"
            :key="index"
            class="fold-item"
            :class="item.id + '_' + item.position === selectedRoomId ? 'selected-room' : ''"
            @tap="handleRoom(item)"
          >
            <text class="label">{{ item.name }}</text>
            <view class="count">空闲{{ ableCount }}</view>
          </view>
        </template>
        <view v-if="halfList && halfList.length" class="filter-fold" @tap="handleFoldHalf">
          半场
          <view v-if="!isFoldHalf" class="down-arrow"></view>
          <view v-else class="left-arrow"></view>
        </view>
        <template v-if="!isFoldHalf">
          <view
            v-for="(item, index) in halfList"
            :key="index"
            class="fold-item"
            :class="item.id + '_' + item.position === selectedRoomId ? 'selected-room' : ''"
            @tap="handleRoom(item)"
          >
            <text class="label">{{ item.name }}</text>
            <view class="count">空闲{{ ableCount }}</view>
          </view>
        </template>
      </view>
      <view class="filter-right">
        <view
          v-for="(item, index) in timeList"
          :key="index"
          class="time-rect"
          :class="
            item.employ
              ? 'disabled-time'
              : selectedTimeBetween[0] <= index && index <= selectedTimeBetween[1]
              ? 'selected-time'
              : ''
          "
          @tap="handleTime(index, item.employ)"
        >
          <view class="time">{{ item.start_time }} ~ {{ item.end_time }}</view>
          <view class="price"><text class="line">|</text>￥{{ item.price }}</view>
        </view>
      </view>
      <view class="filter-bottom">
        <view class="price-box">
          <view class="price"><text class="label">合计:</text> ￥{{ price }}</view>
          <view class="count">
            <text class="count-project">已选 {{ count }} 项目</text>
            <text class="count-people">允许 {{ maxCapacity }} 位成员入场</text>
          </view>
        </view>
        <view class="btn normal-btn" @tap="handleSubmit">{{
          inviteType === 1 ? '发起邀约' : '支付'
        }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="stadiumChoose">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin.ts'
import { useNextDays } from '@/hooks/useNextDays'
import { formatDate } from '@/utils/shared'
const { today, dateList, setDay, setDateList } = useNextDays()
setDateList()
const userStore = useUserStore()
const { checkLogin } = useLogin()
const categoryId = ref('')
const allList = ref([])
const halfList = ref([])
const selectedRoomId = ref('')
const isFoldAll = ref(false)
const isFoldHalf = ref(false)
const selectedTimeBetween = ref([])
const timeList = ref([])
const price = ref(0)
const count = ref(0)
const maxCapacity = ref(0)
const ableCount = ref(0)
const inviteType = ref(0)
const cutOffTime = ref()
const ableLimitTime = ref()
const ableLimitDays = ref(0)
const timeStepList = [
  '07:00',
  '07:30',
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
]
onLoad((options) => {
  categoryId.value = options.id || ''
  inviteType.value = +(options.inviteType || 0)
})
onShow(() => {
  count.value = 0
  selectedTimeBetween.value = []
  price.value = 0
  getRoomList()
})
function handleFoldAll() {
  isFoldAll.value = !isFoldAll.value
}
function handleFoldHalf() {
  isFoldHalf.value = !isFoldHalf.value
}

function handleTime(index, isDisabled) {
  if (isDisabled === 1) {
    return
  }
  if (selectedTimeBetween.value.length === 0) {
    selectedTimeBetween.value = [index, index]
  } else if (selectedTimeBetween.value[0] === index && selectedTimeBetween.value[1] === index) {
    selectedTimeBetween.value = []
  } else if (selectedTimeBetween.value[0] - 1 === index) {
    selectedTimeBetween.value[0] = index
    if (!checkTimeSmooth()) {
      uni.showToast({ title: '请选择连续时间', icon: 'none' })
      selectedTimeBetween.value[0] = index + 1
    }
  } else if (selectedTimeBetween.value[1] + 1 === index) {
    selectedTimeBetween.value[1] = index
    if (!checkTimeSmooth()) {
      uni.showToast({ title: '请选择连续时间', icon: 'none' })
      selectedTimeBetween.value[1] = index - 1
    }
  } else if (selectedTimeBetween.value[0] === index) {
    selectedTimeBetween.value[0] = index + 1
  } else if (selectedTimeBetween.value[1] === index) {
    selectedTimeBetween.value[1] = index - 1
  }

  if (selectedTimeBetween.value.length === 2) {
    count.value = selectedTimeBetween.value[1] - selectedTimeBetween.value[0] + 1
  } else {
    count.value = 0
  }
  let curPrice = 0
  for (let index = selectedTimeBetween.value[0]; index <= selectedTimeBetween.value[1]; index++) {
    const time = timeList.value[index]
    curPrice = Number(time.price) + curPrice
  }
  price.value = parseFloat(curPrice).toFixed(2)
}

function checkDateAvailable() {
  return http
    .post('/Booking/checkSpaceSet', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      date: today.value,
    })
    .then((res) => {
      return true
    })
    .catch(() => {
      return false
    })
}
async function handleSubmit() {
  // 可提前预约天数 ableLimitDays
  const currentDate = new Date()
  const limitDate = new Date(currentDate.setHours(0, 0, 0, 0) + ableLimitDays.value * 24 * 3600 * 1000)
  const clickDateTimestamp = new Date(today.value).setHours(0, 0, 0, 0)
  const limitDateTimestamp = limitDate.getTime()
  if (clickDateTimestamp > limitDateTimestamp) {
    let title = `最早允许提前${ableLimitDays.value}天预定`
    if (ableLimitDays.value === 0) {
      title = '仅可预约当天'
    }
    uni.showToast({
      title,
      icon: 'none',
      duration: 2500,
    })
    return
  }

  if (!userStore.userInfoUserId) {
    await checkLogin()
  }

  if (count.value === 0) {
    uni.showToast({ title: '请选择订场时间', icon: 'none' })
    return
  }
  const flag = await checkDateAvailable()
  if (!flag) {
    return
  }

  let room = null
  let space_name = ''
  const idPosition = selectedRoomId.value.split('_')
  const space_id = idPosition[0]
  const position = idPosition[1]
  if (position === '0') {
    room = allList.value.find((item) => item.id == space_id)
  } else {
    room = halfList.value.find((item) => item.id == space_id && item.position == position)
  }
  space_name = room.name
  const schedules = []
  const start = selectedTimeBetween.value[0]
  const end = selectedTimeBetween.value[1]
  for (let index = start; index <= end; index++) {
    const time = timeList.value[index]
    schedules.push({
      start_time: time.start_time,
      end_time: time.end_time,
    })
  }

  const startTime = timeList.value[start].start_time
  const endTime = timeList.value[end].end_time

  const startIndex = timeStepList.findIndex((time) => time === startTime)
  const endIndex = timeStepList.findIndex((time) => time === endTime)

  if ((endIndex - startIndex) / 2 > room.max_hour) {
    uni.showToast({ title: `最大订场时间${room.max_hour}个小时!`, icon: 'none' })
    return
  }

  const max_join_people = room.max_join_people

  const params = {
    space_id,
    space_name,
    space_type_id: categoryId.value,
    date: today.value,
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    card_user_id: '',
    is_half: position === '0' ? 0 : 1,
    position: position,
    pay_type: '',
    schedules,
    amount: price.value,
    datetime: startTime + '-' + endTime,
    max_join_people,
  }

  uni.setStorageSync('sbPost', params) // 糟糕的设计 最开始的订场后端在支付页面没有详情接口 只有从前面缓存取
  if (inviteType.value) {
    const time = formatDate(
      new Date(Date.now() + ableLimitTime.value * 3600 * 1000),
      'yyyy-MM-dd HH:ss'
    )
    const chooseTime = formatDate(
      new Date(`${today.value.replace(/-/g, '/')} ${startTime}`),
      'yyyy-MM-dd HH:ss'
    )
    if (chooseTime < time) {
      uni.showToast({
        title: `仅能邀约未来${ableLimitTime.value}小时后的场次`,
        icon: 'none',
        duration: 2500,
      })
      return
    }
    uni.navigateTo({
      url: `/pages/stadium/ballRelease?id=${categoryId.value}&inviteType=${inviteType.value}&mark_group_cutoff_time=${cutOffTime.value}`,
    })
  } else {
    uni.navigateTo({ url: '/pages/stadium/buySpace' })
  }
}

function checkTimeSmooth() {
  if (selectedTimeBetween.value.length !== 2) {
    return false
  }
  let flag = true
  let lastEndTime = timeList.value[selectedTimeBetween.value[0]].start_time
  for (let index = selectedTimeBetween.value[0]; index <= selectedTimeBetween.value[1]; index++) {
    const time = timeList.value[index]
    if (time.start_time !== lastEndTime) {
      flag = false
    }
    lastEndTime = time.end_time
  }
  return flag
}
function getInfo() {
  count.value = 0
  price.value = 0
  getRoomList()
}
function getRoomList() {
  http
    .get('/Booking/getSpaceScheduleList', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      type_id: categoryId.value,
      date: today.value,
    })
    .then((res) => {
      allList.value = res.data.all
      halfList.value = res.data.half
      ableLimitTime.value = res.data.able_mark_limit_time
      ableLimitDays.value = Number(res.data.booking_future_days || 0)
      cutOffTime.value = res.data.mark_group_cutoff_time
      if(selectedRoomId.value) {
         const idPosition = selectedRoomId.value.split('_')
        getSpaceScheduleDetail({
          id: idPosition[0],
          position: idPosition[1]
        })
      } else if (Array.isArray(allList.value) && allList.value.length > 0) {
        getSpaceScheduleDetail(allList.value[0])
      } else if (Array.isArray(halfList.value) && halfList.value.length > 0) {
        getSpaceScheduleDetail(halfList.value[0])
      }
    })
}
function getSpaceScheduleDetail(info: { id: number; position: number, [key: string]: any }) {
  return http
    .get('/Booking/getSpaceScheduleDetail', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      type_id: categoryId.value,
      date: today.value,
      space_id: info.id,
      position: info.position,
    })
    .then((res) => {
      setResData(res.data)
      return res.data
    })
}

async function handleRoom(info) {
  selectedRoomId.value = info.id + '_' + info.position
  await getSpaceScheduleDetail(info)
}
function setResData(info) {
  selectedRoomId.value = info.id + '_' + info.position
  timeList.value = info.schedule_price
  selectedTimeBetween.value = []
  maxCapacity.value = info.max_join_people
  ableCount.value = info.able_count
}
const handleDayClick = (item) => {
  setDay(item, getInfo)
}
</script>

<style lang="scss" scoped>
.down-arrow,
.left-arrow {
  border-color: $theme-text-color-other transparent;
  border-style: solid;
  border-width: 8rpx 8rpx 0 8rpx;
  height: 0;
  width: 0;
}

.left-arrow {
  transform: rotate(90deg);
}
.filter {
  display: flex;
  flex-direction: row;
  position: absolute;
  top: 166rpx;
  bottom: 120rpx;
  left: 0;

  .filter-left {
    width: 260rpx;
    background-color: #fff;
    overflow: auto;

    .filter-fold {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      font-weight: bold;
      color: #1b1b1b;
      line-height: 36rpx;
      padding: 30rpx;
    }

    .fold-item {
      position: relative;
      font-size: 24rpx;
      color: #1b1b1b;
      line-height: 36rpx;
      height: 80rpx;
      padding: 0 16rpx 0 24rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .label {
        // width: 150rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .count {
        display: none;
        position: absolute;
        top: 4rpx;
        right: 16rpx;
        padding: 2rpx 3rpx;
        line-height: 1;
        text-align: center;
        font-size: 18rpx;
        font-weight: bold;
        color: #fff;
        background-color: $theme-text-color-other;
        border-radius: 4rpx;
      }
    }

    .selected-room {
      border-left: 6rpx solid var(--THEME-COLOR);
      background-color: rgba(var(--THEME-RGB), 0.15);
      color: #000;
      .count {
        display: block;
      }
    }
  }

  .filter-right {
    width: 490rpx;
    overflow: auto;

    .time-rect {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      font-weight: bold;
      color: #1b1b1b;
      line-height: 36rpx;
      width: 390rpx;
      height: 80rpx;
      background: #ffffff;
      border: 1rpx solid #e7e7e7;
      border-radius: 10rpx;
      margin: 20rpx auto;
      padding: 0 20rpx;

      .line {
        color: #e7e7e7;
        margin-right: 14rpx;
      }
    }
    .disabled-time {
      background-color: #dedede;
      color: $theme-text-color-grey;
    }
    .selected-time {
      border: 2rpx solid var(--THEME-COLOR);

      .line {
        color: var(--THEME-COLOR);
      }
    }
  }

  .filter-bottom {
    position: fixed;
    bottom: 0;
    width: 690rpx;
    height: 120rpx;
    padding: 0 30rpx;
    background-color: white;
    box-shadow: 0px -4px 8px 0px rgba(27, 27, 27, 0.1);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .price-box {
      font-size: 36rpx;
      color: #ca2e53;

      .price {
        font-size: 36rpx;
        color: $theme-text-color-other;

        .label {
          font-size: 24rpx;
          font-weight: bold;
          color: #1b1b1b;
          line-height: 36rpx;
        }
      }

      .count {
        color: #000;
        height: 30rpx;
        display: flex;
        align-items: center;
        font-size: 22rpx;

        .count-people {
          margin-left: 20rpx;
          padding-left: 20rpx;
          border-left: 1px solid #e8e8e8;
        }
      }
    }

    .btn {
      width: 296rpx;
    }
  }
}
</style>
