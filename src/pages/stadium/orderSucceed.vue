<template>
  <view class="footer-hasfixed">
    <view class="success-tips">
      <image class="icon icon-mr" src="/static/img/success.png" />
      <text>支付成功</text>
    </view>
    <view v-if="order" class="form-items theme-bg">
      <view class="item">
        <view class="label">订单编号</view>
        <view class="value">{{ order.order_sn }}</view>
      </view>
      <view class="item noborder">
        <view class="label">时间</view>
        <view class="value">{{ order.validity_time }} <text v-if="order.ticket_type == 1">前可核验</text></view>
      </view>
      <view class="item noborder">
        <view class="label">场地</view>
        <view class="value" @tap="handleSpaceName(sanId, order.space_name)">
          <view class="value-overtext">{{
            order.space_name && order.space_name.length ? order.space_name : '入场'
          }}</view>
          <view class="arrow-right"></view>
        </view>
      </view>
      <view class="item noborder">
        <view class="label">入场人数</view>
        <view class="value">{{ isSan ? 1 : order.max_join_people }}人</view>
      </view>

      <view v-if="adUnitId" class="banner">
        <image class="banner-img" :src="adUnitId" alt="banner" />
        <!-- <ad class="banner-img" :unit-id="adUnitId"></ad> -->
      </view>

      <view class="qrcode">
        <view class="subtitle">请凭二维码进场签到</view>
        <view class="code">{{ consumeSn }}</view>
        <image :class="order.valid == 1 ? 'picture' : 'pic-false'" :src="order.voucher_image" />
        <view v-if="order.valid == 0" class="text-false">已失效</view>
        <button
          v-if="!isSan && order.max_join_people > 1 && order.exist_sports_mark !== 1"
          open-type="share"
          class="normal-btn friend normal-btn-min"
        >
          邀请成员领取进场凭证
        </button>
      </view>
    </view>
    <view v-if="order" class="fixed-bottom-wrap theme-bg">
      <navigator class="normal-btn" open-type="redirect" url="/pages/my/ticket">完成</navigator>
    </view>
  </view>
</template>

<script setup lang="ts" name="orderSucceed">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useSpace } from '@/hooks/useSpace'
const { handleSpaceName } = useSpace()

const userStore = useUserStore()
const isSan = ref(false)
const sanId = ref()
const consumeSn = ref('')
const orderSn = ref()
const order = ref()
onLoad((options) => {
  orderSn.value = options.orderSn ? options.orderSn : ''
  sanId.value = options.sanId ? options.sanId : ''
})
// 解决英东等场馆购票后点右上角退出小程序去开通刷掌，扫刷掌二维码跳转到刷掌授权页面时会触发这里的onunload跳转到我的页面
const hasHideFlag = ref(false)
onHide(() => {
  hasHideFlag.value = true
})
onUnload(() => {
  if (!hasHideFlag.value) {
    uni.switchTab({
      url: '/pages/my/index',
    })
  }
})
onShow(() => {
  getOrder()
})

function getOrder() {
  http
    .get('/Santicket/getVoucher', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      san_log_id: sanId.value,
      order_sn: orderSn.value,
    })
    .then((res) => {
      order.value = res.data
      isSan.value = res.data.ticket_type == 1
      const sn = res.data.consume_sn
      consumeSn.value = `${sn.substring(0, 4)}  ${sn.substring(4, 8)}  ${sn.substring(8, 12)}`
    })
}

// get banner unit-id
const adUnitId = ref('')
const getAdUnitId = () => {
  http
    .post('/Banner/getList', {
      bus_id: userStore.userInfoBusId,
    })
    .then((res) => {
      if (Array.isArray(res.data)) {
        const list = res.data.map((item) => item.link)
        const randomNumber = Math.floor(Math.random() * list.length)
        adUnitId.value = list[randomNumber]
      }
    })
}

getAdUnitId()

onShareAppMessage((options) => {
  const userInfo = uni.getStorageSync('userInfo')
  return {
    title: `${userInfo?.username || ''} 邀请你领取入场凭证`,
    path: `/pages/my/ticketDetail?orderSn=${orderSn.value}&fromShare=1&bus_id=${userStore.userInfoBusId}`,
    imageUrl: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/share-ticket.png',
  }
})
</script>

<style lang="scss" scoped>
.banner {
  height: 180rpx;
  width: 670rpx;
  margin: 70rpx 0 80rpx -10rpx;
  .banner-img {
    width: 100%;
    height: 100%;
  }
}

.success-tips {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-weight: bold;
  color: #000;
}
.qrcode {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 60rpx;
  position: relative;

  .subtitle {
    font-size: 30rpx;
    font-weight: bold;
    color: #313131;
  }
  .normal-btn-min {
    width: 350rpx;
    margin-bottom: 50rpx;
  }

  .code {
    margin-top: 40rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #03080e;
  }

  .picture {
    width: 375rpx;
    height: 375rpx;
    margin-top: 30rpx;
    margin-bottom: 60rpx;
  }

  .pic-false {
    width: 375rpx;
    height: 375rpx;
    margin-top: 30rpx;
    margin-bottom: 60rpx;
    opacity: 0.2;
  }

  .text-false {
    position: absolute;
    right: 130rpx;
    top: 160rpx;
    transform: rotate(-30deg);
    font-size: 24rpx;
    font-weight: bold;
    color: #ca2e53;
    border-radius: 50%;
    border: 1rpx solid #ca2e53;
    width: 100rpx;
    height: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .label {
    display: flex;
    justify-content: center;
    align-items: center;
    letter-spacing: 4rpx;
    margin: 30rpx;
    width: 330rpx;
    height: 80rpx;
    background: #e7e7e7;
    border-radius: 40rpx;
    font-size: 26rpx;
    font-weight: bold;
    color: #b3b3b3;
  }

  .btn-height {
    margin: 30rpx 0 120rpx 0;
    width: 330rpx;
    height: 80rpx;
  }
}
</style>
