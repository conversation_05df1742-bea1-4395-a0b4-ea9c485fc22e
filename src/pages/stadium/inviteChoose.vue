<template>
  <view class="invite-wrap theme-bg">
    <SpaceList v-if="inviteType" :invite-type="inviteType" />
  </view>
</template>
<script setup lang="ts">
import SpaceList from '@/pages/class/components/SpaceList.vue'
const inviteType = ref(0) // 参与方式 1订场，2散场
onLoad((options) => {
  inviteType.value = +(options.inviteType || 1)
})
</script>

<style lang="scss" scoped>
.invite-wrap {
  height: 100%;
  overflow: hidden;
}
</style>
