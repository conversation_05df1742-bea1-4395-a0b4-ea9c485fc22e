import { defineStore } from 'pinia'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'

export const useMerchant = defineStore('merchant', {
  state: () => {
    return {
      busList: [],
      isRequesting: false,
      userInfo: {
        bus_id: '',
        bus_name: '',
        user_id: '',
      },
    }
  },
  getters: {
    userInfoBusId: (state) => state.userInfo.bus_id,
    userInfoUserId: (state) => state.userInfo.user_id,
    getBusList: (state) => {
      return state.busList
    },
    getHasUserIdBusList: (state) => {
      return state.busList.filter((item) => item.user_id)
    },
  },
  actions: {
    async getUserIdByBus(busId, isNeedBusName?: boolean) {
      if (!this.busList.length) {
        await this.requestBusList()
      }
      const list = this.busList.find((item) => item.bus_id === busId)
      const id = list?.user_id || ''
      if (isNeedBusName) {
        return {
          user_id: id,
          bus_name: list?.bus_name || '',
        }
      }
      return id
    },
    setBusId(data) {
      this.userInfo.bus_id = data
    },
    setUserId(data) {
      this.userInfo.user_id = data
    },
    setMerchantUserInfo({ bus_id, user_id, bus_name }) {
      this.userInfo.bus_id = bus_id || ''
      this.userInfo.bus_name = bus_name || ''
      this.userInfo.user_id = user_id || ''
    },
    async requestBusList() {
      const themeStore = useThemeStore()
      if (this.isRequesting || !themeStore.isEntranceReady) {
        // 如果正在进行请求，则等待第一次请求完成后再执行后续的逻辑
        await new Promise((resolve) => {
          const interval = setInterval(() => {
            if (!this.isRequesting && themeStore.isEntranceReady) {
              clearInterval(interval)
              resolve()
            }
          }, 100)
        })
      }
      if (this.busList.length) {
        return Promise.resolve(this.busList)
      }
      const userStore = useUserStore()
      // 未登录状态下的用户请求商家场馆列表 不会返回带有用户信息的场馆列表  所以直接返回空数组
      if (!userStore.userInfoUserId) {
        return Promise.resolve([])
      }
      this.isRequesting = true
      return http
        .get('/Public/getMerBusList', {
          user_id: userStore.userInfoUserId,
        })
        .then((res) => {
          const resData = res.data
          this.busList = resData?.list || []
          this.isRequesting = false
          return resData
        })
        .catch(() => {
          this.isRequesting = false
        })
    },
  },
})
