import { defineStore } from 'pinia'
import { useMerchant } from '@/store/merchant'

export const useUserStore = defineStore('user', {
  persist: {
    // 开启持久化
    enabled: true,
  },
  state: () => {
    return {
      lastMyPageBusId: '', //上一次进入我的页面的时候保存的bus_id 防止在我的页面下级子页面切换了本地场馆id后导致首页等页面场馆切换
      userInfo: {
        m_id: '',
        merchant_user_id: '',
        bus_id: '',
        merchant_name: '',
        bus_name: '',
        is_display_self_qr: 0,
        user_id: '',
        openid: '',
        unionid: '',
        phone: '',
      },
      launchBusId: '', //本次启动小程序时带的bus_id
      locationInfo: {
        longitude: '',
        latitude: '',
      },
      // 扫码等带scene信息进入的场景 注意scene和params要一起设置 否则会造成scene和params不匹配
      sceneInfo: {
        scene: '',
        params: null,
      },
      showLocationDialog: false, // 是否显示获取位置权限弹窗
      isDeniedLocation: false, // 是否拒绝了获取位置权限
    } as {
      lastMyPageBusId: string
      userInfo: User.UserInfo
      locationInfo: User.locationInfo
      sceneInfo: User.sceneInfo
      showLocationDialog: boolean
      isDeniedLocation: boolean
    }
  },
  getters: {
    userInfoMId: (state) => state.userInfo.m_id,
    userInfoBusId: (state) => state.userInfo.bus_id,
    userInfoUserId: (state) => state.userInfo.user_id,
    userInfoMerchantUserId: (state) => state.userInfo.merchant_user_id,
  },
  actions: {
    setSceneInfo(data: User.sceneInfo) {
      this.sceneInfo = data
    },
    getSceneInfoByScene(scene: string): User.sceneInfo['params'] | null {
      if (scene && this.sceneInfo.scene && scene === this.sceneInfo.scene) {
        return this.sceneInfo.params
      }
      return null
    },
    setLaunchBusId(data) {
      this.launchBusId = data
    },
    setBusName(data) {
      this.userInfo.bus_name = data
    },
    setUserId(data) {
      this.userInfo.user_id = data
    },
    setBusId(data) {
      this.userInfo.bus_id = data
    },
    setMId(data) {
      this.userInfo.m_id = data
    },
    setMerchantUserId(data) {
      this.userInfo.merchant_user_id = data
    },
    setUserInfo(userInfo) {
      const { bus_name, user_id, m_id } = { ...userInfo }
      if (bus_name) {
        this.setBusName(bus_name)
      }
      if (m_id) {
        this.setMId(m_id)
      }
      if (bus_name) {
        this.setBusName(bus_name)
      }
      delete userInfo.bus_name
      delete userInfo.m_id
      const useMerchantStore = useMerchant()
      Object.assign(this.userInfo, userInfo)
      if (!useMerchantStore.userInfoBusId && !useMerchantStore.userInfoUserId) {
        useMerchantStore.setUserId(user_id)
      }
    },
    async getLocationInfo(intuitive = false) {
      const authSetting = await uni.getSetting()
      if (authSetting.authSetting['scope.userLocation'] === undefined) {
        this.setIsDeniedLocation(false)
        this.setShowLocationDialog(true)
        
        if (!intuitive) {
          return Promise.resolve({
            longitude: '',
            latitude: '',
          })
        }
      } else if (authSetting.authSetting['scope.userLocation'] === false) {
        this.setIsDeniedLocation(true)
        this.setShowLocationDialog(true)
      } else {
        this.setIsDeniedLocation(false)
        this.setShowLocationDialog(false)
      }

      try {
        const info = await uni.getLocation({ type: 'gcj02' })
        this.locationInfo = info
        return Promise.resolve(info)
      } catch (error) {
        this.setIsDeniedLocation(true)
        this.locationInfo = {
          longitude: '',
          latitude: '',
        }
        return Promise.resolve({
          longitude: '',
          latitude: '',
        })
      }
    },
    setLocationInfo(data) {
      this.locationInfo = data
    },
    getShowLocationDialog() {
      return this.showLocationDialog
    },
    setShowLocationDialog(data) {
      this.showLocationDialog = data
    },
    getIsDeniedLocation() {
      return this.isDeniedLocation
    },
    setIsDeniedLocation(data) {
      this.isDeniedLocation = data
    },
  },
})
