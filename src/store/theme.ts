import { defineStore } from 'pinia'
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { isTabBarPage } from '@/utils/urlMap'

export const useThemeStore = defineStore('theme', {
  state: (): Theme.ThemeState => {
    return {
      isEntranceReady: false,
      isConfigReady: false,
      isRequesting: false,
      tabBarBus: '',
      lastPostData: {},
      isShowMerchantMode: undefined,
      operationMode: undefined,
      allThemeObj: {}, // 保存获取到的所有自定义配置， 当需要获取时根据类型从中获取（不一次性赋值的原因是防止商家模式下场馆频繁切换时赋值过后造成的在后台中的页面的不断重复渲染）
      // 1外观颜色 2首页装修 3预约页面装修 4我的页面装修 5底部导航 6登录页 7教练称谓 8卡课列表 9运营模式 10商城配置
      theme1: {
        background_color: 1,
        fashion_color: 'a1ea2b',
        member_open: 1, // 场馆过期或者闭店时候禁止使用（适用于闭店、过期或者门店装修期间等异常 让会员无法使用本门店会员端）
      },
      theme2: {
        setting: {
          enter_voucher: '1',
          toggle_bus: '1',
        },
        list: [],
      },
      theme3: [],
      theme4: {
        background_img: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/banner.png',
        background_type: '1',
        display: 1,
      },
      theme5: [
        {
          name: '首页',
          temp_type: '1',
          display: 1,
          iconPath: '/static/img/home.png',
          selectedIconPath: '/static/img/home-selected-dark.png',
        },
        {
          name: '约课',
          temp_type: '2',
          display: 1,
          iconPath: '/static/img/reserve.png',
          selectedIconPath: '/static/img/reserve-selected-dark.png',
        },
        {
          name: '商城',
          temp_type: '3',
          display: 1,
          iconPath: '/static/img/mall.png',
          selectedIconPath: '/static/img/mall-selected-dark.png',
        },
        {
          name: '我的',
          temp_type: '4',
          display: 1,
          iconPath: '/static/img/my.png',
          selectedIconPath: '/static/img/my-selected-dark.png',
        },
      ],
      theme6: {
        background_type: '1',
        bus_logo: '',
        background_img: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg',
      },
      theme7: {
        appellation: '教练',
      },
      // 卡课列表页面装修
      theme8: {
        setting: {
          enter_voucher: '1',
          toggle_bus: '1',
        },
        list: [],
      },
      // 商城配置
      theme10: {
        bus_id: '',
      },
      // 进店指引
      theme11: {
        image_url: '',
      },
    }
  },
  actions: {
    setIsEntranceReady(ready: boolean) {
      this.isEntranceReady = ready
    },
    setOperationMode(operationMode) {
      this.operationMode = operationMode
    },
    // 切换当前展示模式 只有在运营模式为综合体育场馆模式时才会生效
    async changeShowMerchantMode(isShow = true) {
      if (!this.isEntranceReady) {
        await new Promise((resolve) => {
          const interval = setInterval(() => {
            if (this.isEntranceReady) {
              clearInterval(interval)
              resolve()
            }
          }, 100)
        })
      }
      if (this.isShowMerchantMode !== isShow) {
        this.isShowMerchantMode = this.operationMode === 1 ? isShow : false
      }
      // 重新获取基础配置
      await this.getConfig({ type: 0 })
    },
    setThemeConfigByType(info, type: Number = 1) {
      // 首页和预约页面、卡课列表
      if ([2,8].includes(type)) {
        info.list = info.list.filter((item) => item.isHide !== 'true' && item.isHide !== true)
      }
      if (type === 3) {
        info = info.filter((item) => item.isHide !== 'true' && item.isHide !== true)
      }
      // info.background_color = 2 // 暗黑
      if (type === 1) {
        Object.assign(this['theme' + type], {
          ...info,
          fashion_color: info?.fashion_color || 'a1ea2b',
        })
        this.setThemeStyle('NavigationBar')
      } else {
        this['theme' + type] = info
        this.setThemeStyle('TabBar')
      }
    },
    setTypeConfigByAll(type: number) {
      const info = this.allThemeObj
      const keyMap = {
        1: {
          background_color: info.background_color,
          fashion_color: info.fashion_color,
        },
        2: 'home_decorate',
        3: 'appointment_decorate',
        4: 'mine_decorate',
        5: 'bottom_navigation',
        6: 'login_decorate',
        7: 'coach_appellation',
        8: 'card_class_list',
        10: 'mall',
        11: 'entry_guide',
      }
      this.setThemeConfigByType(type === 1 ? keyMap[1] : info[keyMap[type]], type)
    },
    setThemeStyle(type) {
      const color = this.theme1.fashion_color || 'a1ea2b'
      const theme = this.theme1.background_color || 1
      //tabBar从左到右
      const tabKeyArr = ['home', 'reserve', 'my']
      tabKeyArr.forEach((item, index) => {
        if (type === 'TabBar' && isTabBarPage()) {
          // uni.setTabBarItem({
          //   index: index,
          //   selectedIconPath: `https://imagecdn.rocketbird.cn/minprogram/uni-member/theme/tabicons/${color}/${item}-selected${
          //     theme == 2 ? '' : '-dark'
          //   }.png`,
          // })
          // uni.setTabBarStyle({
          //   color: theme == 2 ? '#7d7d7d' : '#0E0E0E',
          //   selectedColor: theme == 2 ? '#ffffff' : '#0E0E0E',
          //   backgroundColor: theme == 2 ? '#000000' : '#ffffff',
          //   borderStyle: theme == 2 ? 'white' : 'black',
          // })
        } else {
          uni.setNavigationBarColor({
            frontColor: theme === 2 ? '#ffffff' : '#000000',
            backgroundColor: theme === 2 ? '#000000' : '#ffffff',
          })
        }
      })
    },
    checkMemberOpen(info) {
      if(!info) return
      // 场馆过期或者闭店时候禁止使用（适用于闭店、过期或者门店装修期间等异常 让会员无法使用本门店会员端）
      if (info.member_open === 0) {
        const userStore = useUserStore()
        userStore.setUserId('')
        uni.reLaunch({
          url: '/pages/busSelect',
        })
      }
    },
    // 在商家模式下 需要明确获取某个场馆的部分配置时需要传bus_id
    async getConfig({ type = 1, bus_id }: { type: number; bus_id?: string }) {
      const userStore = useUserStore()
      if (type === 0) {
        this.isConfigReady = false
        const resData = await this.requestConfig({ type })
        this.allThemeObj = resData
        this.isConfigReady = true
        // 每次获取全体配置时都主动更新tabbar配置与主题色
        this.setTypeConfigByAll(1)
        this.setTypeConfigByAll(5)
      } else {
        if (bus_id) {
          const resData = await this.requestConfig({ type, bus_id })
          this.setThemeConfigByType(resData, type)
          return this['theme' + type]
        }
        if (!this.isConfigReady) {
          await new Promise((resolve) => {
            const interval = setInterval(() => {
              if (this.isConfigReady) {
                clearInterval(interval)
                resolve()
              }
            }, 100)
          })
        }
        this.setTypeConfigByAll(type)
        return this['theme' + type]
      }
    },
    async requestConfig({ type = 1, bus_id }: { type: number; bus_id?: string }) {
      const userStore = useUserStore()
      const userInfoBusId = userStore.userInfoBusId
      const userInfoMId = userStore.userInfoMId
      let postData = {
        bus_id: bus_id || userInfoBusId,
        type,
      }
      if (this.isShowMerchantMode && !bus_id) {
        postData = {
          merchant_type_get: true,
          m_id: userInfoMId,
          bus_id: userInfoBusId, // 用以判断当前本地的场馆是否闭店，后端会返回对应场馆的闭店标识
          type,
        }
      }
      if (this.isRequesting && JSON.stringify(this.lastPostData) === JSON.stringify(postData)) {
        await new Promise((resolve) => {
          const interval = setInterval(() => {
            if (!this.isRequesting) {
              clearInterval(interval)
              resolve()
            }
          }, 100)
        })
        return type === 0 ? this.allThemeObj : this['theme' + type]
      }
      this.lastPostData = postData
      this.isRequesting = true
      return http
        .get('/Template/getConfig', postData)
        .then((res) => {
          this.isRequesting = false
          if (res.data.bus_name) {
            userStore.setBusName(res.data.bus_name)
          }
          this.checkMemberOpen(res.data)
          return res.data
        })
        .catch(() => {
          this.isRequesting = false
          return {}
        })
    },
    getRGB(): Theme.rgbInfo {
      let sColor = '#' + (this.theme1.fashion_color || 'a1ea2b').toLowerCase()
      //十六进制颜色值的正则表达式
      const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
      // 如果是16进制颜色
      if (sColor && reg.test(sColor)) {
        if (sColor.length === 4) {
          let sColorNew = '#'
          for (let i = 1; i < 4; i += 1) {
            sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
          }
          sColor = sColorNew
        }
        //处理六位的颜色值
        const sColorChange = []
        for (let i = 1; i < 7; i += 2) {
          sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
        }
        return {
          r: sColorChange[0],
          g: sColorChange[1],
          b: sColorChange[2],
        }
      }
    },
  },
})
