<script setup lang="ts">
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import { useLogin } from '@/hooks/useLogin'

const themeStore = useThemeStore()
const userStore = useUserStore()
  
const { appInit, getParam } = useLogin()

onLaunch(() => {
  console.log('App onLaunch')
})
onShow(async (options) => {
  console.log('App onShow')
  // 每次重新进入小程序 重置保存的上一次进入我的页面的时候保存的bus_id
  userStore.lastMyPageBusId = ''
  userStore.setLaunchBusId(options.query?.bus_id || '')
  if(options.query?.scene && options.path==='pages/index/index') {
    const curParams = await getParam(options.query?.scene, options.path)
    userStore.setLaunchBusId(curParams.bus_id || '')
  }
  appInit()
  // 邀请有礼活动id 和 邀请人id, 将在注册、登录、检测登录后清除
  const { activity_id, inviter_user_id } = options.query
  if (activity_id && inviter_user_id) {
    uni.setStorageSync('activity_id', activity_id)
    uni.setStorageSync('inviter_user_id', inviter_user_id)
  }
  // 微信分期付返回场景
  payscoreScene(options)
  // 处理电子签人脸返回场景
  esignReturnScene(options)
})
onHide(() => {
  console.log('App Hide')
  // 电子签人脸信息
  uni.removeStorageSync('esignFaceReturnInfo')
})

// payscore return scene
const payscoreScene = (options: any) => {
  if (options?.scene === 1038) {
    // 场景值1038：从被打开的小程序返回
    const { appId, extraData } = options.referrerInfo as any
    if (appId == 'wxbd687630cd02ce1d') {
      // appId为wxbd687630cd02ce1d：从签约小程序跳转回来
      if (typeof extraData == 'undefined') {
        // 客户端小程序不确定签约结果，需要向商户侧后台请求确定签约结果
      }
      if (extraData.return_code == 'SUCCESS') {
        // 客户端小程序签约成功，需要向商户侧后台请求确认签约结果
        uni.navigateTo({
          url: '/pages/payscore/succeed?flag=1',
        })
      } else {
        // 签约失败
        uni.navigateTo({
          url: '/pages/payscore/succeed?flag=0&id=' + options.query.id,
        })
      }
    }
  }
}
const esignReturnScene = (options: any) => {
  if (options?.scene === 1038) {
    const { appId, extraData } = options.referrerInfo as any
    if (appId == 'wx1cf2708c2de46337') { // 电子签 上链公证签小程序APPID
      uni.setStorageSync('esignFaceReturnInfo', options)
    }
  }
}
</script>
<style lang="scss">
@import 'static/css/index.scss';
</style>
