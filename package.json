{"name": "uni-preset-vue", "version": "0.0.1", "scripts": {"dev": "npm run dev:mp-weixin", "build": "npm run build:mp-weixin", "dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "tsc": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint --ext .vue --ext .js --ext .ts src/ --fix", "style": "stylelint \"src/**/*.(vue|scss|css)\" --fix", "postinstall": "husky install", "add": "node ./auto/addPage.ts", "cz": "git add . && cz", "tag": "npm version patch && git push && git push origin --tags"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020420240722002", "@dcloudio/uni-app-harmony": "3.0.0-4020420240722002", "@dcloudio/uni-app-plus": "3.0.0-4020420240722002", "@dcloudio/uni-components": "3.0.0-4020420240722002", "@dcloudio/uni-h5": "3.0.0-4020420240722002", "@dcloudio/uni-mp-alipay": "3.0.0-4020420240722002", "@dcloudio/uni-mp-baidu": "3.0.0-4020420240722002", "@dcloudio/uni-mp-jd": "3.0.0-4020420240722002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020420240722002", "@dcloudio/uni-mp-lark": "3.0.0-4020420240722002", "@dcloudio/uni-mp-qq": "3.0.0-4020420240722002", "@dcloudio/uni-mp-toutiao": "3.0.0-4020420240722002", "@dcloudio/uni-mp-weixin": "3.0.0-4020420240722002", "@dcloudio/uni-mp-xhs": "3.0.0-4020420240722002", "@dcloudio/uni-quickapp-webview": "3.0.0-4020420240722002", "@dcloudio/uni-ui": "^1.5.6", "@icon-park/vue-next": "^1.4.1", "auto-import-types": "^0.0.4", "crypto-js": "^4.2.0", "full-icu": "^1.5.0", "lodash": "^4.17.21", "pinia": "2.0.13", "pinia-plugin-persist-uni": "1.1.1", "sass": "1.49.9", "tiny-pinyin": "^1.3.2", "vue": "3.4.34", "vue-i18n": "9.13.1", "vuex": "^4.0.2", "z-paging": "^2.3.2"}, "devDependencies": {"@commitlint/config-conventional": "16.2.1", "@dcloudio/types": "3.4.12", "@dcloudio/uni-automator": "3.0.0-4020420240722002", "@dcloudio/uni-cli-shared": "3.0.0-4020420240722002", "@dcloudio/uni-stacktracey": "3.0.0-4020420240722002", "@dcloudio/vite-plugin-uni": "3.0.0-4020420240722002", "@typescript-eslint/eslint-plugin": "4.15.1", "@typescript-eslint/parser": "4.15.1", "@vue/eslint-config-typescript": "7.0.0", "@vue/runtime-core": "3.4.34", "autoprefixer": "10.4.4", "commitizen": "^1.0.4", "commitlint": "16.2.3", "cz-customizable": "6.3.0", "eslint": "7.32.0", "eslint-config-prettier": "8.3.0", "eslint-config-taro": "3.3.15", "eslint-config-vue-global-api": "^0.4.1", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.0.0", "husky": "7.0.4", "normalize-path": "^3.0.0", "picocolors": "^1.0.0", "postcss-html": "1.3.0", "postcss-scss": "4.0.3", "stylelint": "14.1.0", "stylelint-config-standard": "24.0.0", "stylelint-order": "5.0.0", "stylelint-scss": "4.0.1", "typescript": "4.6.3", "unplugin-auto-import": "0.8.8", "vite": "5.2.8", "vue-global-api": "^0.4.1", "vue-tsc": "0.29.3"}, "overrides": {"@dcloudio/vite-plugin-uni": {"@vitejs/plugin-vue": "~5.0.4"}}}