<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script>
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />'
      )
    </script>
    <title></title>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app">
      <style>
        .loading-wrap {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 90vh;
        }
        .dot {
          display: flex;
          flex-wrap: wrap;
          width: 36px;
          height: 36px;
          transform: rotate(45deg);
          animation: ant-rotate 1.2s infinite linear;
        }
        .dot i {
          margin: 2px;
          border-radius: 50%;
          width: 14px;
          height: 14px;
          background-color: #1890ff;
          opacity: 0.3;
          animation: ant-spin-move 1s infinite linear alternate;
        }
        .dot i:nth-child(2) {
          animation-delay: 0.4s;
        }
        .dot i:nth-child(3) {
          animation-delay: 0.8s;
        }
        .dot i:nth-child(4) {
          animation-delay: 1.2s;
        }
        @keyframes ant-rotate {
          to {
            transform: rotate(405deg);
          }
        }
        @keyframes ant-spin-move {
          to {
            opacity: 1;
          }
        }
      </style>
      <div class="loading-wrap">
        <div class="dot"><i></i><i></i><i></i><i></i></div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
