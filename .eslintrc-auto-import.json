{"globals": {"EffectScope": "readonly", "acceptHMRUpdate": "readonly", "computed": "readonly", "createApp": "readonly", "createPinia": "readonly", "customRef": "readonly", "defineAsyncComponent": "readonly", "defineComponent": "readonly", "defineStore": "readonly", "effectScope": "readonly", "getActivePinia": "readonly", "getCurrentInstance": "readonly", "getCurrentScope": "readonly", "h": "readonly", "inject": "readonly", "isProxy": "readonly", "isReactive": "readonly", "isReadonly": "readonly", "isRef": "readonly", "mapActions": "readonly", "mapGetters": "readonly", "mapState": "readonly", "mapStores": "readonly", "mapWritableState": "readonly", "markRaw": "readonly", "nextTick": "readonly", "onActivated": "readonly", "onAddToFavorites": "readonly", "onBackPress": "readonly", "onBeforeMount": "readonly", "onBeforeUnmount": "readonly", "onBeforeUpdate": "readonly", "onDeactivated": "readonly", "onError": "readonly", "onErrorCaptured": "readonly", "onHide": "readonly", "onLaunch": "readonly", "onLoad": "readonly", "onMounted": "readonly", "onNavigationBarButtonTap": "readonly", "onNavigationBarSearchInputChanged": "readonly", "onNavigationBarSearchInputClicked": "readonly", "onNavigationBarSearchInputConfirmed": "readonly", "onNavigationBarSearchInputFocusChanged": "readonly", "onPageNotFound": "readonly", "onPageScroll": "readonly", "onPullDownRefresh": "readonly", "onReachBottom": "readonly", "onReady": "readonly", "onRenderTracked": "readonly", "onRenderTriggered": "readonly", "onResize": "readonly", "onScopeDispose": "readonly", "onServerPrefetch": "readonly", "onShareAppMessage": "readonly", "onShareTimeline": "readonly", "onShow": "readonly", "onTabItemTap": "readonly", "onThemeChange": "readonly", "onUnhandledRejection": "readonly", "onUnload": "readonly", "onUnmounted": "readonly", "onUpdated": "readonly", "provide": "readonly", "reactive": "readonly", "readonly": "readonly", "ref": "readonly", "resolveComponent": "readonly", "setActivePinia": "readonly", "setMapStoreSuffix": "readonly", "shallowReactive": "readonly", "shallowReadonly": "readonly", "shallowRef": "readonly", "storeToRefs": "readonly", "toRaw": "readonly", "toRef": "readonly", "toRefs": "readonly", "triggerRef": "readonly", "unref": "readonly", "useAttrs": "readonly", "useCssModule": "readonly", "useCssVars": "readonly", "useSlots": "readonly", "watch": "readonly", "watchEffect": "readonly", "watchPostEffect": "readonly", "watchSyncEffect": "readonly"}}